#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Canara Bank 银行对账单PDF解析器
基于BOI银行解析器的成功模式，采用混合解析策略处理Canara银行PDF
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class CanaraBankPDFParser:
    """Canara银行PDF解析器 - 混合解析策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Date', 'Particulars', 'Deposits', 'Withdrawals', 'Balance'
        ]
        
    def parse_canara_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析Canara银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"Canara银行PDF混合解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：使用PyPDF提取完整文本数据
            print("\n🔄 第一步：使用PyPDF提取完整文本数据...")
            pypdf_data = self._extract_pypdf_descriptions(pdf_path)

            if not pypdf_data:
                print("❌ PyPDF提取失败")
                return pd.DataFrame()

            # 第二步：从PyPDF数据构建DataFrame
            print("\n🔄 第二步：从PyPDF数据构建DataFrame...")
            df_from_pypdf = self._build_dataframe_from_pypdf(pypdf_data)

            if df_from_pypdf.empty:
                print("❌ 从PyPDF数据构建DataFrame失败")
                return pd.DataFrame()

            # 第三步：尝试使用Tabula提取表格结构作为补充
            print("\n🔄 第三步：尝试使用Tabula提取表格结构作为补充...")
            df_structured = self._extract_tabula_structure(pdf_path)

            # 第四步：合并数据（如果Tabula有效）
            if not df_structured.empty:
                print("\n🔄 第四步：合并Tabula和PyPDF数据...")
                df_final = self._merge_and_fix_data(df_structured, pypdf_data)
            else:
                print("\n🔄 第四步：使用PyPDF数据作为主要来源...")
                df_final = df_from_pypdf

            # 第五步：数据验证和清理
            print("\n🔄 第五步：数据验证和清理...")
            df_final = self._validate_and_clean_data(df_final)
            
            print(f"\n✅ Canara银行PDF解析完成！提取交易数: {len(df_final)} 条")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_tabula_structure(self, pdf_path: str) -> pd.DataFrame:
        """使用Tabula提取表格结构和金额数据"""
        try:
            # 先尝试stream模式
            print("  🔄 尝试stream模式...")
            dfs_stream = tabula.read_pdf(pdf_path, pages='all', stream=True)
            print(f"  📋 Stream模式找到 {len(dfs_stream)} 个表格")

            if dfs_stream:
                # 处理stream模式的结果
                all_transactions = []
                for i, df in enumerate(dfs_stream):
                    print(f"  📄 处理Stream表格 {i+1}: 形状 {df.shape}")
                    if not df.empty:
                        df_cleaned = self._clean_tabula_table(df, i+1)
                        if not df_cleaned.empty:
                            all_transactions.append(df_cleaned)
                            print(f"    ✅ 提取到 {len(df_cleaned)} 条有效交易")

                if all_transactions:
                    df_combined = pd.concat(all_transactions, ignore_index=True)
                    print(f"  ✅ Stream模式合并后总计 {len(df_combined)} 条交易")
                    return self._standardize_columns(df_combined)

            # 如果stream模式失败，尝试lattice模式
            print("  🔄 Stream模式无效，尝试lattice模式...")
            dfs = tabula.read_pdf(pdf_path, pages='all', lattice=True)
            print(f"  📋 Lattice模式找到 {len(dfs)} 个表格")

            if not dfs:
                print("  ❌ 未找到任何表格")
                return pd.DataFrame()

            # 合并所有页面的表格
            all_transactions = []

            for i, df in enumerate(dfs):
                if i < 5:  # 只显示前5个表格的详细信息
                    print(f"  📄 处理表格 {i+1}: 形状 {df.shape}")

                # 清理表格数据
                df_cleaned = self._clean_tabula_table(df, i+1)

                if not df_cleaned.empty:
                    all_transactions.append(df_cleaned)
                    if i < 5:
                        print(f"    ✅ 提取到 {len(df_cleaned)} 条有效交易")

            if not all_transactions:
                print("  ❌ 所有表格都没有有效数据")
                return pd.DataFrame()

            # 合并所有交易数据
            df_combined = pd.concat(all_transactions, ignore_index=True)
            print(f"  ✅ 合并后总计 {len(df_combined)} 条交易")

            # 标准化列名
            df_combined = self._standardize_columns(df_combined)

            return df_combined

        except Exception as e:
            print(f"  ❌ Tabula提取失败: {e}")
            return pd.DataFrame()

    def _clean_tabula_table(self, df: pd.DataFrame, table_num: int) -> pd.DataFrame:
        """清理单个tabula表格"""
        if df.empty:
            return pd.DataFrame()

        # 查找并移除表头行
        header_row_idx = None
        for i, row in df.iterrows():
            row_str = ' '.join(str(val) for val in row.values if pd.notna(val))
            if 'Date' in row_str and 'Particulars' in row_str and 'Balance' in row_str:
                header_row_idx = i
                break

        if header_row_idx is not None:
            # 移除表头行及其之前的所有行
            df = df.iloc[header_row_idx + 1:].copy()
            print(f"    🧹 移除表头行 {header_row_idx} 及之前的行")

        # 移除空行和无效行
        df = df.dropna(how='all')

        # 过滤有效的交易行（必须有Date和至少一个金额）
        valid_rows = []
        for i, row in df.iterrows():
            # 检查第一列是否为日期
            date_val = row.iloc[0] if len(row) > 0 else None
            
            # 检查是否有金额数据
            has_amount = False
            for j in range(2, min(5, len(row))):  # 检查Deposits, Withdrawals, Balance列
                if pd.notna(row.iloc[j]) and str(row.iloc[j]).strip():
                    has_amount = True
                    break

            if (pd.notna(date_val) and self._is_valid_date(date_val) and has_amount):
                valid_rows.append(row)

        if valid_rows:
            df_valid = pd.DataFrame(valid_rows)
            df_valid.reset_index(drop=True, inplace=True)
            return df_valid
        else:
            return pd.DataFrame()

    def _is_valid_date(self, value) -> bool:
        """检查是否为有效的日期格式"""
        if pd.isna(value):
            return False

        date_str = str(value).strip()
        # Canara银行日期格式: DD-MM-YYYY
        return bool(re.match(r'\d{2}-\d{2}-\d{4}', date_str))

    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        if df.empty:
            return df

        # 确保有5列
        while len(df.columns) < 5:
            df[f'Col_{len(df.columns)}'] = None

        # 设置标准列名
        df.columns = self.expected_columns

        return df
    
    def _extract_pypdf_descriptions(self, pdf_path: str) -> List[Dict]:
        """使用PyPDF提取完整的描述文本"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                print(f"  📖 PyPDF读取 {len(reader.pages)} 页")

                all_descriptions = []

                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    text = page.extract_text()

                    print(f"  📄 处理第 {page_num + 1} 页...")
                    page_descriptions = self._parse_page_text(text, page_num + 1)

                    if page_descriptions:
                        all_descriptions.extend(page_descriptions)
                        print(f"    ✅ 提取到 {len(page_descriptions)} 条描述")
                    else:
                        print(f"    ⚠️ 第 {page_num + 1} 页没有找到有效描述")

                print(f"  ✅ PyPDF总计提取 {len(all_descriptions)} 条完整描述")
                return all_descriptions

        except Exception as e:
            print(f"  ❌ PyPDF提取失败: {e}")
            return []

    def _parse_page_text(self, text: str, page_num: int) -> List[Dict]:
        """解析单页文本，提取交易描述"""
        descriptions = []
        lines = text.split('\n')

        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if not line:
                i += 1
                continue

            # Canara银行格式：日期单独在一行
            if self._is_date_line(line):
                date = line
                particulars_parts = []
                amounts = []
                j = i + 1

                # 收集后续行直到下一个日期或结束
                while j < len(lines):
                    next_line = lines[j].strip()
                    if not next_line:
                        j += 1
                        continue

                    # 如果遇到下一个日期，停止
                    if self._is_date_line(next_line):
                        break

                    # 如果是表头或非交易内容，停止
                    if self._is_non_transaction_line(next_line):
                        break

                    # 检查是否包含金额（包括负号）
                    line_amounts = re.findall(r'-?[\d,]+\.\d{2}', next_line)
                    if line_amounts:
                        amounts.extend(line_amounts)
                        # 移除金额后的文本也可能是描述的一部分
                        text_part = next_line
                        for amount in line_amounts:
                            text_part = text_part.replace(amount, '').strip()
                        # 清理剩余的负号和空格
                        text_part = re.sub(r'\s*-\s*$', '', text_part).strip()
                        if text_part and not re.match(r'^[Chq:\s]*$', text_part):
                            particulars_parts.append(text_part)
                    else:
                        # 纯文本行，可能是描述
                        if not re.match(r'^[Chq:\s]*$', next_line):
                            particulars_parts.append(next_line)

                    j += 1

                # 构建完整的交易记录
                if particulars_parts or amounts:
                    complete_particulars = ' '.join(particulars_parts).strip()

                    # 解析金额
                    deposits, withdrawals, balance = self._parse_amounts_from_list(amounts, complete_particulars)

                    transaction = {
                        'date': date,
                        'particulars': complete_particulars,
                        'deposits': deposits,
                        'withdrawals': withdrawals,
                        'balance': balance
                    }
                    descriptions.append(transaction)

                i = j  # 跳到下一个未处理的行
            else:
                i += 1

        return descriptions

    def _is_date_line(self, line: str) -> bool:
        """检查是否为日期行"""
        if not line:
            return False

        # Canara银行日期格式: DD-MM-YYYY，通常单独在一行
        line_stripped = line.strip()
        return bool(re.match(r'^\d{1,2}-\d{1,2}-\d{4}$', line_stripped))

    def _parse_amounts_from_list(self, amounts: List[str], description: str = "") -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """从金额列表中解析存款、取款和余额"""
        if not amounts:
            return None, None, None

        deposits = None
        withdrawals = None
        balance = None

        # 解析金额，保留原始字符串信息以判断正负
        parsed_amounts = []

        for amount_str in amounts:
            parsed = self._parse_amount(amount_str)
            if parsed is not None:
                parsed_amounts.append(parsed)

        if len(parsed_amounts) == 1:
            # 只有一个金额，通常是余额
            balance = parsed_amounts[0]
        elif len(parsed_amounts) == 2:
            # Canara银行格式：第一个是交易金额（正数），第二个是余额（可能为负）
            transaction_amount = abs(parsed_amounts[0])  # 交易金额总是正数
            balance = parsed_amounts[1]  # 余额保持原始正负（包括负号）

            # 判断交易类型：根据描述内容
            description_upper = description.upper()

            # 明确的借记/取款标识
            debit_keywords = ['DEBIT', 'DR', 'WITHDRAWAL', 'WITHDRAW', 'TRANSFER DEBIT', 'NEFT DR', 'IMPS DR']
            # 明确的贷记/存款标识
            credit_keywords = ['CREDIT', 'CR', 'DEPOSIT', 'UPI/CR', 'CASH DEPOSIT']

            is_debit = any(keyword in description_upper for keyword in debit_keywords)
            is_credit = any(keyword in description_upper for keyword in credit_keywords)

            if is_debit:
                withdrawals = transaction_amount
            elif is_credit:
                deposits = transaction_amount
            else:
                # 如果没有明确标识，根据常见交易类型判断
                if any(keyword in description_upper for keyword in ['UPI', 'NEFT', 'IMPS', 'TRANSFER']):
                    # UPI、NEFT等通常是转账，需要进一步判断
                    if 'UPI/CR' in description_upper:
                        deposits = transaction_amount
                    else:
                        # 默认为取款（转出）
                        withdrawals = transaction_amount
                elif any(keyword in description_upper for keyword in ['CHARGES', 'FEE', 'GODOWN']):
                    # 费用类通常是取款
                    withdrawals = transaction_amount
                else:
                    # 其他情况，根据余额变化趋势判断（这里简化处理）
                    withdrawals = transaction_amount

        elif len(parsed_amounts) >= 3:
            # 多个金额，最后一个通常是余额
            balance = parsed_amounts[-1]
            transaction_amount = abs(parsed_amounts[0])

            # 根据描述判断交易类型
            description_upper = description.upper()
            if any(keyword in description_upper for keyword in ['DEBIT', 'DR', 'WITHDRAWAL']):
                withdrawals = transaction_amount
            else:
                deposits = transaction_amount

        return deposits, withdrawals, balance

    def _build_dataframe_from_pypdf(self, pypdf_data: List[Dict]) -> pd.DataFrame:
        """从PyPDF提取的数据构建DataFrame"""
        if not pypdf_data:
            return pd.DataFrame()

        print(f"  🔨 从 {len(pypdf_data)} 条PyPDF数据构建DataFrame...")

        # 转换为DataFrame格式
        transactions = []
        for item in pypdf_data:
            transaction = {
                'Date': item['date'],
                'Particulars': item['particulars'],
                'Deposits': item.get('deposits'),
                'Withdrawals': item.get('withdrawals'),
                'Balance': item.get('balance')
            }
            transactions.append(transaction)

        df = pd.DataFrame(transactions)
        print(f"  ✅ 成功构建包含 {len(df)} 条记录的DataFrame")

        return df

    def _extract_amounts_from_description(self, description: str) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """从描述文本中提取金额信息"""
        if not description:
            return None, None, None

        # 查找所有金额模式
        amounts = re.findall(r'[\d,]+\.\d{2}', description)

        deposits = None
        withdrawals = None
        balance = None

        if amounts:
            # 根据金额数量和上下文判断类型
            if len(amounts) == 1:
                # 只有一个金额，可能是余额
                balance = self._parse_amount(amounts[0])
            elif len(amounts) == 2:
                # 两个金额，通常是 [取款/存款] + 余额
                amount1 = self._parse_amount(amounts[0])
                balance = self._parse_amount(amounts[1])

                # 判断第一个金额是存款还是取款
                # 如果余额减少，通常是取款；如果余额增加，通常是存款
                if balance and amount1:
                    if balance < 0:  # 负余额通常表示取款
                        withdrawals = amount1
                    else:
                        # 需要更复杂的逻辑来判断，暂时根据描述内容
                        if any(keyword in description.upper() for keyword in ['DEBIT', 'DR', 'WITHDRAWAL']):
                            withdrawals = amount1
                        else:
                            deposits = amount1
            elif len(amounts) >= 3:
                # 三个或更多金额，通常是 存款 + 取款 + 余额
                deposits = self._parse_amount(amounts[0]) if amounts[0] else None
                withdrawals = self._parse_amount(amounts[1]) if amounts[1] else None
                balance = self._parse_amount(amounts[-1])  # 最后一个通常是余额

        return deposits, withdrawals, balance

    def _match_transaction_line(self, line: str) -> Optional[Dict]:
        """匹配交易行，提取日期和完整描述"""
        # Canara银行交易行模式：日期 描述 [金额信息]
        # 例如: "12-05-2023 GODOWN INSP NON PRIORITY Chq: 0 295.00 -295.00"

        # 正则模式：日期(DD-MM-YYYY) + 空格 + 描述
        pattern = r'^(\d{2}-\d{2}-\d{4})\s+(.+)$'
        match = re.match(pattern, line)

        if not match:
            return None

        date = match.group(1)
        rest_content = match.group(2)

        # 提取完整描述（移除末尾的金额部分）
        particulars = self._extract_description_from_content(rest_content)

        if particulars:
            return {
                'date': date,
                'particulars': particulars
            }

        return None

    def _extract_description_from_content(self, content: str) -> str:
        """从内容中提取描述部分，移除金额"""
        if not content:
            return ""

        # 更保守的金额移除策略，避免截断描述文本
        # 找到所有金额模式的位置
        amount_pattern = r'\s+[\d,]+\.\d{2}(?=\s|$)'  # 确保金额后面是空格或结尾
        amounts = list(re.finditer(amount_pattern, content))

        if amounts:
            # 分析金额的位置和上下文
            # 通常最后1-2个数字是金额，但要小心不要截断描述

            # 如果有多个金额，检查它们是否真的是金额而不是描述的一部分
            valid_amounts = []
            for amount_match in amounts:
                amount_text = amount_match.group().strip()
                amount_start = amount_match.start()

                # 检查金额前后的上下文
                before_text = content[:amount_start].strip()
                after_text = content[amount_match.end():].strip()

                # 如果金额后面还有很多文本，可能不是真正的金额
                if len(after_text) > 20:  # 金额后面不应该有太多文本
                    continue

                # 如果金额看起来合理（大于0，格式正确）
                try:
                    amount_value = float(amount_text.replace(',', ''))
                    if amount_value > 0:
                        valid_amounts.append(amount_match)
                except:
                    continue

            # 只移除确认的金额
            if valid_amounts:
                if len(valid_amounts) >= 2:
                    # 移除最后两个有效金额
                    last_amount_start = valid_amounts[-2].start()
                    content = content[:last_amount_start]
                elif len(valid_amounts) == 1:
                    # 只有一个有效金额，移除它
                    last_amount_start = valid_amounts[-1].start()
                    content = content[:last_amount_start]

        # 清理换行符和多余的空格
        content = self._clean_description_text(content)

        # 如果描述太短，可能解析有误
        if len(content) < 3:
            return ""

        return content

    def _is_transaction_start_line(self, line: str) -> bool:
        """检查是否为交易开始行"""
        # 交易行模式：日期(DD-MM-YYYY) + 空格 + 描述
        pattern = r'^\d{2}-\d{2}-\d{4}\s+'
        return bool(re.match(pattern, line))

    def _is_non_transaction_line(self, line: str) -> bool:
        """检查是否为非交易行（表头、页脚等）"""
        non_transaction_keywords = [
            'Date', 'Particulars', 'Deposits', 'Withdrawals', 'Balance',
            'CANARA BANK', 'Statement', 'Page', 'Total', 'Opening Balance',
            'Customer Id', 'Branch Code', 'IFSC Code'
        ]

        line_upper = line.upper()
        for keyword in non_transaction_keywords:
            if keyword.upper() in line_upper:
                return True

        return False

    def _is_description_continuation(self, line: str, current_description: str) -> bool:
        """检查是否为描述的延续行"""
        line_stripped = line.strip()

        # 如果行中包含金额模式，可能不是纯描述延续
        if re.search(r'[\d,]+\.\d{2}', line):
            return False

        # 如果行太短，可能不是有效的延续
        if len(line_stripped) < 1:
            return False

        # 特殊情况：单独的短词（如"Chq:", "NEFT", "IMPS"等）
        # 这些通常是描述的延续
        if len(line_stripped) <= 5 and re.match(r'^[A-Z:]+$', line_stripped):
            return True

        # 如果行包含典型的交易描述内容，认为是延续
        description_indicators = [
            'NEFT', 'IMPS', 'UPI', 'RTGS', 'FUNDS TRANSFER', 'DEBIT', 'CREDIT',
            'AGRASEN', 'ENTERPRISES', 'KUMAR', 'JAISWAL'  # 常见的人名或机构名
        ]

        line_upper = line_stripped.upper()
        for indicator in description_indicators:
            if indicator in line_upper:
                return True

        # 如果行看起来是名字或地址的一部分，认为是延续
        if re.match(r'^[A-Z\s]+$', line_stripped):
            return True

        # 检查是否为常见的描述延续词
        continuation_words = ['CHQ', 'REF', 'TO', 'FROM', 'FOR', 'AND', 'OR']
        if line_stripped.upper() in continuation_words:
            return True

        return False

    def _clean_description_text(self, text: str) -> str:
        """清理描述文本，处理换行符和格式问题"""
        if not text:
            return ""

        # 移除换行符，用空格替换
        text = re.sub(r'\n+', ' ', text)

        # 移除回车符
        text = re.sub(r'\r+', ' ', text)

        # 移除制表符
        text = re.sub(r'\t+', ' ', text)

        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text)

        # 移除首尾空格
        text = text.strip()

        return text

    def _build_description_lookup(self, pypdf_data: List[Dict]) -> Dict[Tuple[str, str], str]:
        """构建描述查找字典，键为(date, particulars_start)"""
        lookup = {}

        for item in pypdf_data:
            # 使用日期和描述的前20个字符作为键
            particulars_start = item['particulars'][:20] if item['particulars'] else ""
            key = (item['date'], particulars_start)
            lookup[key] = item['particulars']

        return lookup

    def _merge_and_fix_data(self, df_structured: pd.DataFrame, pypdf_data: List[Dict]) -> pd.DataFrame:
        """合并tabula和pypdf数据，修复Particulars列"""
        if df_structured.empty or not pypdf_data:
            print("  ⚠️ 无法合并：缺少tabula或pypdf数据")
            return df_structured

        print(f"  🔗 合并数据：tabula {len(df_structured)} 行，pypdf {len(pypdf_data)} 条描述")

        # 构建描述查找字典
        description_lookup = self._build_description_lookup(pypdf_data)

        # 统计匹配情况
        matched_count = 0
        improved_count = 0

        # 遍历tabula数据，替换Particulars
        for idx, row in df_structured.iterrows():
            try:
                # 获取日期和描述作为匹配键
                date = str(row['Date']).strip()
                old_particulars = str(row['Particulars']) if pd.notna(row['Particulars']) else ""
                particulars_start = old_particulars[:20]

                if not date:
                    continue

                # 查找匹配的完整描述
                key = (date, particulars_start)
                if key in description_lookup:
                    new_particulars = description_lookup[key]

                    # 只有当新描述更完整时才替换
                    if len(new_particulars) > len(old_particulars):
                        # 清理描述文本格式
                        cleaned_particulars = self._clean_description_text(new_particulars)
                        df_structured.at[idx, 'Particulars'] = cleaned_particulars
                        improved_count += 1

                    matched_count += 1

            except Exception as e:
                print(f"    ⚠️ 处理第 {idx} 行时出错: {e}")
                continue

        print(f"  ✅ 匹配成功: {matched_count}/{len(df_structured)} 行")
        print(f"  📈 描述改进: {improved_count} 行")

        return df_structured

    def _validate_and_clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清理最终数据"""
        if df.empty:
            return df

        print("  🧹 清洗金额数据...")

        # 清洗金额列
        df = self._clean_amount_columns(df)

        # 清洗日期列
        df = self._clean_date_column(df)

        # 验证数据完整性
        df = self._validate_data_integrity(df)

        return df

    def _clean_amount_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗金额列数据"""
        amount_columns = ['Deposits', 'Withdrawals', 'Balance']

        for col in amount_columns:
            if col in df.columns:
                print(f"    💰 清洗 {col} 列...")
                df[col] = df[col].apply(self._parse_amount)

        return df

    def _parse_amount(self, value) -> Optional[float]:
        """解析金额字符串为浮点数"""
        if pd.isna(value) or value == '' or str(value).strip() == '':
            return None

        try:
            # 转换为字符串并清理
            amount_str = str(value).strip()

            # 移除货币符号和空格
            amount_str = re.sub(r'[₹\s]', '', amount_str)

            # 移除逗号分隔符
            amount_str = re.sub(r',', '', amount_str)

            # 检查是否为负数
            is_negative = amount_str.startswith('-') or ('(' in amount_str and ')' in amount_str)

            # 移除括号（如果有）
            amount_str = re.sub(r'[()]', '', amount_str)

            # 只保留数字、小数点和负号
            amount_str = re.sub(r'[^\d.-]', '', amount_str)

            if not amount_str or amount_str == '-':
                return None

            # 转换为浮点数
            amount = float(amount_str)

            # 如果原始字符串有括号表示负数，但没有负号，则设为负数
            if is_negative and amount > 0 and not amount_str.startswith('-'):
                amount = -amount

            return amount

        except (ValueError, TypeError):
            return None

    def _clean_date_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗日期列"""
        if 'Date' in df.columns:
            print("    📅 清洗日期列...")
            df['Date'] = df['Date'].apply(self._parse_date)

        return df

    def _parse_date(self, value) -> str:
        """解析日期字符串"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()

        # Canara银行日期格式: DD-MM-YYYY
        if re.match(r'\d{2}-\d{2}-\d{4}', date_str):
            return date_str

        # 尝试其他常见格式
        try:
            # 尝试解析各种日期格式
            for fmt in ['%d-%m-%Y', '%d/%m/%Y', '%Y-%m-%d']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%d-%m-%Y')  # 统一为Canara格式
                except ValueError:
                    continue
        except:
            pass

        return date_str  # 如果无法解析，返回原值

    def _validate_data_integrity(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据完整性"""
        print("    ✅ 验证数据完整性...")

        # 移除没有日期的行
        before_count = len(df)
        df = df[df['Date'].notna() & (df['Date'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效日期的记录")

        # 确保至少有一个金额列有值
        df = df[
            df['Deposits'].notna() |
            df['Withdrawals'].notna() |
            df['Balance'].notna()
        ]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "canara_extracted") -> Tuple[str, str, str]:
        """
        保存解析结果为多种格式

        Args:
            df: 解析后的DataFrame
            output_base: 输出文件名前缀

        Returns:
            Tuple[str, str, str]: CSV、JSON、Excel文件路径
        """
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 确保当前目录可写（不需要创建子目录）
        pass

        # 在保存前进行最终的文本清理
        df_clean = df.copy()
        if 'Particulars' in df_clean.columns:
            df_clean['Particulars'] = df_clean['Particulars'].apply(
                lambda x: self._clean_description_text(str(x)) if pd.notna(x) else ""
            )

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df_clean.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df_clean.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df_clean.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 Canara银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 金额统计
        withdrawal_count = df['Withdrawals'].notna().sum()
        deposit_count = df['Deposits'].notna().sum()

        print(f"取款交易: {withdrawal_count} 笔")
        print(f"存款交易: {deposit_count} 笔")

        # 计算金额总计
        total_withdrawals = df['Withdrawals'].sum()
        total_deposits = df['Deposits'].sum()

        print(f"\n💰 金额统计:")
        print(f"  总取款金额: ₹{total_withdrawals:,.2f}")
        print(f"  总存款金额: ₹{total_deposits:,.2f}")
        print(f"  净变化: ₹{total_deposits - total_withdrawals:,.2f}")

        # 数据完整性检查
        missing_dates = df['Date'].isna().sum()
        missing_particulars = df['Particulars'].isna().sum()
        missing_balances = df['Balance'].isna().sum()

        print(f"\n📋 数据完整性:")
        print(f"  缺失日期: {missing_dates} 条")
        print(f"  缺失描述: {missing_particulars} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额连续性检查
        self._check_balance_continuity(df)

        # 日期范围
        if not df['Date'].isna().all():
            valid_dates = df[df['Date'].notna() & (df['Date'] != '')]
            if not valid_dates.empty:
                print(f"\n📅 日期范围:")
                print(f"  最早交易: {valid_dates['Date'].iloc[0]}")
                print(f"  最晚交易: {valid_dates['Date'].iloc[-1]}")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            withdrawal = f"₹{row['Withdrawals']:,.2f}" if pd.notna(row['Withdrawals']) else "-"
            deposit = f"₹{row['Deposits']:,.2f}" if pd.notna(row['Deposits']) else "-"
            balance = f"₹{row['Balance']:,.2f}" if pd.notna(row['Balance']) else "-"

            print(f"  {i+1}. {row['Date']} | {str(row['Particulars'])[:30]}...")
            print(f"     取款: {withdrawal} | 存款: {deposit} | 余额: {balance}")

    def _check_balance_continuity(self, df: pd.DataFrame) -> None:
        """检查余额连续性"""
        print(f"\n🔍 余额连续性检查:")

        balance_col = 'Balance'
        if balance_col not in df.columns:
            print("  ⚠️ 没有余额列")
            return

        # 检查余额是否连续
        valid_balances = df[df[balance_col].notna()]

        if len(valid_balances) < 2:
            print("  ⚠️ 余额数据不足，无法检查连续性")
            return

        # 简单检查：余额应该是递增或递减的趋势
        balance_changes = 0
        for i in range(1, len(valid_balances)):
            prev_balance = valid_balances.iloc[i-1][balance_col]
            curr_balance = valid_balances.iloc[i][balance_col]

            if abs(curr_balance - prev_balance) > 0.01:  # 忽略小数点误差
                balance_changes += 1

        print(f"  余额变化次数: {balance_changes}")

        # 检查首末余额
        first_balance = valid_balances.iloc[0][balance_col]
        last_balance = valid_balances.iloc[-1][balance_col]

        print(f"  期初余额: ₹{first_balance:,.2f}")
        print(f"  期末余额: ₹{last_balance:,.2f}")
        print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")


def main():
    """主函数"""
    parser = CanaraBankPDFParser()

    pdf_path = "../files/5-canara-*********-canara-statement-2024-06-28-12-06-08-708598.pdf"

    print("🚀 启动Canara银行PDF混合解析器")

    # 解析PDF
    df = parser.parse_canara_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 Canara银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ Canara银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
