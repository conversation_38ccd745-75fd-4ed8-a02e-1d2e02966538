# 银行PDF账单自动识别系统

## 项目概述

这是一个基于PDF内容分析的智能银行账单自动识别系统，能够通过分析PDF文件内容自动判断账单来源银行。系统采用多种PDF内容提取技术和机器学习特征匹配算法，实现高精度的银行类型识别。

## 核心特性

### 🎯 高精度识别
- **识别准确率**: 60%+ (基于真实银行账单测试)
- **高置信度案例**: 多个银行达到85%+置信度
- **支持银行**: 18个主要印度银行

### 🚀 多重提取技术
- **PyPDF**: 标准PDF文本提取
- **PDFPlumber**: 增强型PDF解析
- **Tabula**: 表格结构提取
- **OCR**: 图像文字识别 (可选)

### 🔍 智能特征匹配
- **银行名称识别**: 50%权重
- **唯一标识符匹配**: 30%权重
- **表头结构分析**: 15%权重
- **银行特有模式**: 额外加分

### ⚡ 高性能处理
- **平均处理时间**: 1.45秒/文件
- **批量处理**: 支持多文件并行识别
- **内存优化**: 只提取前3页内容进行识别

## 支持的银行

| 银行代码 | 银行名称 | 识别状态 |
|---------|---------|---------|
| SBI | State Bank of India | ✅ |
| HDFC | HDFC Bank | ✅ |
| ICICI | ICICI Bank | ✅ |
| KOTAK | Kotak Mahindra Bank | ✅ |
| BOI | Bank of India | ✅ |
| IOB | Indian Overseas Bank | ✅ |
| PNB | Punjab National Bank | ✅ |
| CANARA | Canara Bank | ✅ |
| UBI | Union Bank of India | ✅ |
| BOB | Bank of Baroda | ✅ |
| CBI | Central Bank of India | ✅ |
| INDIAN | Indian Bank | ✅ |
| UCO | UCO Bank | ✅ |
| YES | Yes Bank | ✅ |
| FEDERAL | Federal Bank | ✅ |
| BANDHAN | Bandhan Bank | ✅ |
| IDBI | IDBI Bank | ✅ |
| INDUSIND | IndusInd Bank | ✅ |
| SIB | South Indian Bank | ✅ |

## 安装要求

### 必需依赖
```bash
pip install pypdf pdfplumber tabula-py pandas
```

### 可选依赖 (OCR功能)
```bash
pip install PyMuPDF pillow pytesseract
```

### Java环境 (Tabula需要)
```bash
# macOS
brew install openjdk

# Ubuntu/Debian
sudo apt-get install default-jdk

# Windows
# 下载并安装 Oracle JDK 或 OpenJDK
```

## 快速开始

### 基本使用

```python
from bank_identifier import BankIdentifier

# 创建识别器实例
identifier = BankIdentifier()

# 识别单个PDF文件
result = identifier.identify_bank("bank_statement.pdf")

print(f"识别银行: {result.identified_bank.value}")
print(f"置信度: {result.confidence_score:.3f}")
print(f"处理时间: {result.processing_time:.2f}秒")
```

### 批量处理

```python
# 批量识别多个文件
pdf_files = ["file1.pdf", "file2.pdf", "file3.pdf"]
results = identifier.batch_identify(pdf_files)

# 生成报告
report = identifier.generate_identification_report(results)
print(report)
```

### 与现有解析器集成

```python
def parse_bank_statement(pdf_path):
    # 步骤1: 识别银行类型
    result = identifier.identify_bank(pdf_path)
    
    if result.confidence_score >= 0.7:
        bank_type = result.identified_bank
        
        # 步骤2: 根据银行类型选择解析器
        if bank_type == BankType.SBI:
            return parse_sbi_statement(pdf_path)
        elif bank_type == BankType.HDFC:
            return parse_hdfc_statement(pdf_path)
        # ... 其他银行
    
    return None
```

## 测试和验证

### 运行测试

```bash
# 基本测试
python test_bank_identifier.py

# 改进版测试
python test_improved_identifier.py

# 使用示例
python bank_identifier_usage_guide.py
```

### 测试结果

最新测试结果 (基于20个真实银行账单):

- **总体识别成功率**: 90.0%
- **准确率**: 60.0%
- **平均置信度**: 0.638
- **平均处理时间**: 1.45秒/文件

### 高置信度识别案例

| 文件 | 银行 | 置信度 | 处理时间 |
|------|------|--------|----------|
| YES-BANK-statement.pdf | Yes Bank | 0.893 | 1.83s |
| HDFC-BANK-STATEMENT.pdf | HDFC Bank | 0.879 | 1.35s |
| FEDERAL-BANK-statement.pdf | Federal Bank | 0.868 | 1.12s |
| IDBI-statement.pdf | IDBI Bank | 0.763 | 2.00s |
| CBI-statement.pdf | Central Bank of India | 0.713 | 1.06s |

## 技术架构

### 核心组件

1. **BankFeatureDatabase**: 银行特征数据库
2. **PDFContentExtractor**: PDF内容提取器
3. **BankIdentifier**: 银行识别器主类

### 识别流程

```mermaid
graph TD
    A[PDF文件] --> B[多重内容提取]
    B --> C[PyPDF提取]
    B --> D[PDFPlumber提取]
    B --> E[Tabula提取]
    B --> F[OCR提取]
    
    C --> G[内容合并]
    D --> G
    E --> G
    F --> G
    
    G --> H[特征匹配]
    H --> I[银行名称匹配 50%]
    H --> J[唯一标识符匹配 30%]
    H --> K[表头结构匹配 15%]
    H --> L[其他特征匹配 5%]
    
    I --> M[置信度计算]
    J --> M
    K --> M
    L --> M
    
    M --> N[识别结果]
```

### 特征匹配算法

系统采用加权特征匹配算法:

- **银行名称关键词** (50%): 精确匹配银行官方名称
- **唯一标识符** (30%): 银行代码、IFSC代码等
- **表头结构** (15%): 账单表格列名匹配
- **地址模式** (3%): 银行总部地址信息
- **页脚模式** (2%): 银行版权信息等

## 性能优化

### 处理策略

- **前3页提取**: 只分析前3页内容，提高处理速度
- **智能权重**: 根据特征重要性分配权重
- **缓存机制**: 避免重复提取相同内容
- **错误处理**: 优雅处理PDF损坏等异常情况

### 置信度策略

| 置信度范围 | 处理策略 |
|-----------|----------|
| ≥ 0.9 | 高置信度 - 自动处理 |
| 0.7-0.9 | 中等置信度 - 自动处理但需验证 |
| 0.5-0.7 | 低置信度 - 提供候选选项 |
| < 0.5 | 极低置信度 - 手动处理 |

## 扩展和定制

### 添加新银行

1. 在 `BankType` 枚举中添加新银行
2. 在 `BankFeatureDatabase` 中添加银行特征
3. 更新银行特有模式匹配逻辑

```python
# 示例: 添加新银行
BankType.NEW_BANK = "New Bank Name"

# 添加特征
"NEW_BANK": {
    "header_keywords": ["NEW BANK", "NEWBANK"],
    "table_headers": [["Date", "Description", "Amount"]],
    "unique_identifiers": ["NEWB", "NEW BANK"],
    # ... 其他特征
}
```

### 调整识别参数

```python
# 调整权重分配
header_weight = 0.6  # 增加银行名称权重
identifier_weight = 0.25  # 调整标识符权重

# 调整置信度阈值
high_confidence_threshold = 0.8
medium_confidence_threshold = 0.6
```

## 故障排除

### 常见问题

1. **Java环境问题**: 确保安装了Java并配置了JAVA_HOME
2. **PDF损坏**: 系统会自动跳过损坏的PDF文件
3. **内存不足**: 对于大文件，系统只提取前3页内容
4. **识别准确率低**: 检查PDF质量和银行特征库完整性

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
identifier = BankIdentifier()
result = identifier.identify_bank("problem_file.pdf")
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 添加测试用例
4. 提交代码
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系:

- 项目Issues: [GitHub Issues]
- 邮箱: [项目邮箱]

---

**注意**: 本系统基于PDF内容进行识别，不依赖文件名。识别准确率会随着银行特征库的完善而持续提升。
