#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用银行PDF解析框架
基于项目经验总结，提供可配置、可扩展的银行PDF解析解决方案
"""

import os
import pandas as pd
import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import pdfplumber
import tabula
import pypdf


class ParseStrategy(Enum):
    """解析策略枚举"""
    PDFPLUMBER = "pdfplumber"
    TABULA = "tabula"
    PYPDF = "pypdf"
    OCR = "ocr"
    HYBRID = "hybrid"
    AUTO = "auto"


@dataclass
class BankConfig:
    """银行配置类"""
    bank_name: str
    primary_strategy: ParseStrategy
    backup_strategy: ParseStrategy
    date_format: str
    amount_pattern: str
    quality_threshold: float
    column_mapping: Dict[str, str]
    validation_rules: Dict[str, Any]
    
    @classmethod
    def load_from_file(cls, config_path: str) -> 'BankConfig':
        """从配置文件加载银行配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return cls(
            bank_name=config_data['bank_name'],
            primary_strategy=ParseStrategy(config_data['primary_strategy']),
            backup_strategy=ParseStrategy(config_data['backup_strategy']),
            date_format=config_data['date_format'],
            amount_pattern=config_data['amount_pattern'],
            quality_threshold=config_data['quality_threshold'],
            column_mapping=config_data['column_mapping'],
            validation_rules=config_data['validation_rules']
        )


@dataclass
class ParseResult:
    """解析结果类"""
    transactions: List[Dict[str, Any]]
    quality_score: float
    strategy_used: ParseStrategy
    processing_time: float
    error_count: int
    warnings: List[str]


class ParseStrategyInterface(ABC):
    """解析策略接口"""
    
    @abstractmethod
    def parse(self, pdf_path: str, config: BankConfig) -> ParseResult:
        """解析PDF文件"""
        pass
    
    @abstractmethod
    def validate(self, result: ParseResult, config: BankConfig) -> bool:
        """验证解析结果"""
        pass


class PDFPlumberStrategy(ParseStrategyInterface):
    """pdfplumber解析策略"""
    
    def parse(self, pdf_path: str, config: BankConfig) -> ParseResult:
        """使用pdfplumber解析PDF"""
        import time
        start_time = time.time()
        
        transactions = []
        warnings = []
        error_count = 0
        
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        # 提取表格
                        tables = page.extract_tables()
                        
                        for table in tables:
                            if table:
                                page_transactions = self._process_table(table, config)
                                transactions.extend(page_transactions)
                                
                    except Exception as e:
                        error_count += 1
                        warnings.append(f"第{page_num}页处理失败: {str(e)}")
                        
        except Exception as e:
            error_count += 1
            warnings.append(f"PDF打开失败: {str(e)}")
        
        processing_time = time.time() - start_time
        quality_score = self._calculate_quality_score(transactions, config)
        
        return ParseResult(
            transactions=transactions,
            quality_score=quality_score,
            strategy_used=ParseStrategy.PDFPLUMBER,
            processing_time=processing_time,
            error_count=error_count,
            warnings=warnings
        )
    
    def _process_table(self, table: List[List], config: BankConfig) -> List[Dict]:
        """处理表格数据"""
        transactions = []
        
        # 跳过表头
        for row in table[1:]:
            if row and any(cell for cell in row if cell):
                transaction = self._parse_row(row, config)
                if transaction:
                    transactions.append(transaction)
        
        return transactions
    
    def _parse_row(self, row: List, config: BankConfig) -> Optional[Dict]:
        """解析表格行"""
        try:
            # 根据列映射解析数据
            transaction = {}
            
            for i, cell in enumerate(row):
                if i < len(config.column_mapping):
                    column_name = list(config.column_mapping.keys())[i]
                    transaction[column_name] = str(cell).strip() if cell else ""
            
            return transaction if transaction else None
            
        except Exception:
            return None
    
    def _calculate_quality_score(self, transactions: List[Dict], config: BankConfig) -> float:
        """计算质量分数"""
        if not transactions:
            return 0.0
        
        total_score = 0
        for transaction in transactions:
            score = 0
            
            # 检查必需字段
            required_fields = ['Date', 'Description', 'Amount', 'Balance']
            for field in required_fields:
                if transaction.get(field):
                    score += 1
            
            total_score += score / len(required_fields)
        
        return total_score / len(transactions)
    
    def validate(self, result: ParseResult, config: BankConfig) -> bool:
        """验证解析结果"""
        return result.quality_score >= config.quality_threshold


class TabulaStrategy(ParseStrategyInterface):
    """Tabula解析策略"""
    
    def parse(self, pdf_path: str, config: BankConfig) -> ParseResult:
        """使用Tabula解析PDF"""
        import time
        start_time = time.time()
        
        transactions = []
        warnings = []
        error_count = 0
        
        try:
            # 尝试不同的Tabula参数
            param_sets = [
                {"lattice": True, "pages": "all"},
                {"stream": True, "pages": "all"},
                {"lattice": True, "pages": "all", "multiple_tables": True}
            ]
            
            for params in param_sets:
                try:
                    dfs = tabula.read_pdf(pdf_path, **params)
                    
                    for df in dfs:
                        if not df.empty:
                            df_transactions = self._process_dataframe(df, config)
                            transactions.extend(df_transactions)
                    
                    if transactions:
                        break  # 如果成功提取到数据，停止尝试其他参数
                        
                except Exception as e:
                    error_count += 1
                    warnings.append(f"Tabula参数 {params} 失败: {str(e)}")
                    
        except Exception as e:
            error_count += 1
            warnings.append(f"Tabula解析失败: {str(e)}")
        
        processing_time = time.time() - start_time
        quality_score = self._calculate_quality_score(transactions, config)
        
        return ParseResult(
            transactions=transactions,
            quality_score=quality_score,
            strategy_used=ParseStrategy.TABULA,
            processing_time=processing_time,
            error_count=error_count,
            warnings=warnings
        )
    
    def _process_dataframe(self, df: pd.DataFrame, config: BankConfig) -> List[Dict]:
        """处理DataFrame数据"""
        transactions = []
        
        for _, row in df.iterrows():
            transaction = {}
            
            # 根据列映射转换数据
            for original_col, standard_col in config.column_mapping.items():
                if original_col in df.columns:
                    value = row[original_col]
                    transaction[standard_col] = str(value).strip() if pd.notna(value) else ""
            
            if transaction:
                transactions.append(transaction)
        
        return transactions
    
    def _calculate_quality_score(self, transactions: List[Dict], config: BankConfig) -> float:
        """计算质量分数"""
        if not transactions:
            return 0.0
        
        # 简化的质量评分
        valid_count = 0
        for transaction in transactions:
            if (transaction.get('Date') and 
                transaction.get('Description') and 
                (transaction.get('Credit') or transaction.get('Debit'))):
                valid_count += 1
        
        return valid_count / len(transactions)
    
    def validate(self, result: ParseResult, config: BankConfig) -> bool:
        """验证解析结果"""
        return result.quality_score >= config.quality_threshold


class HybridStrategy(ParseStrategyInterface):
    """混合解析策略"""
    
    def __init__(self):
        self.strategies = {
            ParseStrategy.PDFPLUMBER: PDFPlumberStrategy(),
            ParseStrategy.TABULA: TabulaStrategy()
        }
    
    def parse(self, pdf_path: str, config: BankConfig) -> ParseResult:
        """使用混合策略解析PDF"""
        results = []
        
        # 尝试所有策略
        for strategy_name, strategy in self.strategies.items():
            try:
                result = strategy.parse(pdf_path, config)
                result.strategy_used = strategy_name
                results.append(result)
            except Exception as e:
                logging.warning(f"策略 {strategy_name} 失败: {e}")
        
        # 选择最佳结果
        if results:
            best_result = max(results, key=lambda r: r.quality_score)
            best_result.strategy_used = ParseStrategy.HYBRID
            return best_result
        else:
            # 返回空结果
            return ParseResult(
                transactions=[],
                quality_score=0.0,
                strategy_used=ParseStrategy.HYBRID,
                processing_time=0.0,
                error_count=1,
                warnings=["所有策略都失败了"]
            )
    
    def validate(self, result: ParseResult, config: BankConfig) -> bool:
        """验证解析结果"""
        return result.quality_score >= config.quality_threshold


class UniversalBankPDFParser:
    """通用银行PDF解析器"""
    
    def __init__(self):
        self.strategies = {
            ParseStrategy.PDFPLUMBER: PDFPlumberStrategy(),
            ParseStrategy.TABULA: TabulaStrategy(),
            ParseStrategy.HYBRID: HybridStrategy()
        }
        
        self.logger = self._setup_logging()
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('UniversalBankParser')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def parse_pdf(self, pdf_path: str, config: BankConfig) -> ParseResult:
        """解析PDF文件"""
        self.logger.info(f"开始解析 {config.bank_name} 银行PDF: {pdf_path}")
        
        # 选择解析策略
        strategy = self._select_strategy(pdf_path, config)
        
        # 执行解析
        result = strategy.parse(pdf_path, config)
        
        # 验证结果
        if not strategy.validate(result, config):
            self.logger.warning(f"主策略质量不达标，尝试备选策略")
            
            # 尝试备选策略
            backup_strategy = self.strategies.get(config.backup_strategy)
            if backup_strategy:
                backup_result = backup_strategy.parse(pdf_path, config)
                if backup_strategy.validate(backup_result, config):
                    result = backup_result
        
        self.logger.info(f"解析完成，提取 {len(result.transactions)} 条记录，质量分数: {result.quality_score:.3f}")
        
        return result
    
    def _select_strategy(self, pdf_path: str, config: BankConfig) -> ParseStrategyInterface:
        """选择解析策略"""
        if config.primary_strategy == ParseStrategy.AUTO:
            # 自动选择策略
            return self._auto_select_strategy(pdf_path)
        else:
            return self.strategies[config.primary_strategy]
    
    def _auto_select_strategy(self, pdf_path: str) -> ParseStrategyInterface:
        """自动选择最适合的策略"""
        # 简化的自动选择逻辑
        # 实际应用中可以基于PDF特征分析
        return self.strategies[ParseStrategy.HYBRID]
    
    def save_results(self, result: ParseResult, output_base: str) -> Tuple[str, str, str]:
        """保存解析结果"""
        if not result.transactions:
            self.logger.warning("没有数据可保存")
            return "", "", ""
        
        df = pd.DataFrame(result.transactions)
        
        # 保存为不同格式
        csv_file = f"{output_base}.csv"
        json_file = f"{output_base}.json"
        excel_file = f"{output_base}.xlsx"
        
        df.to_csv(csv_file, index=False, encoding='utf-8')
        df.to_json(json_file, orient='records', indent=2, force_ascii=False)
        df.to_excel(excel_file, index=False, engine='openpyxl')
        
        self.logger.info(f"结果已保存: CSV({csv_file}), JSON({json_file}), Excel({excel_file})")
        
        return csv_file, json_file, excel_file


def create_sample_config(bank_name: str) -> BankConfig:
    """创建示例配置"""
    return BankConfig(
        bank_name=bank_name,
        primary_strategy=ParseStrategy.HYBRID,
        backup_strategy=ParseStrategy.PDFPLUMBER,
        date_format="%d-%m-%Y",
        amount_pattern=r"[\d,]+\.\d{2}",
        quality_threshold=0.90,
        column_mapping={
            "Date": "Transaction Date",
            "Description": "Description", 
            "Credit": "Credit",
            "Debit": "Debit",
            "Balance": "Balance"
        },
        validation_rules={
            "min_transactions": 1,
            "required_fields": ["Date", "Description", "Balance"],
            "balance_continuity": True
        }
    )


def main():
    """示例用法"""
    # 创建解析器
    parser = UniversalBankPDFParser()
    
    # 创建配置
    config = create_sample_config("SAMPLE_BANK")
    
    # 解析PDF
    pdf_path = "sample_bank_statement.pdf"
    
    if os.path.exists(pdf_path):
        result = parser.parse_pdf(pdf_path, config)
        
        # 保存结果
        parser.save_results(result, "sample_bank_extracted")
        
        print(f"解析完成！")
        print(f"提取记录数: {len(result.transactions)}")
        print(f"质量分数: {result.quality_score:.3f}")
        print(f"使用策略: {result.strategy_used.value}")
        print(f"处理时间: {result.processing_time:.2f}秒")
    else:
        print(f"PDF文件不存在: {pdf_path}")


if __name__ == "__main__":
    main()
