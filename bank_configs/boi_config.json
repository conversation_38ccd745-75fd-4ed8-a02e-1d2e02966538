{"bank_name": "Bank of India", "bank_code": "BOI", "primary_strategy": "hybrid", "backup_strategy": "pdfplumber", "date_format": "%d-%m-%Y", "amount_pattern": "[\\d,]+\\.\\d{2}", "quality_threshold": 0.98, "column_mapping": {"Txn Date": "Transaction Date", "Value Date": "Value Date", "Description": "Description", "Ref No./Cheque No.": "Cheque Number", "Debit": "Debit", "Credit": "Credit", "Balance": "Balance"}, "validation_rules": {"min_transactions": 1, "required_fields": ["Transaction Date", "Description", "Balance"], "balance_continuity": true, "amount_validation": true, "description_completeness": true}, "parsing_parameters": {"tabula_settings": {"lattice": true, "stream": false, "multiple_tables": true, "pages": "all"}, "pypdf_settings": {"extract_text": true, "preserve_layout": true}, "hybrid_strategy": {"tabula_for_structure": true, "pypdf_for_description": true, "merge_algorithm": "intelligent"}}, "output_format": {"standard_columns": ["Serial No", "Transaction Date", "Value Date", "Description", "Cheque Number", "Credit", "Debit", "Balance"], "date_output_format": "%d-%m-%Y", "amount_decimal_places": 2}, "quality_metrics": {"target_accuracy": 100, "field_completeness": 100, "balance_match": 100, "description_accuracy": 100}, "success_indicators": {"solved_description_column_issue": true, "achieved_100_percent_accuracy": true, "production_ready": true}}