{"bank_name": "Kotak Mahindra Bank", "bank_code": "KOTAK", "primary_strategy": "ocr", "backup_strategy": "pdfplumber", "date_format": "%d-%m-%Y", "amount_pattern": "[\\d,]+\\.\\d{2}", "quality_threshold": 1.0, "column_mapping": {"Date": "Transaction Date", "Narration": "Description", "Chq/Ref No": "Cheque Number", "Withdrawal(Dr)/Deposit(Cr)": "Amount", "Balance": "Balance"}, "validation_rules": {"min_transactions": 1, "required_fields": ["Date", "Description", "Balance"], "balance_continuity": true, "amount_validation": true, "date_sequence": true}, "parsing_parameters": {"ocr_engine": "tesseract", "ocr_config": "--psm 6", "image_preprocessing": true, "balance_validation": {"opening_balance": 678.3, "closing_balance": 9089.43, "total_withdrawals": 294596.61, "total_deposits": 303007.74}}, "output_format": {"standard_columns": ["Serial No", "Transaction Date", "Value Date", "Description", "Cheque Number", "Credit", "Debit", "Balance"], "date_output_format": "%d-%m-%Y", "amount_decimal_places": 2}, "quality_metrics": {"target_accuracy": 100, "field_completeness": 100, "balance_match": 100, "transaction_count": 1148}}