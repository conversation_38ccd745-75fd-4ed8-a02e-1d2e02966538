#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证BOI银行PDF解析结果
参考ICICI验证脚本实现，提供详细的对比分析和质量评估
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple


class BOIResultValidator:
    """BOI银行解析结果验证器"""
    
    def __init__(self):
        self.reference_file = "boi/boi_check.csv"
        self.result_file = "boi/boi_extracted.csv"
        
    def validate_results(self) -> bool:
        """验证解析结果"""
        print(f"\n{'='*80}")
        print("BOI银行PDF解析结果验证")
        print(f"{'='*80}")
        
        # 加载数据
        try:
            ref_df = pd.read_csv(self.reference_file)
            result_df = pd.read_csv(self.result_file)
            
            print(f"✅ 数据加载成功")
            print(f"  参考文件: {len(ref_df)} 行")
            print(f"  解析结果: {len(result_df)} 行")
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
        
        # 验证结构
        structure_valid = self._validate_structure(ref_df, result_df)
        
        # 验证交易统计
        transaction_valid = self._validate_transactions(ref_df, result_df)
        
        # 计算字段匹配度
        field_matches = self._calculate_field_matches(ref_df, result_df)
        
        # 生成详细差异报告
        self._generate_difference_report(ref_df, result_df)
        
        # 计算总体匹配度
        overall_score = self._calculate_overall_score(field_matches, structure_valid, transaction_valid)
        
        # 最终评估
        return self._final_assessment(overall_score, field_matches)
    
    def _validate_structure(self, ref_df: pd.DataFrame, result_df: pd.DataFrame) -> bool:
        """验证数据结构"""
        row_match = len(ref_df) == len(result_df)
        col_match = list(ref_df.columns) == list(result_df.columns)
        
        print(f"\n📊 结构验证:")
        print(f"  行数匹配: {'✅' if row_match else '❌'} ({len(result_df)}/{len(ref_df)})")
        print(f"  列名匹配: {'✅' if col_match else '❌'}")
        
        if not col_match:
            print(f"    参考列名: {list(ref_df.columns)}")
            print(f"    结果列名: {list(result_df.columns)}")
        
        return row_match and col_match
    
    def _validate_transactions(self, ref_df: pd.DataFrame, result_df: pd.DataFrame) -> bool:
        """验证交易统计"""
        # 取款交易统计
        ref_withdrawals = (ref_df['Withdrawal(in Rs.)'].notna() & 
                          (ref_df['Withdrawal(in Rs.)'] != "")).sum()
        result_withdrawals = (result_df['Withdrawal(in Rs.)'].notna() & 
                             (result_df['Withdrawal(in Rs.)'] != "")).sum()
        
        # 存款交易统计
        ref_deposits = (ref_df['Deposits(in Rs.)'].notna() & 
                       (ref_df['Deposits(in Rs.)'] != "")).sum()
        result_deposits = (result_df['Deposits(in Rs.)'].notna() & 
                          (result_df['Deposits(in Rs.)'] != "")).sum()
        
        # 金额总计
        ref_total_w = ref_df['Withdrawal(in Rs.)'].fillna(0).sum()
        result_total_w = result_df['Withdrawal(in Rs.)'].fillna(0).sum()
        ref_total_d = ref_df['Deposits(in Rs.)'].fillna(0).sum()
        result_total_d = result_df['Deposits(in Rs.)'].fillna(0).sum()
        
        withdrawal_match = ref_withdrawals == result_withdrawals
        deposit_match = ref_deposits == result_deposits
        amount_w_match = abs(ref_total_w - result_total_w) < 0.01
        amount_d_match = abs(ref_total_d - result_total_d) < 0.01
        
        print(f"\n💰 交易统计验证:")
        print(f"  取款交易数: {'✅' if withdrawal_match else '❌'} ({result_withdrawals}/{ref_withdrawals})")
        print(f"  存款交易数: {'✅' if deposit_match else '❌'} ({result_deposits}/{ref_deposits})")
        print(f"  取款总额: {'✅' if amount_w_match else '❌'} (₹{result_total_w:,.2f}/₹{ref_total_w:,.2f})")
        print(f"  存款总额: {'✅' if amount_d_match else '❌'} (₹{result_total_d:,.2f}/₹{ref_total_d:,.2f})")
        
        return withdrawal_match and deposit_match and amount_w_match and amount_d_match
    
    def _calculate_field_matches(self, ref_df: pd.DataFrame, result_df: pd.DataFrame) -> Dict[str, float]:
        """计算各字段匹配度 - 增强精确度"""
        total_rows = min(len(ref_df), len(result_df))
        if total_rows == 0:
            return {}

        matches = {
            'Sl No': 0,
            'Txn Date': 0,
            'Description': 0,
            'Cheque No': 0,
            'Withdrawal(in Rs.)': 0,
            'Deposits(in Rs.)': 0,
            'Balance(in Rs.)': 0
        }

        # 记录不匹配的详细信息
        mismatches = {field: [] for field in matches.keys()}

        for i in range(total_rows):
            ref_row = ref_df.iloc[i]
            result_row = result_df.iloc[i]

            # Sl No匹配 - 严格比较
            ref_sl = self._normalize_sl_no(ref_row['Sl No'])
            result_sl = self._normalize_sl_no(result_row['Sl No'])
            if ref_sl == result_sl:
                matches['Sl No'] += 1
            else:
                mismatches['Sl No'].append((i+1, ref_sl, result_sl))

            # 日期匹配 - 严格字符串比较
            ref_date = self._strict_normalize_text(ref_row['Txn Date'])
            result_date = self._strict_normalize_text(result_row['Txn Date'])
            if ref_date == result_date:
                matches['Txn Date'] += 1
            else:
                mismatches['Txn Date'].append((i+1, ref_date, result_date))

            # 描述匹配 - 逐字符比较
            ref_desc = self._strict_normalize_text(ref_row['Description'])
            result_desc = self._strict_normalize_text(result_row['Description'])
            if ref_desc == result_desc:
                matches['Description'] += 1
            else:
                mismatches['Description'].append((i+1, ref_desc[:50], result_desc[:50]))

            # Cheque No匹配 - 严格比较
            ref_cheque = self._strict_normalize_text(ref_row['Cheque No'])
            result_cheque = self._strict_normalize_text(result_row['Cheque No'])
            if ref_cheque == result_cheque:
                matches['Cheque No'] += 1
            else:
                mismatches['Cheque No'].append((i+1, ref_cheque, result_cheque))

            # 金额匹配 - 精确比较
            ref_w = self._strict_normalize_amount(ref_row['Withdrawal(in Rs.)'])
            result_w = self._strict_normalize_amount(result_row['Withdrawal(in Rs.)'])
            if ref_w == result_w:
                matches['Withdrawal(in Rs.)'] += 1
            else:
                mismatches['Withdrawal(in Rs.)'].append((i+1, ref_w, result_w))

            ref_d = self._strict_normalize_amount(ref_row['Deposits(in Rs.)'])
            result_d = self._strict_normalize_amount(result_row['Deposits(in Rs.)'])
            if ref_d == result_d:
                matches['Deposits(in Rs.)'] += 1
            else:
                mismatches['Deposits(in Rs.)'].append((i+1, ref_d, result_d))

            ref_b = self._strict_normalize_amount(ref_row['Balance(in Rs.)'])
            result_b = self._strict_normalize_amount(result_row['Balance(in Rs.)'])
            if ref_b == result_b:
                matches['Balance(in Rs.)'] += 1
            else:
                mismatches['Balance(in Rs.)'].append((i+1, ref_b, result_b))

        # 计算匹配率
        match_rates = {}
        for field, count in matches.items():
            match_rates[field] = (count / total_rows) * 100

        print(f"\n🎯 字段匹配度分析 (严格模式):")
        for field, rate in match_rates.items():
            status = '✅' if rate == 100.0 else '❌'
            print(f"  {field}: {status} {rate:.1f}% ({matches[field]}/{total_rows})")

            # 显示不匹配的详细信息
            if rate < 100.0 and mismatches[field]:
                print(f"    不匹配行: {[m[0] for m in mismatches[field][:5]]}")

        # 存储不匹配信息供详细报告使用
        self.detailed_mismatches = mismatches

        return match_rates
    
    def _normalize_sl_no(self, value) -> int:
        """标准化序列号"""
        if pd.isna(value):
            return 0
        try:
            return int(float(str(value).strip()))
        except:
            return 0
    
    def _normalize_text(self, value) -> str:
        """标准化文本"""
        if pd.isna(value) or value == "":
            return ""
        return str(value).strip()
    
    def _normalize_amount(self, value) -> str:
        """标准化金额"""
        if pd.isna(value) or value == "" or str(value).strip() == "":
            return ""

        try:
            amount_num = float(str(value).replace(',', ''))
            return f"{amount_num:.2f}"
        except:
            return str(value).strip()

    def _strict_normalize_text(self, value) -> str:
        """严格标准化文本 - 逐字符精确比较"""
        if pd.isna(value):
            return ""

        text = str(value)

        # 只移除首尾空格，保持内部格式
        text = text.strip()

        # 处理nan字符串
        if text.lower() == 'nan':
            return ""

        return text

    def _strict_normalize_amount(self, value) -> str:
        """严格标准化金额 - 精确比较"""
        if pd.isna(value):
            return ""

        text = str(value).strip()

        # 处理nan字符串
        if text.lower() == 'nan' or text == '':
            return ""

        try:
            # 移除逗号并转换为浮点数
            amount_num = float(text.replace(',', ''))
            return f"{amount_num:.2f}"
        except:
            return text
    
    def _generate_difference_report(self, ref_df: pd.DataFrame, result_df: pd.DataFrame) -> None:
        """生成详细差异报告 - 增强版"""
        print(f"\n📋 详细差异分析:")

        if not hasattr(self, 'detailed_mismatches'):
            print("  ⚠️ 无详细不匹配信息")
            return

        total_mismatches = 0
        for field, mismatches in self.detailed_mismatches.items():
            if mismatches:
                total_mismatches += len(mismatches)

        if total_mismatches == 0:
            print(f"  ✅ 所有字段完全匹配！")
            return

        print(f"  发现 {total_mismatches} 个字段不匹配:")

        # 按字段显示详细差异
        for field, mismatches in self.detailed_mismatches.items():
            if mismatches:
                print(f"\n  🔍 {field} 字段不匹配 ({len(mismatches)} 行):")

                for i, (row_num, ref_val, result_val) in enumerate(mismatches[:5]):  # 显示前5个
                    print(f"    行{row_num}:")
                    print(f"      参考值: \"{ref_val}\"")
                    print(f"      解析值: \"{result_val}\"")

                    # 字符级差异分析
                    if field in ['Description', 'Cheque No']:
                        self._analyze_character_differences(ref_val, result_val)

                if len(mismatches) > 5:
                    print(f"    ... 还有 {len(mismatches) - 5} 行不匹配")

    def _analyze_character_differences(self, ref_text: str, result_text: str) -> None:
        """分析字符级差异"""
        ref_str = str(ref_text)
        result_str = str(result_text)

        if len(ref_str) != len(result_str):
            print(f"      长度差异: {len(result_str)} vs {len(ref_str)}")

        # 找到第一个不同的字符
        for i, (c1, c2) in enumerate(zip(ref_str, result_str)):
            if c1 != c2:
                print(f"      第{i}位差异: '{c2}' vs '{c1}'")
                break

        # 显示末尾差异
        if len(ref_str) != len(result_str):
            min_len = min(len(ref_str), len(result_str))
            if len(ref_str) > min_len:
                print(f"      参考文件多出: \"{ref_str[min_len:]}\"")
            if len(result_str) > min_len:
                print(f"      解析结果多出: \"{result_str[min_len:]}\"")
    
    def _calculate_overall_score(self, field_matches: Dict[str, float], 
                               structure_valid: bool, transaction_valid: bool) -> float:
        """计算总体评分"""
        if not field_matches:
            return 0.0
        
        # 字段匹配度权重
        weights = {
            'Sl No': 0.15,
            'Txn Date': 0.15,
            'Description': 0.20,
            'Cheque No': 0.05,
            'Withdrawal(in Rs.)': 0.20,
            'Deposits(in Rs.)': 0.20,
            'Balance(in Rs.)': 0.05
        }
        
        weighted_score = sum(field_matches.get(field, 0) * weight 
                           for field, weight in weights.items())
        
        # 结构和交易统计加分
        if structure_valid:
            weighted_score += 5
        if transaction_valid:
            weighted_score += 5
        
        return min(weighted_score, 100.0)
    
    def _final_assessment(self, overall_score: float, field_matches: Dict[str, float]) -> bool:
        """最终评估 - 严格模式，只有100%匹配才算成功"""
        print(f"\n🏆 总体评分: {overall_score:.1f}%")

        # 检查是否所有字段都是100%匹配
        all_perfect = all(rate == 100.0 for rate in field_matches.values())

        if all_perfect and overall_score == 100.0:
            grade = "A+ (完美)"
            status = True
            print(f"  质量等级: {grade}")
            print(f"\n🎉 验证结果: 100%完美匹配！")
            print(f"✅ 解析器已准备就绪，可用于生产环境")
        else:
            # 任何不是100%的都需要改进
            if overall_score >= 95:
                grade = "A- (接近完美，但仍需改进)"
            elif overall_score >= 90:
                grade = "B+ (良好，但不符合100%标准)"
            elif overall_score >= 80:
                grade = "B (及格，但距离目标较远)"
            else:
                grade = "C (需要大幅改进)"

            status = False
            print(f"  质量等级: {grade}")
            print(f"\n❌ 验证结果: 未达到100%匹配标准")
            print(f"📊 必须修复所有不匹配项才能通过验证")

            # 显示需要改进的字段
            imperfect_fields = [field for field, rate in field_matches.items() if rate < 100.0]
            if imperfect_fields:
                print(f"🔧 需要改进的字段: {', '.join(imperfect_fields)}")

        return status


def main():
    """主函数"""
    validator = BOIResultValidator()
    success = validator.validate_results()
    
    print(f"\n{'='*80}")
    if success:
        print(f"🎊 BOI银行PDF解析验证成功！")
    else:
        print(f"⚠️ BOI银行PDF解析需要进一步优化")
    print(f"{'='*80}")
    
    return success


if __name__ == "__main__":
    main()
