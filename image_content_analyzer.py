#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
银行PDF图像内容分析器
专门用于提取和分析PDF文件第一页的图像内容
"""

import os
import io
import time
from typing import List, Dict, Tuple, Optional
from pathlib import Path

# 导入依赖
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
    print("✅ PyMuPDF可用")
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("❌ PyMuPDF不可用")

try:
    from PIL import Image, ImageEnhance, ImageFilter
    PIL_AVAILABLE = True
    print("✅ Pillow可用")
except ImportError:
    PIL_AVAILABLE = False
    print("❌ Pillow不可用")

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
    print("✅ pytesseract可用")
except ImportError:
    TESSERACT_AVAILABLE = False
    print("❌ pytesseract不可用")


class PDFImageContentAnalyzer:
    """
    PDF图像内容分析器
    专门用于提取和分析PDF文件第一页的图像内容
    """
    
    def __init__(self):
        self.max_images_per_file = 20  # 限制每个文件处理的图像数量
        self.min_image_width = 1     # 最小图像宽度
        self.min_image_height = 1    # 最小图像高度
        self.max_image_width = 3500    # 最大图像宽度（避免处理过大图像）
        self.max_image_height = 2000   # 最大图像高度
    
    def extract_first_page_images(self, pdf_path: str) -> List[Dict]:
        """
        从PDF第一页提取图像
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            List[Dict]: 图像信息列表，包含图像对象和位置信息
        """
        images_info = []
        
        if not PYMUPDF_AVAILABLE or not PIL_AVAILABLE:
            print(f"    ⚠️ 图像提取功能不可用，缺少必要依赖")
            return images_info
        
        try:
            doc = fitz.open(pdf_path)
            
            if len(doc) == 0:
                print(f"    ⚠️ PDF文件为空")
                return images_info
            
            # 只处理第一页
            page = doc[0]
            image_list = page.get_images()
            
            print(f"    📷 第一页发现 {len(image_list)} 个图像")
            
            # 获取页面尺寸用于位置计算
            page_rect = page.rect
            page_height = page_rect.height
            
            processed_count = 0
            
            for img_index, img in enumerate(image_list):
                if processed_count >= self.max_images_per_file:
                    print(f"    ⚠️ 达到最大图像处理数量限制 ({self.max_images_per_file})")
                    break
                
                try:
                    # 获取图像数据
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)

                    print(f"    🔍 图像{img_index+1}详细信息: xref={xref}, n={pix.n}, alpha={pix.alpha}, width={pix.width}, height={pix.height}")

                    # 转换为PIL Image
                    color_channels = pix.n - pix.alpha
                    if color_channels <= 4:  # 支持灰度(1)、RGB(3)、CMYK(4)图像
                        try:
                            if color_channels == 4:  # CMYK图像
                                # 先转换为RGB
                                pix_rgb = fitz.Pixmap(fitz.csRGB, pix)
                                img_data = pix_rgb.tobytes("ppm")
                                pix_rgb = None  # 释放临时pixmap
                            else:
                                img_data = pix.tobytes("ppm")

                            pil_image = Image.open(io.BytesIO(img_data))
                            print(f"    ✅ 图像{img_index+1}成功转换为PIL图像 (颜色通道: {color_channels})")
                        except Exception as e:
                            print(f"    ❌ 图像{img_index+1}转换失败: {e}")
                            pix = None
                            continue

                        # 检查图像尺寸
                        width, height = pil_image.size

                        # 过滤不合适的图像
                        if (width < self.min_image_width or
                            height < self.min_image_height or
                            width > self.max_image_width or
                            height > self.max_image_height):
                            print(f"    ⚠️ 图像{img_index+1}尺寸不合适: {width}x{height} (限制: {self.min_image_width}-{self.max_image_width} x {self.min_image_height}-{self.max_image_height})")
                            pix = None
                            continue
                        
                        # 获取图像在页面中的位置
                        img_rects = page.get_image_rects(xref)
                        position_info = "未知位置"
                        is_top_area = False
                        
                        if img_rects:
                            rect = img_rects[0]  # 取第一个位置
                            y_position = rect.y0
                            # 判断是否在页面顶部区域（前1/3）
                            is_top_area = y_position < (page_height / 3)
                            position_info = f"Y位置: {y_position:.1f} ({'顶部区域' if is_top_area else '中下部区域'})"
                        
                        image_info = {
                            'index': img_index + 1,
                            'image': pil_image,
                            'width': width,
                            'height': height,
                            'position': position_info,
                            'is_top_area': is_top_area,
                            'size_mb': len(img_data) / (1024 * 1024)
                        }
                        
                        images_info.append(image_info)
                        processed_count += 1
                        
                        print(f"    ✅ 图像{img_index+1}: {width}x{height}, {position_info}")
                    else:
                        print(f"    ❌ 图像{img_index+1}颜色通道不支持: 有效通道数={color_channels} (n={pix.n}, alpha={pix.alpha})")

                    pix = None  # 释放内存
                    
                except Exception as e:
                    print(f"    ❌ 提取图像{img_index+1}失败: {e}")
                    continue
            
            doc.close()
            
            # 按位置排序：顶部区域的图像优先
            images_info.sort(key=lambda x: (not x['is_top_area'], x['index']))
            
            print(f"    📊 成功提取 {len(images_info)} 个有效图像")
            
        except Exception as e:
            print(f"    ❌ PDF图像提取失败: {e}")
        
        return images_info
    
    def preprocess_image_for_ocr(self, image):
        """
        图像预处理以提高OCR准确率

        Args:
            image: 原始图像

        Returns:
            预处理后的图像
        """
        try:
            # 转换为灰度图像
            if image.mode != 'L':
                image = image.convert('L')
            
            # 调整图像大小（如果太小则放大）
            width, height = image.size
            if width < 200 or height < 100:
                scale_factor = max(200 / width, 100 / height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                if PIL_AVAILABLE:
                    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)
            
            # 增强锐度
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.5)
            
            # 轻微的高斯模糊去噪
            image = image.filter(ImageFilter.GaussianBlur(radius=0.5))
            
            return image
            
        except Exception as e:
            print(f"    ⚠️ 图像预处理失败: {e}")
            return image
    
    def ocr_image_content(self, image, image_index: int) -> str:
        """
        使用OCR识别图像中的文字内容
        
        Args:
            image: PIL图像对象
            image_index: 图像索引
            
        Returns:
            str: 识别的文字内容
        """
        if not TESSERACT_AVAILABLE:
            return "OCR功能不可用"
        
        try:
            # 图像预处理
            processed_image = self.preprocess_image_for_ocr(image)
            
            # 使用tesseract进行OCR，支持英文和印地文
            text = pytesseract.image_to_string(processed_image, lang='eng+hin')
            
            # 清理识别结果
            text = text.strip()
            
            # 移除多余的空白字符
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            cleaned_text = ' '.join(lines)
            
            if cleaned_text:
                return cleaned_text
            else:
                return "未识别到文字内容"
                
        except Exception as e:
            return f"OCR识别失败: {e}"
    
    def analyze_pdf_images(self, pdf_path: str) -> Dict:
        """
        分析单个PDF文件的图像内容
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            Dict: 分析结果
        """
        result = {
            'file_path': pdf_path,
            'file_name': os.path.basename(pdf_path),
            'success': False,
            'error_message': None,
            'images_count': 0,
            'images_analysis': [],
            'processing_time': 0.0
        }
        
        start_time = time.time()
        
        try:
            # 检查文件是否存在
            if not os.path.exists(pdf_path):
                result['error_message'] = "文件不存在"
                return result
            
            # 提取第一页图像
            images_info = self.extract_first_page_images(pdf_path)
            result['images_count'] = len(images_info)
            
            if not images_info:
                result['error_message'] = "未找到有效图像"
                return result
            
            # 对每个图像进行OCR识别
            for img_info in images_info:
                print(f"    🔍 正在识别图像{img_info['index']}...")
                
                ocr_result = self.ocr_image_content(img_info['image'], img_info['index'])
                
                analysis = {
                    'image_index': img_info['index'],
                    'image_size': f"{img_info['width']}x{img_info['height']}",
                    'position': img_info['position'],
                    'is_top_area': img_info['is_top_area'],
                    'file_size_mb': round(img_info['size_mb'], 2),
                    'ocr_result': ocr_result
                }
                
                result['images_analysis'].append(analysis)
                
                # 显示OCR结果预览
                preview = ocr_result[:50] + "..." if len(ocr_result) > 50 else ocr_result
                print(f"    📝 图像{img_info['index']} OCR结果: {preview}")
            
            result['success'] = True
            
        except Exception as e:
            result['error_message'] = f"处理失败: {e}"
        
        finally:
            end_time = time.time()
            result['processing_time'] = end_time - start_time
        
        return result

    def analyze_directory(self, directory_path: str) -> List[Dict]:
        """
        分析目录中所有PDF文件的图像内容

        Args:
            directory_path: 目录路径

        Returns:
            List[Dict]: 所有文件的分析结果
        """
        results = []
        pdf_files = []

        # 查找所有PDF文件
        for file_path in Path(directory_path).glob("*.pdf"):
            pdf_files.append(str(file_path))

        print(f"📁 找到 {len(pdf_files)} 个PDF文件")
        print("=" * 80)

        # 逐个分析
        for i, pdf_path in enumerate(sorted(pdf_files), 1):
            print(f"🔍 [{i}/{len(pdf_files)}] 分析文件: {os.path.basename(pdf_path)}")

            result = self.analyze_pdf_images(pdf_path)
            results.append(result)

            # 显示处理状态
            if result['success']:
                print(f"    ✅ 处理成功: 提取{result['images_count']}个图像")
            else:
                print(f"    ❌ 处理失败: {result['error_message']}")

            print(f"    ⏱️ 处理时间: {result['processing_time']:.2f}秒")
            print("-" * 80)

        return results

    def print_analysis_summary(self, results: List[Dict]):
        """
        打印图像内容分析结果摘要

        Args:
            results: 分析结果列表
        """
        print("\n" + "=" * 80)
        print("🖼️ 银行PDF图像内容分析结果汇总")
        print("=" * 80)

        # 统计信息
        total_files = len(results)
        successful_files = sum(1 for r in results if r['success'])
        failed_files = total_files - successful_files
        total_images = sum(r['images_count'] for r in results)
        total_time = sum(r['processing_time'] for r in results)

        print(f"\n📊 总体统计:")
        print(f"  总文件数: {total_files}")
        print(f"  成功处理: {successful_files}")
        print(f"  处理失败: {failed_files}")
        print(f"  成功率: {(successful_files/total_files*100):.1f}%")
        print(f"  总图像数: {total_images}")
        print(f"  总处理时间: {total_time:.2f}秒")
        print(f"  平均处理时间: {(total_time/total_files):.2f}秒/文件")

        print(f"\n📋 详细分析结果:")
        print("=" * 80)

        for result in results:
            print(f"\n文件名: {result['file_name']}")

            if not result['success']:
                print(f"❌ 处理失败: {result['error_message']}")
                print("---")
                continue

            print(f"提取图像数量: {result['images_count']}个")

            if result['images_count'] == 0:
                print("未找到有效图像")
                print("---")
                continue

            # 显示每个图像的OCR结果
            for analysis in result['images_analysis']:
                print(f"\n图像{analysis['image_index']} OCR结果:")
                print(f"  尺寸: {analysis['image_size']}")
                print(f"  位置: {analysis['position']}")
                print(f"  文件大小: {analysis['file_size_mb']} MB")
                print(f"  识别内容: {analysis['ocr_result']}")

            print("---")

        # 失败文件分析
        if failed_files > 0:
            print(f"\n🔍 处理失败文件分析:")
            for result in results:
                if not result['success']:
                    print(f"  ❌ {result['file_name']}: {result['error_message']}")

        # 图像统计
        if total_images > 0:
            top_area_images = 0
            total_ocr_success = 0

            for result in results:
                if result['success']:
                    for analysis in result['images_analysis']:
                        if analysis['is_top_area']:
                            top_area_images += 1
                        if analysis['ocr_result'] != "未识别到文字内容" and not analysis['ocr_result'].startswith("OCR识别失败"):
                            total_ocr_success += 1

            print(f"\n📈 图像分析统计:")
            print(f"  顶部区域图像: {top_area_images}/{total_images} ({(top_area_images/total_images*100):.1f}%)")
            print(f"  OCR识别成功: {total_ocr_success}/{total_images} ({(total_ocr_success/total_images*100):.1f}%)")

        print(f"\n💡 分析建议:")
        if successful_files == total_files:
            print(f"  ✅ 所有文件处理成功，图像提取功能正常")
        elif successful_files > total_files * 0.8:
            print(f"  🔶 大部分文件处理成功，少数文件可能存在格式问题")
        else:
            print(f"  ⚠️ 较多文件处理失败，建议检查PDF文件格式或依赖库安装")

        if total_images > 0:
            ocr_success_rate = sum(1 for r in results if r['success']
                                 for a in r['images_analysis']
                                 if a['ocr_result'] != "未识别到文字内容" and not a['ocr_result'].startswith("OCR识别失败")) / total_images

            if ocr_success_rate > 0.7:
                print(f"  ✅ OCR识别效果良好")
            elif ocr_success_rate > 0.4:
                print(f"  🔶 OCR识别效果一般，可能需要优化图像预处理")
            else:
                print(f"  ⚠️ OCR识别效果较差，建议检查tesseract配置")


def main():
    """主函数"""
    print("🚀 启动银行PDF图像内容分析器")
    print("🎯 目标目录: bank_files")
    print("🔧 专门分析PDF第一页的图像内容")
    print("=" * 80)

    # 检查依赖可用性
    print("📋 依赖检查:")
    print(f"  PyMuPDF: {'✅' if PYMUPDF_AVAILABLE else '❌'}")
    print(f"  Pillow: {'✅' if PIL_AVAILABLE else '❌'}")
    print(f"  pytesseract: {'✅' if TESSERACT_AVAILABLE else '❌'}")

    if not all([PYMUPDF_AVAILABLE, PIL_AVAILABLE, TESSERACT_AVAILABLE]):
        print("\n❌ 缺少必要依赖，无法继续执行")
        print("请安装: pip install PyMuPDF Pillow pytesseract")
        return

    print("=" * 80)

    # 目标目录
    target_directory = "bank_files"

    if not os.path.exists(target_directory):
        print(f"❌ 目录不存在: {target_directory}")
        return

    # 创建图像内容分析器
    analyzer = PDFImageContentAnalyzer()

    # 分析目录中的所有PDF文件
    results = analyzer.analyze_directory(target_directory)

    # 打印分析结果摘要
    analyzer.print_analysis_summary(results)

    print(f"\n🎉 银行PDF图像内容分析完成！")


if __name__ == "__main__":
    main()
