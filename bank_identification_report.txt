================================================================================
银行PDF账单自动识别报告
================================================================================

📊 总体统计:
  总文件数: 20
  成功识别: 18
  识别成功率: 90.0%

🏦 银行分布:
  State Bank of India: 4 个文件
  Yes Bank: 2 个文件
  Central Bank of India: 2 个文件
  Unknown Bank: 2 个文件
  HDFC Bank: 2 个文件
  Bank of Baroda: 2 个文件
  Union Bank of India: 1 个文件
  IDBI Bank: 1 个文件
  Federal Bank: 1 个文件
  Indian Overseas Bank: 1 个文件
  ICICI Bank: 1 个文件
  IndusInd Bank: 1 个文件

📋 详细识别结果:
================================================================================

文件: 1-sbi-**********-recent-sbi-statement.pdf
识别银行: State Bank of India
置信度: 0.517
处理时间: 1.25秒
提取方法: pypdf, tabula
所有匹配结果:
  - State Bank of India: 0.517
    匹配特征: 银行名称匹配(0.50), 表头结构匹配(1.00), 唯一标识符匹配(0.33)
  - Yes Bank: 0.450
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(1.00), 唯一标识符匹配(0.33)
  - HDFC Bank: 0.333
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.40), 唯一标识符匹配(0.50)
----------------------------------------

文件: 10-boi-*********-BANK-OF-INDIA-2.pdf
识别银行: Union Bank of India
置信度: 0.433
处理时间: 0.56秒
提取方法: pypdf, tabula
所有匹配结果:
  - Union Bank of India: 0.433
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.67), 唯一标识符匹配(0.67)
  - Yes Bank: 0.433
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.67), 唯一标识符匹配(0.67)
  - State Bank of India: 0.417
    匹配特征: 银行名称匹配(0.50), 表头结构匹配(0.60), 唯一标识符匹配(0.33)
----------------------------------------

文件: 11-idbi-*********-idbi-bankin.pdf
识别银行: IDBI Bank
置信度: 0.758
处理时间: 2.00秒
提取方法: pypdf, tabula
所有匹配结果:
  - IDBI Bank: 0.758
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(0.83), 唯一标识符匹配(0.67), 地址模式匹配(1.00), 页脚模式匹配(1.00)
  - IndusInd Bank: 0.758
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(0.83), 唯一标识符匹配(0.67), 地址模式匹配(1.00), 页脚模式匹配(1.00)
  - Yes Bank: 0.500
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(1.00), 唯一标识符匹配(0.33), 地址模式匹配(0.50)
----------------------------------------

文件: 12-federal-*********-AC-STATEMENT-2055XXXXXX9745-01JUL2019-10JUL2019-**************-1.pdf
识别银行: Federal Bank
置信度: 0.733
处理时间: 1.12秒
提取方法: tabula
所有匹配结果:
  - Federal Bank: 0.733
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(0.67), 唯一标识符匹配(1.00), 地址模式匹配(0.50), 页脚模式匹配(1.00)
  - State Bank of India: 0.517
    匹配特征: 银行名称匹配(0.50), 表头结构匹配(1.00), 唯一标识符匹配(0.33)
  - ICICI Bank: 0.481
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.86), 唯一标识符匹配(0.67)
----------------------------------------

文件: 13-bandhan-*********-Statement-Bandhan-Bank.pdf
识别银行: Yes Bank
置信度: 0.750
处理时间: 0.52秒
提取方法: pypdf, tabula
所有匹配结果:
  - Yes Bank: 0.750
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(1.00), 唯一标识符匹配(0.67), 地址模式匹配(0.50), 页脚模式匹配(1.00)
  - Kotak Mahindra Bank: 0.450
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(1.00), 唯一标识符匹配(0.33)
  - IDBI Bank: 0.408
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.83), 唯一标识符匹配(0.33)
----------------------------------------

文件: 14-iob-*********-Statement-***************-2.pdf
识别银行: Indian Overseas Bank
置信度: 0.517
处理时间: 0.44秒
提取方法: pypdf, tabula
所有匹配结果:
  - Indian Overseas Bank: 0.517
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(1.00), 唯一标识符匹配(0.67)
  - Union Bank of India: 0.408
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.83), 唯一标识符匹配(0.33)
  - Yes Bank: 0.408
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.83), 唯一标识符匹配(0.33)
----------------------------------------

文件: 15-cbi-*********-Bank-statement.pdf
识别银行: Central Bank of India
置信度: 0.700
处理时间: 1.08秒
提取方法: pypdf, tabula
所有匹配结果:
  - Central Bank of India: 0.700
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(1.00), 唯一标识符匹配(0.67), 页脚模式匹配(1.00)
  - Bank of India: 0.464
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.86), 唯一标识符匹配(0.33), 页脚模式匹配(1.00)
  - State Bank of India: 0.214
    匹配特征: 表头结构匹配(0.86)
----------------------------------------

文件: 16-indian-*********-SOORAJ-INDIAN-BANK-STATEMENT.pdf
识别银行: Central Bank of India
置信度: 0.517
处理时间: 1.24秒
提取方法: pypdf, tabula
所有匹配结果:
  - Central Bank of India: 0.517
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(1.00), 唯一标识符匹配(0.67)
  - Yes Bank: 0.392
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.50), 唯一标识符匹配(0.67)
  - South Indian Bank: 0.392
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.50), 唯一标识符匹配(0.67)
----------------------------------------

文件: 17-uco-*********-Account-Statement-UCO-Dec23-May24.pdf
识别银行: State Bank of India
置信度: 0.517
处理时间: 1.27秒
提取方法: pypdf, tabula
所有匹配结果:
  - State Bank of India: 0.517
    匹配特征: 银行名称匹配(0.50), 表头结构匹配(1.00), 唯一标识符匹配(0.33)
  - ICICI Bank: 0.517
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(1.00), 唯一标识符匹配(0.67)
  - UCO Bank: 0.517
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(1.00), 唯一标识符匹配(0.67)
----------------------------------------

文件: 18-yes-*********-YES-BANK-1-1-MARCH-2023-TO-4-APRIL-2023.pdf
识别银行: Yes Bank
置信度: 0.775
处理时间: 2.01秒
提取方法: pypdf, tabula
所有匹配结果:
  - Yes Bank: 0.775
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(0.83), 唯一标识符匹配(1.00), 地址模式匹配(0.50), 页脚模式匹配(1.00)
  - ICICI Bank: 0.629
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(0.71), 唯一标识符匹配(0.67), 页脚模式匹配(1.00)
  - Kotak Mahindra Bank: 0.600
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(0.80), 唯一标识符匹配(0.67)
----------------------------------------

文件: 19-sib-*********-BANK-STATEMENT.pdf
识别银行: Unknown Bank
置信度: 0.000
处理时间: 3.60秒
提取方法: pypdf, tabula
错误信息: 无法提取PDF内容
----------------------------------------

文件: 2-hdfc-*********-HDFC-BANK-STATEMENT-pdf.pdf
识别银行: HDFC Bank
置信度: 0.624
处理时间: 1.33秒
提取方法: pypdf, tabula
所有匹配结果:
  - HDFC Bank: 0.624
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(0.43), 唯一标识符匹配(1.00), 地址模式匹配(0.50)
  - Kotak Mahindra Bank: 0.567
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(1.00), 唯一标识符匹配(0.67), 地址模式匹配(0.50)
  - Union Bank of India: 0.525
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.83), 唯一标识符匹配(0.67), 地址模式匹配(0.50)
----------------------------------------

文件: 3-icici-*********-*********-ICICI-BANK-STATEMENT-1-1.pdf
识别银行: HDFC Bank
置信度: 0.617
处理时间: 1.53秒
提取方法: pypdf, tabula
所有匹配结果:
  - HDFC Bank: 0.617
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(1.00), 唯一标识符匹配(0.50)
  - ICICI Bank: 0.481
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.86), 唯一标识符匹配(0.67)
  - State Bank of India: 0.417
    匹配特征: 银行名称匹配(0.25), 表头结构匹配(1.00), 唯一标识符匹配(0.33)
----------------------------------------

文件: 4-kotak-*********-Kotak-Bank-Statement.pdf
识别银行: Unknown Bank
置信度: 0.000
处理时间: 1.53秒
提取方法: pypdf, tabula
错误信息: 无法提取PDF内容
----------------------------------------

文件: 5-canara-*********-canara-statement-2024-06-28-12-06-08-708598.pdf
识别银行: State Bank of India
置信度: 0.517
处理时间: 1.14秒
提取方法: pypdf, tabula
所有匹配结果:
  - State Bank of India: 0.517
    匹配特征: 银行名称匹配(0.50), 表头结构匹配(1.00), 唯一标识符匹配(0.33)
  - HDFC Bank: 0.483
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(1.00), 唯一标识符匹配(0.50)
  - Canara Bank: 0.475
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.83), 唯一标识符匹配(0.67)
----------------------------------------

文件: 6-pnb-*********-PNB-BANK-STATEMENT.pdf
识别银行: ICICI Bank
置信度: 0.445
处理时间: 1.04秒
提取方法: pypdf, tabula
所有匹配结果:
  - ICICI Bank: 0.445
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.71), 唯一标识符匹配(0.67)
  - Yes Bank: 0.392
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.50), 唯一标识符匹配(0.67)
  - Kotak Mahindra Bank: 0.250
    匹配特征: 表头结构匹配(1.00)
----------------------------------------

文件: 7-ubi-*********-UBI-FullStatement-5.pdf
识别银行: State Bank of India
置信度: 0.410
处理时间: 1.13秒
提取方法: pypdf, tabula
所有匹配结果:
  - State Bank of India: 0.410
    匹配特征: 银行名称匹配(0.50), 表头结构匹配(0.57), 唯一标识符匹配(0.33)
  - Union Bank of India: 0.392
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.50), 唯一标识符匹配(0.67)
  - Kotak Mahindra Bank: 0.250
    匹配特征: 表头结构匹配(1.00)
----------------------------------------

文件: 8-bob-*********-BOB-STATEMENT-page-1-2.pdf
识别银行: Bank of Baroda
置信度: 0.475
处理时间: 1.54秒
提取方法: pypdf, tabula
所有匹配结果:
  - Bank of Baroda: 0.475
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.83), 唯一标识符匹配(0.67)
  - State Bank of India: 0.314
    匹配特征: 银行名称匹配(0.25), 表头结构匹配(0.86)
  - ICICI Bank: 0.250
    匹配特征: 表头结构匹配(1.00)
----------------------------------------

文件: 8-bob-*********-BOB-STATEMENT.pdf
识别银行: Bank of Baroda
置信度: 0.475
处理时间: 1.78秒
提取方法: pypdf, tabula
所有匹配结果:
  - Bank of Baroda: 0.475
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.83), 唯一标识符匹配(0.67)
  - State Bank of India: 0.314
    匹配特征: 银行名称匹配(0.25), 表头结构匹配(0.86)
  - ICICI Bank: 0.250
    匹配特征: 表头结构匹配(1.00)
----------------------------------------

文件: 9-IndusInd-*********-Statement.pdf
识别银行: IndusInd Bank
置信度: 0.667
处理时间: 3.35秒
提取方法: pypdf, tabula
所有匹配结果:
  - IndusInd Bank: 0.667
    匹配特征: 银行名称匹配(0.67), 表头结构匹配(0.67), 唯一标识符匹配(0.67), 地址模式匹配(0.50), 页脚模式匹配(1.00)
  - Kotak Mahindra Bank: 0.400
    匹配特征: 银行名称匹配(0.33), 表头结构匹配(0.80), 唯一标识符匹配(0.33)
  - ICICI Bank: 0.214
    匹配特征: 表头结构匹配(0.86)
----------------------------------------

总处理时间: 29.46秒
平均处理时间: 1.47秒/文件