# YES 银行 PDF 账单解析器

## 项目概述

本项目实现了 YES 银行 PDF 账单的通用自动解析功能，基于表格结构特征的智能识别，支持任意页数的 YES 银行账单文件，能够将 PDF 格式的银行对账单转换为结构化数据（CSV、JSON、Excel 格式）。

## 通用架构设计

### 核心特性

1. **通用性**: 支持任意页数的 YES 银行账单文件
2. **智能识别**: 基于表格结构特征自动识别交易表格
3. **数据保真**: 完全保留 PDF 中的原始表格结构和格式
4. **格式自适应**: 自动处理不同页面的表格格式变化
5. **可扩展性**: 易于扩展支持 YES 银行的其他账单格式

### 表格类型识别

系统能够自动识别以下表格类型：

1. **交易表格**: `TRANSACTION_TABLE` - 包含 7 列的标准交易记录表格
2. **表头信息**: `HEADER_INFO` - 账单头部信息
3. **账户信息**: `ACCOUNT_INFO` - 账户详细信息

### 解析策略

采用**基于表格结构特征的智能识别架构**：

1. **动态页面检测**: 自动获取 PDF 页数，支持任意页数的账单文件
2. **智能表格分析**: 基于表格结构特征自动识别表格类型
3. **优化提取策略**: 优先使用 lattice 模式（YES 银行表格结构清晰）
4. **多表格处理**: 自动处理单页面内的多个表格

## 数据结构

### 原始列结构（完全保留）

YES 银行 PDF 账单包含以下 7 列标准结构：

1. **Transaction Date** - 交易日期
2. **Value Date** - 起息日期
3. **Cheque No/Reference No** - 支票号/参考号
4. **Description** - 交易描述
5. **Withdrawals** - 提取金额
6. **Deposits** - 存入金额
7. **Running Balance** - 账户余额

### 数据格式特点

-   **日期格式**: DD MMM YYYY (如: 03 Apr 2023)
-   **金额格式**: 带逗号分隔的数字 (如: 97,350.00)
-   **参考号格式**: 完全保留原始格式，包括前导零 (如: 000000028422)
-   **描述格式**: 详细的交易描述，包含银行代码、参考号等信息

### 数据格式保真性

解析器特别注重保持原始数据格式的完整性：

1. **前导零保护**: Cheque No/Reference No 列中的前导零被完全保留
    - 示例: 000000028422, 000000001058, 000000001057
2. **字符串类型处理**: 参考号作为字符串类型处理，避免数值转换
3. **跨格式一致性**: CSV、JSON、Excel 三种格式中的数据完全一致

## 解析结果

### 数据统计

-   **总交易数**: 61 条
-   **数据完整性**: 100%（无缺失字段）
-   **日期范围**: 2023 年 3 月 11 日 - 2023 年 4 月 3 日
-   **解析策略**: 通用的智能表格识别架构

### 交易统计

-   **提取交易**: 23 笔
-   **提取总额**: ₹2,074,885.50
-   **存入交易**: 38 笔
-   **存入总额**: ₹2,089,494.00
-   **净变化**: ₹14,608.50

### 余额信息

-   **期初余额**: ₹393,530.61
-   **期末余额**: ₹375,778.11
-   **余额变化**: ₹-17,752.50

### 页面处理统计

-   **第 1 页**: 8 条交易
-   **第 2 页**: 18 条交易
-   **第 3 页**: 17 条交易
-   **第 4 页**: 18 条交易

## 技术实现

### 核心算法

1. **表格识别算法**: 基于列数和内容特征识别交易表格
2. **日期模式匹配**: 使用正则表达式识别 YES 银行日期格式
3. **金额数据清理**: 智能处理带逗号的金额格式
4. **描述文本处理**: 清理换行符和多余空格
5. **前导零保护**: 强制字符串类型处理，确保参考号前导零不丢失

### 数据质量保证

1. **完整性验证**: 确保所有交易记录都被正确提取
2. **格式标准化**: 统一的数据格式和结构
3. **原始性保持**: 保留所有原始列名和数据结构

### 输出格式

-   **CSV**: 8.5KB，便于数据分析和导入其他系统
-   **JSON**: 19KB，适合程序化处理和 API 集成
-   **Excel**: 9.6KB，便于人工查看和编辑

## 使用方法

### 基本使用

```python
from yes_pdf_parser import YESPDFParser

# 创建解析器实例
parser = YESPDFParser()

# 解析PDF文件
df = parser.parse_yes_pdf("path/to/yes_statement.pdf")

# 保存结果
parser.save_results(df)

# 生成验证报告
parser.generate_validation_report(df)
```

### 命令行使用

```bash
python3 yes_pdf_parser.py
```

## 文件结构

```
yes/
├── yes_pdf_parser.py            # 通用解析器
├── yes_extracted.csv            # CSV输出文件 (8.5KB, 61条记录)
├── yes_extracted.json           # JSON输出文件 (19KB, 61条记录)
├── yes_extracted.xlsx           # Excel输出文件 (9.6KB, 61条记录)
├── README.md                    # 项目文档
└── analyze_yes_pdf.py           # PDF结构分析工具（开发用）
```

## YES 银行 PDF 格式特点

### 表格结构

1. **清晰的表格边界**: YES 银行 PDF 使用清晰的表格线，适合 lattice 模式提取
2. **标准 7 列格式**: 所有交易页面都使用相同的 7 列结构
3. **一致的表头**: 每页都包含标准的表头行
4. **规范的日期格式**: 统一的 DD MMM YYYY 格式

### 页面布局

1. **第 1 页**: 包含账户信息和部分交易记录
2. **中间页**: 纯交易记录表格
3. **最后页**: 交易记录延续

### 数据特征

1. **交易描述详细**: 包含完整的银行间转账信息
2. **参考号完整**: 提供详细的交易参考号
3. **金额格式统一**: 使用逗号分隔的标准格式
4. **余额连续**: 每笔交易都有对应的账户余额

## 技术优势

### 相比硬编码解析器

1. **通用性**: 支持任意页数的 YES 银行账单
2. **适应性**: 基于表格结构特征的智能识别
3. **可维护性**: 规则驱动，易于扩展和维护
4. **可靠性**: 基于 YES 银行的通用格式规则

### 数据保真性

1. **完整保留**: 保持所有原始列名和数据结构
2. **格式一致**: 三种输出格式内容完全一致
3. **无数据丢失**: 确保所有交易记录都被正确提取
4. **原始性维护**: 不对原始数据进行不必要的修改

## 验证与质量

### 数据完整性

-   ✅ **总交易数**: 61 条（完整提取）
-   ✅ **数据完整性**: 100%（无缺失字段）
-   ✅ **列结构**: 7 列标准格式（完全保留）
-   ✅ **格式一致性**: 三种输出格式完全一致

### 解析准确性

-   ✅ **日期识别**: 100%准确识别 DD MMM YYYY 格式
-   ✅ **金额处理**: 正确处理带逗号的金额格式
-   ✅ **描述完整**: 保留完整的交易描述信息
-   ✅ **余额连续**: 正确提取所有余额信息

## 总结

YES 银行 PDF 解析器成功实现了以下目标：

1. ✅ **通用架构**: 基于表格结构特征的智能识别，支持任意页数
2. ✅ **数据保真**: 完全保留原始表格结构和格式
3. ✅ **高质量输出**: 61 条交易记录，100%数据完整性
4. ✅ **多格式支持**: CSV、JSON、Excel 三种格式输出
5. ✅ **可扩展性**: 易于扩展支持 YES 银行的其他账单格式

通过采用 UCO 银行解析器的通用架构设计理念，YES 银行 PDF 解析器实现了真正的通用性和可扩展性，为 YES 银行账单的数字化处理提供了高质量、高可靠性的技术支持。
