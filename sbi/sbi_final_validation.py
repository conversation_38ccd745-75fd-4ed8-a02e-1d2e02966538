#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SBI银行账单PDF解析最终验证
"""

import pandas as pd
import json

def final_validation():
    """最终验证SBI银行数据解析结果"""
    
    print("=" * 70)
    print("SBI银行账单PDF解析 - 最终验证报告")
    print("=" * 70)
    
    try:
        # 加载CSV数据
        df = pd.read_csv("sbi_extracted.csv")
        
        # 加载JSON数据
        with open("sbi_extracted.json", 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        print(f"\n✅ 数据加载成功")
        print(f"   CSV记录数: {len(df)}")
        print(f"   JSON记录数: {len(json_data)}")
        
        # 验证数据一致性
        if len(df) == len(json_data):
            print(f"✅ CSV和JSON记录数一致")
        else:
            print(f"❌ CSV和JSON记录数不一致")
        
        print(f"\n📊 详细数据验证:")
        
        # 1. 验证日期格式
        valid_dates = 0
        for date in df['Txn Date']:
            if date and str(date) != 'nan':
                # SBI日期格式应该是 DD/MM/YYYY
                if '/' in str(date) and len(str(date).split('/')) == 3:
                    valid_dates += 1
        
        print(f"   有效日期格式: {valid_dates}/{len(df)} ({valid_dates/len(df)*100:.1f}%)")
        
        # 2. 验证金额字段
        valid_debits = 0
        valid_credits = 0
        valid_balances = 0
        
        for _, row in df.iterrows():
            # 检查借记
            if row['Debit'] and str(row['Debit']) != 'nan' and str(row['Debit']) != '':
                try:
                    float(str(row['Debit']).replace(',', ''))
                    valid_debits += 1
                except:
                    pass
            
            # 检查贷记
            if row['Credit'] and str(row['Credit']) != 'nan' and str(row['Credit']) != '':
                try:
                    float(str(row['Credit']).replace(',', ''))
                    valid_credits += 1
                except:
                    pass
            
            # 检查余额
            if row['Balance'] and str(row['Balance']) != 'nan' and str(row['Balance']) != '':
                try:
                    float(str(row['Balance']).replace(',', ''))
                    valid_balances += 1
                except:
                    pass
        
        print(f"   有效借记金额: {valid_debits} 条")
        print(f"   有效贷记金额: {valid_credits} 条")
        print(f"   有效余额: {valid_balances}/{len(df)} ({valid_balances/len(df)*100:.1f}%)")
        
        # 3. 验证交易逻辑
        transactions_with_amount = 0
        for _, row in df.iterrows():
            has_debit = row['Debit'] and str(row['Debit']) != 'nan' and str(row['Debit']) != ''
            has_credit = row['Credit'] and str(row['Credit']) != 'nan' and str(row['Credit']) != ''
            
            if has_debit or has_credit:
                transactions_with_amount += 1
        
        print(f"   有交易金额的记录: {transactions_with_amount}/{len(df)} ({transactions_with_amount/len(df)*100:.1f}%)")
        
        # 4. 验证描述字段
        valid_descriptions = 0
        for desc in df['Description']:
            if desc and str(desc) != 'nan' and str(desc).strip() != '':
                valid_descriptions += 1
        
        print(f"   有效描述字段: {valid_descriptions}/{len(df)} ({valid_descriptions/len(df)*100:.1f}%)")
        
        print(f"\n📈 交易统计验证:")
        
        # 计算总金额
        total_debit = 0
        total_credit = 0
        
        for _, row in df.iterrows():
            if row['Debit'] and str(row['Debit']) != 'nan' and str(row['Debit']) != '':
                try:
                    total_debit += float(str(row['Debit']).replace(',', ''))
                except:
                    pass
            
            if row['Credit'] and str(row['Credit']) != 'nan' and str(row['Credit']) != '':
                try:
                    total_credit += float(str(row['Credit']).replace(',', ''))
                except:
                    pass
        
        print(f"   总借记金额: ₹{total_debit:,.2f}")
        print(f"   总贷记金额: ₹{total_credit:,.2f}")
        print(f"   净变化: ₹{total_credit - total_debit:,.2f}")
        
        # 5. 余额验证
        balances = []
        for balance in df['Balance']:
            if balance and str(balance) != 'nan':
                try:
                    balances.append(float(str(balance).replace(',', '')))
                except:
                    pass
        
        if balances:
            print(f"   期初余额: ₹{balances[0]:,.2f}")
            print(f"   期末余额: ₹{balances[-1]:,.2f}")
            print(f"   余额变化: ₹{balances[-1] - balances[0]:,.2f}")
        
        print(f"\n🔍 数据样本检查:")
        print("   前3条记录:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            debit = row['Debit'] if row['Debit'] and str(row['Debit']) != 'nan' else ''
            credit = row['Credit'] if row['Credit'] and str(row['Credit']) != 'nan' else ''
            amount_info = f"借记: ₹{debit}" if debit else f"贷记: ₹{credit}"
            
            print(f"   {i+1}. {row['Txn Date']} | {row['Description'][:30]}... | {amount_info} | 余额: ₹{row['Balance']}")
        
        print(f"\n📁 文件信息:")
        import os
        for file in ['sbi_extracted.csv', 'sbi_extracted.json', 'sbi_extracted.xlsx']:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"   ✓ {file}: {size:,} bytes")
        
        print(f"\n🎯 验证结论:")
        
        # 计算总体质量分数
        quality_score = 0
        total_checks = 5
        
        if valid_dates == len(df):
            quality_score += 1
            print(f"   ✅ 日期格式: 100% 正确")
        else:
            print(f"   ⚠️  日期格式: {valid_dates/len(df)*100:.1f}% 正确")
        
        if valid_balances == len(df):
            quality_score += 1
            print(f"   ✅ 余额数据: 100% 完整")
        else:
            print(f"   ⚠️  余额数据: {valid_balances/len(df)*100:.1f}% 完整")
        
        if valid_descriptions == len(df):
            quality_score += 1
            print(f"   ✅ 交易描述: 100% 完整")
        else:
            print(f"   ⚠️  交易描述: {valid_descriptions/len(df)*100:.1f}% 完整")
        
        if transactions_with_amount == len(df):
            quality_score += 1
            print(f"   ✅ 交易金额: 100% 完整")
        else:
            print(f"   ⚠️  交易金额: {transactions_with_amount/len(df)*100:.1f}% 完整")
        
        if len(df) == len(json_data):
            quality_score += 1
            print(f"   ✅ 数据一致性: CSV和JSON完全一致")
        else:
            print(f"   ⚠️  数据一致性: CSV和JSON不一致")
        
        overall_quality = quality_score / total_checks * 100
        print(f"\n🏆 总体质量评分: {overall_quality:.1f}%")
        
        if overall_quality >= 95:
            print("🎉 优秀！数据解析质量非常高")
        elif overall_quality >= 85:
            print("👍 良好！数据解析质量较高")
        elif overall_quality >= 70:
            print("⚠️  一般！数据解析质量需要改进")
        else:
            print("❌ 较差！数据解析质量需要大幅改进")
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 70)

if __name__ == "__main__":
    final_validation()
