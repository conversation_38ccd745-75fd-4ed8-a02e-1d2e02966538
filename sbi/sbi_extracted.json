[{"Txn Date": "05/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*HDFC0000240*N126201 128575952*ASTHA CREDIT AND-", "Ref No./Cheque No.": "TRANSFER FROM 3199423044304", "Debit": "", "Credit": "4000.00", "Balance": "64593.68"}, {"Txn Date": "05/05/2020", "Value Date": "", "Description": "TO TRANSFER-INB ASTHA CREDIT AND SECURITI-", "Ref No./Cheque No.": "300081706232IG AIOGPNL1 TRANSFER TO 4", "Debit": "20010.62", "Credit": "", "Balance": "44583.06"}, {"Txn Date": "05/05/2020", "Value Date": "", "Description": "TO TRANSFER-INB ASTHA CREDIT AND SECURITI-", "Ref No./Cheque No.": "300081708096IG AIOGPYG8 TRANSFER TO 4", "Debit": "14010.62", "Credit": "", "Balance": "30572.44"}, {"Txn Date": "06/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*HDFC0000240*N127201 129594007*ASTHA CREDIT AND-", "Ref No./Cheque No.": "TRANSFER FROM 3199681044308", "Debit": "", "Credit": "8930.20", "Balance": "39502.64"}, {"Txn Date": "06/05/2020", "Value Date": "", "Description": "TO TRANSFER-INB ASTHA CREDIT AND SECURITI-", "Ref No./Cheque No.": "300081864719IG AIOJTGK0 TRANSFER TO 4", "Debit": "35010.62", "Credit": "", "Balance": "4492.02"}, {"Txn Date": "06/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/012711859702/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097698162092", "Debit": "25.00", "Credit": "", "Balance": "4467.02"}, {"Txn Date": "06/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/012714912378/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097699162091", "Debit": "4000.00", "Credit": "", "Balance": "467.02"}, {"Txn Date": "06/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1473 633049*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199415044304", "Debit": "", "Credit": "700.00", "Balance": "1167.02"}, {"Txn Date": "06/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1473 633034*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199414044305", "Debit": "", "Credit": "2000.00", "Balance": "3167.02"}, {"Txn Date": "07/05/2020", "Value Date": "", "Description": "BY TRANSFER-INB IMPS012805491234/63649001 06/XX6751/EnGOmnV56o-", "Ref No./Cheque No.": "MAB00047435589 5 MAB00047435589 5", "Debit": "", "Credit": "1.00", "Balance": "3168.02"}, {"Txn Date": "07/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/012809669620/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097796162090", "Debit": "500.00", "Credit": "", "Balance": "2668.02"}, {"Txn Date": "07/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1474 513048*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199418044301", "Debit": "", "Credit": "2000.00", "Balance": "4668.02"}, {"Txn Date": "07/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1474 513066*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199414044305", "Debit": "", "Credit": "600.00", "Balance": "5268.02"}, {"Txn Date": "07/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1474 513084*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199956044307", "Debit": "", "Credit": "600.00", "Balance": "5868.02"}, {"Txn Date": "07/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1474 513094*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199957044306", "Debit": "", "Credit": "500.00", "Balance": "6368.02"}, {"Txn Date": "07/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1474 513054*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199955044308", "Debit": "", "Credit": "600.00", "Balance": "6968.02"}, {"Txn Date": "08/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*HDFC0000240*N129201 131306444*ASTHA CREDIT AND-", "Ref No./Cheque No.": "TRANSFER FROM 3199418044301", "Debit": "", "Credit": "9400.00", "Balance": "16368.02"}, {"Txn Date": "08/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/012912100399/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097899162093", "Debit": "4900.00", "Credit": "", "Balance": "11468.02"}, {"Txn Date": "08/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/012913540517/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097904162091", "Debit": "4900.00", "Credit": "", "Balance": "6568.02"}, {"Txn Date": "08/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1475 520413*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199682044307", "Debit": "", "Credit": "2500.00", "Balance": "9068.02"}, {"Txn Date": "08/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1475 520471*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199680044308", "Debit": "", "Credit": "4800.00", "Balance": "13868.02"}, {"Txn Date": "08/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1475 520571*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199681044308", "Debit": "", "Credit": "6400.00", "Balance": "20268.02"}, {"Txn Date": "08/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1475 520609*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199971044309", "Debit": "", "Credit": "5750.00", "Balance": "26018.02"}, {"Txn Date": "11/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013209078022/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097506162093", "Debit": "4900.00", "Credit": "", "Balance": "21118.02"}, {"Txn Date": "11/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013209200965/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097534162090", "Debit": "1400.00", "Credit": "", "Balance": "19718.02"}, {"Txn Date": "11/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013212215493/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097540162091", "Debit": "7000.00", "Credit": "", "Balance": "12718.02"}, {"Txn Date": "11/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1477 548488*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199967044305", "Debit": "", "Credit": "9500.00", "Balance": "22218.02"}, {"Txn Date": "12/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013306285673/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097605162091", "Debit": "4850.00", "Credit": "", "Balance": "17368.02"}, {"Txn Date": "12/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013309728084/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097644162095", "Debit": "15000.00", "Credit": "", "Balance": "2368.02"}, {"Txn Date": "12/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1478 441361*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199960044301", "Debit": "", "Credit": "18450.00", "Balance": "20818.02"}, {"Txn Date": "13/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013408238027/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097717162094", "Debit": "19750.00", "Credit": "", "Balance": "1068.02"}, {"Txn Date": "13/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1479 263926*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199955044308", "Debit": "", "Credit": "22500.00", "Balance": "23568.02"}, {"Txn Date": "14/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013508191847/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097800162097", "Debit": "19900.00", "Credit": "", "Balance": "3668.02"}, {"Txn Date": "14/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1480 033811*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199966044306", "Debit": "", "Credit": "21190.00", "Balance": "24858.02"}, {"Txn Date": "15/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013608142514/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO *************", "Debit": "19850.00", "Credit": "", "Balance": "5008.02"}, {"Txn Date": "15/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013612499510/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097880162093", "Debit": "1100.00", "Credit": "", "Balance": "3908.02"}, {"Txn Date": "15/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013614109590/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097901162094", "Debit": "900.00", "Credit": "", "Balance": "3008.02"}, {"Txn Date": "15/05/2020", "Value Date": "", "Description": "TO TRANSFER-INB ASTHA CREDIT AND SECURITI-", "Ref No./Cheque No.": "300083037246IG AIPPFZZ7 TRANSFER TO 4", "Debit": "2910.62", "Credit": "", "Balance": "97.40"}, {"Txn Date": "15/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1481 328095*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199963044309", "Debit": "", "Credit": "17300.00", "Balance": "17397.40"}, {"Txn Date": "15/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013619623548/Zerodh a /HDFC/zerodhabro/Zerod-", "Ref No./Cheque No.": "TRANSFER TO 5097905162090", "Debit": "40.00", "Credit": "", "Balance": "17357.40"}, {"Txn Date": "15/05/2020", "Value Date": "", "Description": "BY TRANSFER- UPI/REV/013619623548-", "Ref No./Cheque No.": "TRANSFER FROM 5097905162090", "Debit": "", "Credit": "40.00", "Balance": "17397.40"}, {"Txn Date": "15/05/2020", "Value Date": "", "Description": "BY TRANSFER-INB IMPS013619436612/99869820 20/XX3413/-", "Ref No./Cheque No.": "MAC00048797032 5 MAC00048797032 5", "Debit": "", "Credit": "1.00", "Balance": "17398.40"}, {"Txn Date": "16/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013623656236/CASHF REE/HDFC/cashfree@h/SKRS k-", "Ref No./Cheque No.": "TRANSFER TO 5098018162090", "Debit": "1026.43", "Credit": "", "Balance": "16371.97"}, {"Txn Date": "16/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013710783192/CASHF REE/HDFC/cashfree@h/SKRS k-", "Ref No./Cheque No.": "TRANSFER TO 5098008162091", "Debit": "840.45", "Credit": "", "Balance": "15531.52"}, {"Txn Date": "17/05/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/013807887006/Zerodh a /HDFC/zerodhabro/Kite-", "Ref No./Cheque No.": "TRANSFER TO 5098086162099", "Debit": "40.00", "Credit": "", "Balance": "15491.52"}, {"Txn Date": "17/05/2020", "Value Date": "", "Description": "TO TRANSFER-INB ASTHA CREDIT AND SECURITI-", "Ref No./Cheque No.": "300083234403IG AIPXLHU0 TRANSFER TO 4", "Debit": "13510.62", "Credit": "", "Balance": "1980.90"}, {"Txn Date": "19/05/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1484 099137*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199677044304", "Debit": "", "Credit": "200.00", "Balance": "2180.90"}, {"Txn Date": "20/05/2020", "Value Date": "", "Description": "TO TRANSFER-INB ASTHA CREDIT AND SECURITI-", "Ref No./Cheque No.": "300083642903IG AIQIJOB0 TRANSFER TO 4", "Debit": "2010.62", "Credit": "", "Balance": "170.28"}, {"Txn Date": "02/06/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*RBIS0GOAPEP*RBI154 2016810233*Andhra Pradesh T -", "Ref No./Cheque No.": "TRANSFER FROM 3199963044309", "Debit": "", "Credit": "42770.00", "Balance": "42940.28"}, {"Txn Date": "02/06/2020", "Value Date": "", "Description": "TO TRANSFER-INB ASTHA CREDIT AND SECURITI-", "Ref No./Cheque No.": "300085082319IG AISFILM6 TRANSFER TO 4", "Debit": "37110.62", "Credit": "", "Balance": "5829.66"}, {"Txn Date": "02/06/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/015412178917/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097629162094", "Debit": "5000.00", "Credit": "", "Balance": "829.66"}, {"Txn Date": "02/06/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1496 947273*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199413044306", "Debit": "", "Credit": "1040.00", "Balance": "1869.66"}, {"Txn Date": "03/06/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*HDFC0000240*N155201 149483113*ASTHA CREDIT AND-", "Ref No./Cheque No.": "TRANSFER FROM 3199423044304", "Debit": "", "Credit": "6000.00", "Balance": "7869.66"}, {"Txn Date": "03/06/2020", "Value Date": "", "Description": "TO TRANSFER-INB ASTHA CREDIT AND SECURITI-", "Ref No./Cheque No.": "300085261335IG AISKFTY6 TRANSFER TO 4", "Debit": "2310.62", "Credit": "", "Balance": "5559.04"}, {"Txn Date": "04/06/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/015609010772/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO *************", "Debit": "5000.00", "Credit": "", "Balance": "559.04"}, {"Txn Date": "04/06/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1499 188157*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199423044304", "Debit": "", "Credit": "145.00", "Balance": "704.04"}, {"Txn Date": "04/06/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1499 188197*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199424044303", "Debit": "", "Credit": "2440.00", "Balance": "3144.04"}, {"Txn Date": "10/06/2020", "Value Date": "", "Description": "TO TRANSFER-INB Navia Markets Limited-", "Ref No./Cheque No.": "300086195312IG AITMHGA0 TRANSFER TO 4", "Debit": "1000.00", "Credit": "", "Balance": "2144.04"}, {"Txn Date": "10/06/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*CITI0100000*CITIN2008 0400757*EDELWEISS BROKIN -", "Ref No./Cheque No.": "TRANSFER FROM 3199423044304", "Debit": "", "Credit": "31.22", "Balance": "2175.26"}, {"Txn Date": "10/06/2020", "Value Date": "", "Description": "TO TRANSFER-INB Navia Markets Limited-", "Ref No./Cheque No.": "300086260583IG AITNUPQ3 TRANSFER TO 4", "Debit": "1000.00", "Credit": "", "Balance": "1175.26"}, {"Txn Date": "11/06/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00026476 2276*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199678044303", "Debit": "", "Credit": "1000.00", "Balance": "2175.26"}, {"Txn Date": "16/06/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*HDFC0000240*N168201 161017801*ASTHA CREDIT AND-", "Ref No./Cheque No.": "TRANSFER FROM 3199682044307", "Debit": "", "Credit": "450.00", "Balance": "2625.26"}, {"Txn Date": "16/06/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/016816090425/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097624162099", "Debit": "82.00", "Credit": "", "Balance": "2543.26"}, {"Txn Date": "17/06/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/016912022408/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097722162097", "Debit": "19.00", "Credit": "", "Balance": "2524.26"}, {"Txn Date": "18/06/2020", "Value Date": "", "Description": "TO TRANSFER-INB Navia Markets Limited-", "Ref No./Cheque No.": "300087282670IG AIUQPUC0 TRANSFER TO 4", "Debit": "1000.00", "Credit": "", "Balance": "1524.26"}, {"Txn Date": "19/06/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/017106083625/razorpa y/ICIC/razorpay@i/UPSTO-", "Ref No./Cheque No.": "TRANSFER TO 5097883162091", "Debit": "293.82", "Credit": "", "Balance": "1230.44"}, {"Txn Date": "25/06/2020", "Value Date": "", "Description": "TO TRANSFER-INB Navia Markets Limited-", "Ref No./Cheque No.": "400088158582IG AIVQQAZ4 TRANSFER TO 4", "Debit": "1000.00", "Credit": "", "Balance": "230.44"}, {"Txn Date": "25/06/2020", "Value Date": "", "Description": "CREDIT INTEREST--", "Ref No./Cheque No.": "", "Debit": "", "Credit": "99.00", "Balance": "329.44"}, {"Txn Date": "30/06/2020", "Value Date": "", "Description": "TO TRANSFER-INB BSNL BSNL_RECHARGE Paymen-", "Ref No./Cheque No.": "SSBI8948578037I GAIWNFDG2 TRANSFER TO", "Debit": "98.00", "Credit": "", "Balance": "231.44"}, {"Txn Date": "01/07/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00026842 9358*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199964044308", "Debit": "", "Credit": "2716.84", "Balance": "2948.28"}, {"Txn Date": "01/07/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00026842 9331*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199682044307", "Debit": "", "Credit": "2898.46", "Balance": "5846.74"}, {"Txn Date": "01/07/2020", "Value Date": "", "Description": "TO TRANSFER-INB BSNL ALL INDIA CDMA TECH_BSNLCDM Payment-", "Ref No./Cheque No.": "1154107999IGAI WRMXJ9 TRANSFER TO 459", "Debit": "98.00", "Credit": "", "Balance": "5748.74"}, {"Txn Date": "03/07/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/018509514803/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097906162099", "Debit": "5000.00", "Credit": "", "Balance": "748.74"}, {"Txn Date": "03/07/2020", "Value Date": "", "Description": "BY TRANSFER- UPI/CR/018509660745/GOOG LEPAY/UTIB/goog- payme/Rewa-", "Ref No./Cheque No.": "TRANSFER FROM *************", "Debit": "", "Credit": "7.00", "Balance": "755.74"}, {"Txn Date": "03/07/2020", "Value Date": "", "Description": "BY TRANSFER- UPI/CR/018509663668/GOOG LEPAY/UTIB/goog- payme/Rewa-", "Ref No./Cheque No.": "TRANSFER FROM 5099140162091", "Debit": "", "Credit": "7.00", "Balance": "762.74"}, {"Txn Date": "03/07/2020", "Value Date": "", "Description": "BY TRANSFER- UPI/CR/018509665779/GOOG LEPAY/UTIB/goog- payme/Rewa-", "Ref No./Cheque No.": "TRANSFER FROM 5099170162096", "Debit": "", "Credit": "9.00", "Balance": "771.74"}, {"Txn Date": "03/07/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1528 976317*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199969044303", "Debit": "", "Credit": "6000.00", "Balance": "6771.74"}, {"Txn Date": "03/07/2020", "Value Date": "", "Description": "BY TRANSFER-INB Refund of IGAIWNFDG2-", "Ref No./Cheque No.": "SSBI8948578037 1IXGATJRKW9 TRANSFER F", "Debit": "", "Credit": "98.00", "Balance": "6869.74"}, {"Txn Date": "06/07/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/018808997440/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097539162095", "Debit": "4800.00", "Credit": "", "Balance": "2069.74"}, {"Txn Date": "06/07/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1531 554892*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199675044306", "Debit": "", "Credit": "1450.00", "Balance": "3519.74"}, {"Txn Date": "08/07/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*RBIS0GOAPEP*RBI191 2059201350*Andhra Pradesh T -", "Ref No./Cheque No.": "TRANSFER FROM 3199960044301", "Debit": "", "Credit": "42770.00", "Balance": "46289.74"}, {"Txn Date": "09/07/2020", "Value Date": "", "Description": "TO TRANSFER-INB ASTHA CREDIT AND SECURITI-", "Ref No./Cheque No.": "300090124870IG AIXYLJV1 TRANSFER TO 4", "Debit": "45010.62", "Credit": "", "Balance": "1279.12"}, {"Txn Date": "18/07/2020", "Value Date": "", "Description": "TO TRANSFER-INB BSNL BSNL_RECHARGE Paymen-", "Ref No./Cheque No.": "SSBI9011782555I GAIZMMNO2 TRANSFER TO", "Debit": "98.00", "Credit": "", "Balance": "1181.12"}, {"Txn Date": "23/07/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/020509199541/NAVIA MAR/ICIC/naviaupi@i/Fund-", "Ref No./Cheque No.": "TRANSFER TO 5097805162093", "Debit": "400.00", "Credit": "", "Balance": "781.12"}, {"Txn Date": "30/07/2020", "Value Date": "", "Description": "TO TRANSFER-INB Navia Markets Limited-", "Ref No./Cheque No.": "300093629481IG AJBJPED9 TRANSFER TO 4", "Debit": "700.00", "Credit": "", "Balance": "81.12"}, {"Txn Date": "31/07/2020", "Value Date": "", "Description": "BY TRANSFER-INB IMPS021304407738/00000000 00/XX0150/-", "Ref No./Cheque No.": "MAC00052620693 6 MAC00052620693 6", "Debit": "", "Credit": "11000.00", "Balance": "11081.12"}, {"Txn Date": "31/07/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/021312498980/NAVIA MAR/ICIC/naviaupi@i/Fund-", "Ref No./Cheque No.": "TRANSFER TO 5097914162090", "Debit": "8000.00", "Credit": "", "Balance": "3081.12"}, {"Txn Date": "03/08/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*RBIS0GOAPEP*RBI216 2079921563*Andhra Pradesh T -", "Ref No./Cheque No.": "TRANSFER FROM 3199410044308", "Debit": "", "Credit": "42770.00", "Balance": "45851.12"}, {"Txn Date": "03/08/2020", "Value Date": "", "Description": "TO TRANSFER-INB Navia Markets Limited-", "Ref No./Cheque No.": "300094300704IG AJCAJRL3 TRANSFER TO 4", "Debit": "40000.00", "Credit": "", "Balance": "5851.12"}, {"Txn Date": "04/08/2020", "Value Date": "", "Description": "TO TRANSFER-INB Navia Markets Limited-", "Ref No./Cheque No.": "300094583732IG AJCGXUW1 TRANSFER TO 4", "Debit": "5000.00", "Credit": "", "Balance": "851.12"}, {"Txn Date": "05/08/2020", "Value Date": "", "Description": "BY TRANSFER-INB IMPS021821441806/00000000 00/XX0150/-", "Ref No./Cheque No.": "MAB00051993356 0 MAB00051993356 0", "Debit": "", "Credit": "400.00", "Balance": "1251.12"}, {"Txn Date": "10/08/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*HDFC0000240*N223201 209941075*ZERODHA BROKING-", "Ref No./Cheque No.": "TRANSFER FROM 3199959044304", "Debit": "", "Credit": "4.71", "Balance": "1255.83"}, {"Txn Date": "10/08/2020", "Value Date": "", "Description": "ATM WDL-ATM CASH 1180 SBI G.K.RACHAPALLI CUDDAPAH-", "Ref No./Cheque No.": "", "Debit": "1000.00", "Credit": "", "Balance": "255.83"}, {"Txn Date": "13/08/2020", "Value Date": "", "Description": "TO TRANSFER-INB BSNL BSNL_RECHARGE Paymen-", "Ref No./Cheque No.": "SSBI9109106649I GAJEECRX0 TRANSFER TO", "Debit": "98.00", "Credit": "", "Balance": "157.83"}, {"Txn Date": "21/08/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00027844 8008*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199681044308", "Debit": "", "Credit": "21000.00", "Balance": "21157.83"}, {"Txn Date": "27/08/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/024009435680/NAVIA MAR/ICIC/naviaupi@i/Fund-", "Ref No./Cheque No.": "TRANSFER TO 5097792162094", "Debit": "21000.00", "Credit": "", "Balance": "157.83"}, {"Txn Date": "28/08/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00027972 4408*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199956044307", "Debit": "", "Credit": "22000.00", "Balance": "22157.83"}, {"Txn Date": "31/08/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/024406060192/RELIAN CE/CITI/jio@citiba/JIO20-", "Ref No./Cheque No.": "TRANSFER TO 5097505162094", "Debit": "149.00", "Credit": "", "Balance": "22008.83"}, {"Txn Date": "31/08/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/024408416274/NAVIA MAR/ICIC/naviaupi@i/Fund-", "Ref No./Cheque No.": "TRANSFER TO 5097526162090", "Debit": "22000.00", "Credit": "", "Balance": "8.83"}, {"Txn Date": "01/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*RBIS0GOAPEP*RBI245 2011692921*Andhra Pradesh T -", "Ref No./Cheque No.": "TRANSFER FROM 3199418044301", "Debit": "", "Credit": "42770.00", "Balance": "42778.83"}, {"Txn Date": "02/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1600 442713*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199958044305", "Debit": "", "Credit": "25.00", "Balance": "42803.83"}, {"Txn Date": "04/09/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/024808444135/NAVIA MAR/ICIC/naviaupi@i/Fund-", "Ref No./Cheque No.": "TRANSFER TO 5097924162098", "Debit": "21000.00", "Credit": "", "Balance": "21803.83"}, {"Txn Date": "04/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00028133 2428*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199420044306", "Debit": "", "Credit": "25000.00", "Balance": "46803.83"}, {"Txn Date": "05/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00028162 1305*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199965044307", "Debit": "", "Credit": "320.27", "Balance": "47124.10"}, {"Txn Date": "08/09/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/025208014895/NAVIA MAR/ICIC/naviaupi@i/Fund-", "Ref No./Cheque No.": "TRANSFER TO 5097632162099", "Debit": "41000.00", "Credit": "", "Balance": "6124.10"}, {"Txn Date": "08/09/2020", "Value Date": "", "Description": "BY TRANSFER- UPI/CR/025214520293/GOOG LEPAY/UTIB/goog- payme/Rewa-", "Ref No./Cheque No.": "TRANSFER FROM 5098928162096", "Debit": "", "Credit": "8.00", "Balance": "6132.10"}, {"Txn Date": "14/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*HDFC0000240*N258201 243466025*5 PAISA CAPITAL-", "Ref No./Cheque No.": "TRANSFER FROM 3199675044306", "Debit": "", "Credit": "423.11", "Balance": "6555.21"}, {"Txn Date": "19/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00028454 0603*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199680044308", "Debit": "", "Credit": "587.71", "Balance": "7142.92"}, {"Txn Date": "21/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00028468 5465*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199957044306", "Debit": "", "Credit": "4412.29", "Balance": "11555.21"}, {"Txn Date": "23/09/2020", "Value Date": "", "Description": "BY TRANSFER- UPI/CR/026704224189/GOOG LEPAY/UTIB/goog- payme/Rewa-", "Ref No./Cheque No.": "TRANSFER FROM 5098422162090", "Debit": "", "Credit": "5.00", "Balance": "11560.21"}, {"Txn Date": "23/09/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/026704573917/<PERSON><PERSON>/YESB/jio@yesban/JIO20-", "Ref No./Cheque No.": "TRANSFER TO 5097693162097", "Debit": "199.00", "Credit": "", "Balance": "11361.21"}, {"Txn Date": "23/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00028520 8956*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199959044304", "Debit": "", "Credit": "3000.00", "Balance": "14361.21"}, {"Txn Date": "24/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00028545 0961*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199968044304", "Debit": "", "Credit": "9500.00", "Balance": "23861.21"}, {"Txn Date": "25/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00028569 8161*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199419044300", "Debit": "", "Credit": "4500.00", "Balance": "28361.21"}, {"Txn Date": "25/09/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/026914450558/NAVIA MAR/ICIC/naviaupi@i/Fund-", "Ref No./Cheque No.": "TRANSFER TO 5097921162091", "Debit": "25000.00", "Credit": "", "Balance": "3361.21"}, {"Txn Date": "25/09/2020", "Value Date": "", "Description": "CREDIT INTEREST--", "Ref No./Cheque No.": "", "Debit": "", "Credit": "58.00", "Balance": "3419.21"}, {"Txn Date": "26/09/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/027007570915/Federal /FDRL/bsnlupi1@f/FEDER-", "Ref No./Cheque No.": "TRANSFER TO 5097996162093", "Debit": "97.00", "Credit": "", "Balance": "3322.21"}, {"Txn Date": "26/09/2020", "Value Date": "", "Description": "BY TRANSFER-INB IMPS027018295209/00000000 00/XX6735/94941349DT-", "Ref No./Cheque No.": "MAA00056588764 9 MAA00056588764 9", "Debit": "", "Credit": "4500.00", "Balance": "7822.21"}, {"Txn Date": "28/09/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/027212857432/FNOPA Y/ICIC/fnopay@ici/Paying-", "Ref No./Cheque No.": "TRANSFER TO 5097539162095", "Debit": "5000.00", "Credit": "", "Balance": "2822.21"}, {"Txn Date": "28/09/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*ICIC0000104*CMS1628 221610*FNO INDIA SECURITIE-", "Ref No./Cheque No.": "TRANSFER FROM 3199969044303", "Debit": "", "Credit": "2560.00", "Balance": "5382.21"}, {"Txn Date": "01/10/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/027510163127/NAVIA MAR/ICIC/naviaupi@i/Fund-", "Ref No./Cheque No.": "TRANSFER TO 5097811162096", "Debit": "4000.00", "Credit": "", "Balance": "1382.21"}, {"Txn Date": "01/10/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*RBIS0GOAPEP*RBI276 2046152442*Andhra Pradesh T -", "Ref No./Cheque No.": "TRANSFER FROM 3199963044309", "Debit": "", "Credit": "42770.00", "Balance": "44152.21"}, {"Txn Date": "03/10/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*INDB0000006*00028781 6898*NAVIA MARKETS LIMITE -", "Ref No./Cheque No.": "TRANSFER FROM 3199676044305", "Debit": "", "Credit": "513.77", "Balance": "44665.98"}, {"Txn Date": "09/10/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/028309301655/Cash Free/YESB/cashfree@y/UPI-", "Ref No./Cheque No.": "TRANSFER TO 5097906162099", "Debit": "100.00", "Credit": "", "Balance": "44565.98"}, {"Txn Date": "12/10/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/028616340416/Federal /FDRL/bsnlupi1@f/FEDER-", "Ref No./Cheque No.": "TRANSFER TO 5097547162095", "Debit": "105.00", "Credit": "", "Balance": "44460.98"}, {"Txn Date": "15/10/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/028907241774/Upstox/ HDFC/upstoxsec@/AV2540-", "Ref No./Cheque No.": "TRANSFER TO 5097796162090", "Debit": "40000.00", "Credit": "", "Balance": "4460.98"}, {"Txn Date": "15/10/2020", "Value Date": "", "Description": "BY TRANSFER- UPI/028616340416/REVERSAL -", "Ref No./Cheque No.": "TRANSFER FROM *************", "Debit": "", "Credit": "105.00", "Balance": "4565.98"}, {"Txn Date": "16/10/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*HDFC0000240*N290201 277908087*RKSV SECURITIES-", "Ref No./Cheque No.": "TRANSFER FROM 3199964044308", "Debit": "", "Credit": "2500.00", "Balance": "7065.98"}, {"Txn Date": "17/10/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/029106978870/Bharat S/PYTM/paytm-3309/UPI-", "Ref No./Cheque No.": "TRANSFER TO *************", "Debit": "105.00", "Credit": "", "Balance": "6960.98"}, {"Txn Date": "17/10/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*HDFC0000240*N291201 278892898*RKSV SECURITIES-", "Ref No./Cheque No.": "TRANSFER FROM 3199416044303", "Debit": "", "Credit": "2600.00", "Balance": "9560.98"}, {"Txn Date": "25/10/2020", "Value Date": "", "Description": "by debit card-SBIPG 380000584972airtel3/ap/prepai d Gurgaon-", "Ref No./Cheque No.": "", "Debit": "219.00", "Credit": "", "Balance": "9341.98"}, {"Txn Date": "02/11/2020", "Value Date": "", "Description": "BY TRANSFER- NEFT*RBIS0GOAPEP*RBI307 2078896212*Andhra Pradesh T -", "Ref No./Cheque No.": "TRANSFER FROM 3199424044303", "Debit": "", "Credit": "42770.00", "Balance": "52111.98"}, {"Txn Date": "02/11/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/030713814511/Upstox/ HDFC/upstoxsec@/AV2540-", "Ref No./Cheque No.": "TRANSFER TO 5097550162099", "Debit": "40000.00", "Credit": "", "Balance": "12111.98"}, {"Txn Date": "03/11/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/030812539650/Bharat S/PYTM/paytm-3309/UPI-", "Ref No./Cheque No.": "TRANSFER TO 5097626162097", "Debit": "98.00", "Credit": "", "Balance": "12013.98"}, {"Txn Date": "05/11/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/031016587297/005410 10/andb/0054101001/Send-", "Ref No./Cheque No.": "TRANSFER TO *************", "Debit": "100.00", "Credit": "", "Balance": "11913.98"}, {"Txn Date": "05/11/2020", "Value Date": "", "Description": "TO TRANSFER- UPI/DR/031016625648/005410 10/andb/0054101001/Send-", "Ref No./Cheque No.": "TRANSFER TO 5099556162099", "Debit": "3900.00", "Credit": "", "Balance": "8013.98"}]