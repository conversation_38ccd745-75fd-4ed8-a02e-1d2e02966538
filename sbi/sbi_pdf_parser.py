#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SBI银行账单PDF解析器
从PDF中提取交易数据并保存为JSON、CSV和Excel格式
"""

import pandas as pd
import pdfplumber
import re
import json
from datetime import datetime
import os
import sys

def clean_text(text):
    """清理文本，去除多余的空格和特殊字符"""
    if not text:
        return ""
    # 移除特殊字符如(cid:9)
    text = re.sub(r'\(cid:\d+\)', ' ', text)
    # 替换多个空格为单个空格
    text = re.sub(r'\s+', ' ', text.strip())
    return text

def parse_amount(amount_str):
    """解析金额字符串，返回浮点数"""
    if not amount_str or amount_str.strip() == '':
        return None
    
    # 移除逗号和其他非数字字符（除了小数点）
    amount_str = re.sub(r'[^\d.-]', '', amount_str.strip())
    
    try:
        return float(amount_str) if amount_str else None
    except ValueError:
        return None

def parse_date(date_str):
    """解析日期字符串，转换为统一格式"""
    if not date_str:
        return ""
    
    date_str = clean_text(date_str).strip()
    
    # SBI日期格式通常是 "7 May 2020"
    try:
        # 尝试解析 "DD MMM YYYY" 格式
        if re.match(r'\d{1,2}\s+[A-Za-z]{3}\s+\d{4}', date_str):
            date_obj = datetime.strptime(date_str, '%d %b %Y')
            return date_obj.strftime('%d/%m/%Y')
    except:
        pass
    
    return date_str

def extract_transaction_data_from_pdf(pdf_path):
    """从SBI银行PDF中提取交易数据"""
    transactions = []
    
    print(f"正在解析SBI银行PDF文件: {pdf_path}")
    
    with pdfplumber.open(pdf_path) as pdf:
        print(f"PDF总页数: {len(pdf.pages)}")
        
        for page_num, page in enumerate(pdf.pages, 1):
            print(f"正在处理第 {page_num} 页...")
            
            # 提取表格数据
            tables = page.extract_tables()
            
            if tables:
                for table_idx, table in enumerate(tables):
                    print(f"  找到表格 {table_idx + 1}，行数: {len(table)}")
                    
                    # 查找表头
                    header_row = None
                    for i, row in enumerate(table):
                        if row and any(cell and ('Txn Date' in str(cell) or 'Date' in str(cell)) for cell in row):
                            header_row = i
                            break
                    
                    if header_row is not None:
                        headers = table[header_row]
                        print(f"  找到表头: {headers}")
                        
                        # 标准化表头
                        normalized_headers = []
                        for header in headers:
                            if header:
                                header_clean = clean_text(str(header))
                                if 'Txn Date' in header_clean or 'Date' in header_clean:
                                    normalized_headers.append('Txn Date')
                                elif 'Value' in header_clean and 'Date' in header_clean:
                                    normalized_headers.append('Value Date')
                                elif 'Description' in header_clean:
                                    normalized_headers.append('Description')
                                elif 'Ref' in header_clean or 'Cheque' in header_clean:
                                    normalized_headers.append('Ref No./Cheque No.')
                                elif 'Debit' in header_clean:
                                    normalized_headers.append('Debit')
                                elif 'Credit' in header_clean:
                                    normalized_headers.append('Credit')
                                elif 'Balance' in header_clean:
                                    normalized_headers.append('Balance')
                                else:
                                    normalized_headers.append(header_clean)
                            else:
                                normalized_headers.append('')
                        
                        # 处理数据行
                        for row_idx in range(header_row + 1, len(table)):
                            row = table[row_idx]
                            if not row or all(not cell or str(cell).strip() == '' for cell in row):
                                continue
                            
                            # 确保行有足够的列
                            while len(row) < len(normalized_headers):
                                row.append('')
                            
                            # 检查是否是有效的交易行（至少有日期）
                            if row[0] and clean_text(str(row[0])):
                                # 创建交易记录
                                transaction = {}
                                for col_idx, header in enumerate(normalized_headers):
                                    if header and col_idx < len(row):
                                        cell_value = clean_text(str(row[col_idx]) if row[col_idx] else '')
                                        transaction[header] = cell_value
                                
                                # 只添加有效的交易记录（至少有日期）
                                if transaction.get('Txn Date'):
                                    transactions.append(transaction)
            
            # 如果没有找到表格，尝试文本解析
            if not tables:
                print(f"  未找到表格，尝试文本解析...")
                text = page.extract_text()
                if text:
                    text_transactions = parse_text_transactions(text)
                    transactions.extend(text_transactions)
    
    print(f"总共提取到 {len(transactions)} 条交易记录")
    return transactions

def parse_text_transactions(text):
    """从文本中解析交易数据（备用方法）"""
    transactions = []
    lines = text.split('\n')
    
    # 查找交易数据行
    for line in lines:
        line = clean_text(line)
        if not line:
            continue
        
        # 查找日期模式开始的行（SBI格式：DD MMM YYYY）
        date_match = re.match(r'^(\d{1,2}\s+[A-Za-z]{3}\s+\d{4})', line)
        if date_match:
            # 这是一个简化的文本解析，可能需要根据实际格式调整
            parts = line.split()
            if len(parts) >= 3:
                transaction = {
                    'Txn Date': date_match.group(1),
                    'Value Date': '',
                    'Description': '',
                    'Ref No./Cheque No.': '',
                    'Debit': '',
                    'Credit': '',
                    'Balance': ''
                }
                transactions.append(transaction)
    
    return transactions

def normalize_transaction_data(transactions):
    """标准化交易数据"""
    normalized_transactions = []
    
    for transaction in transactions:
        normalized = {
            'Txn Date': parse_date(transaction.get('Txn Date', '')),
            'Value Date': parse_date(transaction.get('Value Date', '')),
            'Description': clean_text(transaction.get('Description', '')),
            'Ref No./Cheque No.': clean_text(transaction.get('Ref No./Cheque No.', '')),
            'Debit': format_amount(transaction.get('Debit', '')),
            'Credit': format_amount(transaction.get('Credit', '')),
            'Balance': format_amount(transaction.get('Balance', ''))
        }
        
        # 只添加有日期的记录
        if normalized['Txn Date']:
            normalized_transactions.append(normalized)
    
    return normalized_transactions

def format_amount(amount_str):
    """格式化金额"""
    if not amount_str or str(amount_str).strip() == '':
        return ''
    
    amount = parse_amount(str(amount_str))
    if amount is not None:
        return f"{amount:.2f}"
    return ''

def save_data(transactions, base_filename):
    """保存数据为JSON、CSV和Excel格式"""
    
    # 创建DataFrame
    df = pd.DataFrame(transactions)
    
    # 确保列的顺序
    target_columns = ['Txn Date', 'Value Date', 'Description', 'Ref No./Cheque No.', 'Debit', 'Credit', 'Balance']
    df = df.reindex(columns=target_columns, fill_value='')
    
    # 保存为CSV
    csv_filename = f"{base_filename}.csv"
    df.to_csv(csv_filename, index=False, encoding='utf-8')
    print(f"已保存CSV文件: {csv_filename}")
    
    # 保存为JSON
    json_filename = f"{base_filename}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(transactions, f, ensure_ascii=False, indent=2)
    print(f"已保存JSON文件: {json_filename}")
    
    # 保存为Excel
    excel_filename = f"{base_filename}.xlsx"
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    print(f"已保存Excel文件: {excel_filename}")
    
    return csv_filename, json_filename, excel_filename

def generate_summary_report(transactions):
    """生成总结报告"""
    print(f"\n=== SBI银行账单解析结果总结 ===")
    print(f"总交易记录数: {len(transactions)}")
    
    if transactions:
        # 统计交易类型
        debit_count = sum(1 for t in transactions if t.get('Debit', ''))
        credit_count = sum(1 for t in transactions if t.get('Credit', ''))
        
        print(f"借记交易: {debit_count} 笔")
        print(f"贷记交易: {credit_count} 笔")
        
        # 计算总金额
        try:
            total_debit = sum(float(t['Debit'].replace(',', '')) for t in transactions if t.get('Debit', ''))
            total_credit = sum(float(t['Credit'].replace(',', '')) for t in transactions if t.get('Credit', ''))
            
            print(f"总借记金额: ₹{total_debit:,.2f}")
            print(f"总贷记金额: ₹{total_credit:,.2f}")
        except:
            print("金额计算出错")
        
        # 时间范围
        dates = [t['Txn Date'] for t in transactions if t.get('Txn Date')]
        if dates:
            print(f"交易时间范围: {dates[0]} 到 {dates[-1]}")
        
        # 样本数据
        print(f"\n前3条交易记录:")
        for i, transaction in enumerate(transactions[:3], 1):
            print(f"{i}. {transaction.get('Txn Date', '')} | {transaction.get('Description', '')[:40]}... | 余额: ₹{transaction.get('Balance', '')}")

def main():
    """主函数"""
    pdf_file = "../files/1-sbi-0552932927-recent-sbi-statement.pdf"
    output_base = "sbi_extracted"
    
    if not os.path.exists(pdf_file):
        print(f"错误: PDF文件不存在 - {pdf_file}")
        return
    
    try:
        # 提取数据
        transactions = extract_transaction_data_from_pdf(pdf_file)
        
        if not transactions:
            print("未能从PDF中提取到任何交易数据")
            return
        
        # 标准化数据
        normalized_transactions = normalize_transaction_data(transactions)
        
        if not normalized_transactions:
            print("数据标准化后没有有效记录")
            return
        
        # 保存数据
        csv_file, json_file, excel_file = save_data(normalized_transactions, output_base)
        
        # 生成总结报告
        generate_summary_report(normalized_transactions)
        
        print(f"\n=== 处理完成 ===")
        print(f"成功提取并保存了 {len(normalized_transactions)} 条交易记录")
        print(f"文件已保存在 sbi/ 文件夹中:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")
        
    except Exception as e:
        print(f"处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
