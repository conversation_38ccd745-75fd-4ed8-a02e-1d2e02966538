#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IndusInd银行Tesseract OCR解析器
使用Tesseract OCR + pdf2image技术方案提取PDF中的交易数据
"""

import pandas as pd
import re
import os
import subprocess
import sys

# 导入所需库
try:
    import pytesseract
    from pdf2image import convert_from_path
    from PIL import Image, ImageEnhance, ImageFilter
    import cv2
    import numpy as np
    OCR_AVAILABLE = True
    print("✅ OCR库可用")
except ImportError as e:
    OCR_AVAILABLE = False
    print(f"❌ OCR库不可用: {e}")
    print("正在安装必要的库...")


def install_ocr_dependencies():
    """安装OCR相关依赖"""
    dependencies = [
        "pytesseract",
        "pdf2image", 
        "Pillow",
        "opencv-python",
        "numpy"
    ]
    
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ 安装成功: {dep}")
        except Exception as e:
            print(f"❌ 安装失败 {dep}: {e}")
    
    # 重新导入
    try:
        global pytesseract, convert_from_path, Image, ImageEnhance, ImageFilter, cv2, np, OCR_AVAILABLE
        import pytesseract
        from pdf2image import convert_from_path
        from PIL import Image, ImageEnhance, ImageFilter
        import cv2
        import numpy as np
        OCR_AVAILABLE = True
        print("✅ OCR库重新导入成功")
    except ImportError as e:
        print(f"❌ OCR库重新导入失败: {e}")
        OCR_AVAILABLE = False


class IndusIndTesseractParser:
    """IndusInd银行Tesseract OCR解析器"""
    
    def __init__(self):
        self.pdf_path = "../files/9-IndusInd-393762685-Statement.pdf"
        
        # IndusInd银行标准列名
        self.standard_columns = [
            'Date', 'Particulars', 'Chq./Ref. No', 'Withdrawal', 'Deposit', 'Balance'
        ]
    
    def parse_with_tesseract(self):
        """使用Tesseract OCR解析PDF"""
        print("=== IndusInd银行Tesseract OCR解析器 ===")
        print("🎯 使用Tesseract OCR + pdf2image技术方案")
        
        # 检查OCR可用性
        if not OCR_AVAILABLE:
            install_ocr_dependencies()
            if not OCR_AVAILABLE:
                print("❌ OCR环境配置失败")
                return pd.DataFrame()
        
        if not os.path.exists(self.pdf_path):
            print(f"❌ PDF文件不存在: {self.pdf_path}")
            return pd.DataFrame()
        
        try:
            print("🚀 启动Tesseract OCR解析...")
            transactions = []
            
            # 步骤1: PDF转图像
            print("📄 步骤1: 将PDF转换为图像...")
            images = convert_from_path(self.pdf_path, dpi=300)  # 高DPI提高OCR质量
            print(f"✅ 成功转换 {len(images)} 页为图像")
            
            # 步骤2: 对每页图像进行OCR
            for page_num, image in enumerate(images, 1):
                print(f"\n🔍 步骤2: OCR识别第{page_num}页...")
                
                # 图像预处理
                processed_image = self._preprocess_image(image)
                
                # OCR识别
                ocr_text = self._perform_ocr(processed_image, page_num)
                
                if ocr_text:
                    print(f"  📝 OCR文本长度: {len(ocr_text)} 字符")
                    
                    # 显示OCR结果的前几行
                    lines = [line.strip() for line in ocr_text.split('\n') if line.strip()]
                    print(f"  📋 OCR识别的前10行:")
                    for i, line in enumerate(lines[:10], 1):
                        print(f"    {i:2d}: {line}")
                    
                    # 从OCR文本中提取交易数据
                    page_transactions = self._extract_transactions_from_ocr(ocr_text, page_num)
                    if page_transactions:
                        transactions.extend(page_transactions)
                        print(f"  ✅ 第{page_num}页提取到 {len(page_transactions)} 条交易")
                        
                        # 显示提取的交易
                        for trans in page_transactions:
                            print(f"    - {trans.get('Date', '')} | {trans.get('Particulars', '')[:30]}... | ₹{trans.get('Balance', 0)}")
                    else:
                        print(f"  ❌ 第{page_num}页未找到交易数据")
                else:
                    print(f"  ❌ 第{page_num}页OCR识别失败")
                
                # 限制处理页数
                if page_num >= 8:
                    print(f"⚠️ 限制处理前8页")
                    break
            
            print(f"\n📊 Tesseract OCR处理统计:")
            print(f"  总交易记录: {len(transactions)}")
            
            if transactions:
                # 转换为DataFrame
                df = pd.DataFrame(transactions)
                df = self._format_dataframe(df)
                
                # 生成统计信息
                self._generate_statistics(df)
                
                return df
            else:
                print("❌ 未提取到任何交易数据")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"❌ Tesseract OCR解析失败: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _preprocess_image(self, image):
        """图像预处理以提高OCR质量"""
        print("    🔧 图像预处理...")
        
        try:
            # 转换为numpy数组
            img_array = np.array(image)
            
            # 转换为灰度
            if len(img_array.shape) == 3:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            else:
                gray = img_array
            
            # 1. 降噪
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # 2. 增强对比度
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # 3. 二值化
            _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 4. 形态学操作去除噪点
            kernel = np.ones((1,1), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            # 转换回PIL图像
            processed_image = Image.fromarray(cleaned)
            
            print("    ✅ 图像预处理完成")
            return processed_image
            
        except Exception as e:
            print(f"    ❌ 图像预处理失败: {e}")
            return image
    
    def _perform_ocr(self, image, page_num: int):
        """执行OCR识别"""
        try:
            # Tesseract配置
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,/()-₹ '
            
            # 执行OCR
            text = pytesseract.image_to_string(image, config=custom_config, lang='eng')
            
            # 清理OCR结果
            cleaned_text = self._clean_ocr_text(text)
            
            return cleaned_text
            
        except Exception as e:
            print(f"    ❌ OCR识别失败: {e}")
            return ""
    
    def _clean_ocr_text(self, text):
        """清理OCR识别的文本"""
        if not text:
            return ""
        
        # 移除特殊字符
        text = re.sub(r'\(cid:\d+\)', ' ', text)
        
        # 修复常见OCR错误
        ocr_fixes = {
            'UPl': 'UPI',
            'UP I': 'UPI', 
            'UP!': 'UPI',
            'Il1|': '1',
            'O0': '0',
            'S5': '5',
            'G6': '6',
            'B8': '8',
            'g9': '9',
            'Jul-2018': 'Jul-2018',
            'Aug-2018': 'Aug-2018',
            'Sep-2018': 'Sep-2018',
            'Oct-2018': 'Oct-2018'
        }
        
        for error, fix in ocr_fixes.items():
            text = text.replace(error, fix)
        
        # 替换多个空格为单个空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    def _extract_transactions_from_ocr(self, text, page_num: int):
        """从OCR文本中提取交易数据"""
        transactions = []

        try:
            # 使用日期模式分割文本，因为OCR文本是连续的
            date_pattern = r'(\d{2}-[A-Za-z]{3}-\d{4})'

            # 找到所有日期位置
            date_matches = list(re.finditer(date_pattern, text))

            if not date_matches:
                return transactions

            # 按日期分割文本
            for i, match in enumerate(date_matches):
                start_pos = match.start()

                # 确定这个交易的结束位置
                if i + 1 < len(date_matches):
                    end_pos = date_matches[i + 1].start()
                else:
                    end_pos = len(text)

                # 提取单个交易文本
                transaction_text = text[start_pos:end_pos].strip()

                # 检查是否是交易行
                if self._is_transaction_line(transaction_text):
                    transaction = self._parse_transaction_line(transaction_text, page_num, i)
                    if transaction:
                        transactions.append(transaction)
                        # 特别关注19-Jul-2018的交易
                        if '19-Jul-2018' in transaction['Date']:
                            print(f"      🔍 19-Jul交易: {transaction['Date']} | {transaction['Particulars'][:50]}... | W:{transaction['Withdrawal']} D:{transaction['Deposit']} B:{transaction['Balance']}")

        except Exception as e:
            print(f"    ❌ 交易提取失败: {e}")

        return transactions
    
    def _is_transaction_line(self, line: str) -> bool:
        """判断是否是交易行"""
        # 检查日期模式
        has_date = bool(re.search(r'\d{2}-[A-Za-z]{3}-\d{4}', line))

        # 检查交易关键词 - 放宽条件
        transaction_keywords = [
            'BroughtForward',
            'IMPS',
            'FTTO',
            'FTFROM',
            'MasterCard',
            'NFSCASH',
            'MCPOST',
            'UPI',
            'ATM',
            'Refund'
        ]

        has_transaction_keyword = any(keyword in line for keyword in transaction_keywords)

        # 检查金额模式 - 更灵活的模式
        has_amount = bool(re.search(r'\d+[,.]?\d*\.?\d{2}', line))

        # 排除页面信息
        exclude_keywords = [
            'GenerationDate',
            'Period',
            'CustomerId',
            'AccountNo',
            'AccountType',
            'Page',
            'PhoneBanking',
            'ServiceTax',
            'RegisteredofficeINDUSIND',
            'CorporateIdentityNumber'
        ]

        has_exclude = any(keyword in line for keyword in exclude_keywords)

        # 如果有日期和交易关键词，就认为是交易行
        return has_date and has_transaction_keyword and not has_exclude
    
    def _parse_transaction_line(self, line: str, page_num: int, line_idx: int):
        """解析交易行"""
        try:
            # 提取日期
            date_match = re.search(r'(\d{2}-[A-Za-z]{3}-\d{4})', line)
            if not date_match:
                return None

            date = date_match.group(1)

            # 分析OCR文本的实际模式
            print(f"      🔍 分析交易行: {line}")

            # 重新分析OCR原始文本的金额模式
            # 从调试输出可以看出，OCR文本格式是：
            # "02-Jul-2018IMPSP2A02070110003794UCBA8183 1.00 20,181.00 18962237INDUSMOB00005 ************"
            # 即：日期 + 描述 + 交易金额 + 余额 + 其他信息

            # 1. 直接从原始行中提取所有金额格式
            # 匹配标准金额格式：1,234.56 或 1234.56
            amount_pattern = r'(\d{1,2},\d{3}\.\d{2}|\d{1,4}\.\d{2})'
            all_amounts_in_line = re.findall(amount_pattern, line)

            print(f"        🔍 原始行中的所有金额: {all_amounts_in_line}")

            # 2. 过滤出真正的交易金额（排除银行卡号等）
            valid_amounts = []
            for amount_str in all_amounts_in_line:
                try:
                    clean_amount = amount_str.replace(',', '')
                    amount_val = float(clean_amount)

                    # 排除明显不是金额的数字
                    # 525622.xx 是银行卡号，4827.xx 也是
                    if (clean_amount.startswith('525622') or
                        clean_amount.startswith('4827') or
                        amount_val < 0.01 or
                        amount_val > 99999999):
                        print(f"        ❌ 排除非金额: {amount_str}")
                        continue

                    valid_amounts.append(amount_val)
                    print(f"        💰 有效金额: {amount_str} -> {amount_val}")
                except:
                    continue

            # 3. 提取描述（移除日期和金额）
            remaining_text = line.replace(date, '').strip()
            for amount_str in all_amounts_in_line:
                remaining_text = remaining_text.replace(amount_str, '').strip()

            if not valid_amounts:
                print(f"        ❌ 未找到有效金额")
                return None

            # 4. 构建交易记录
            transaction = {
                'Date': date,
                'Particulars': self._clean_particulars(remaining_text, all_amounts_in_line),
                'Chq./Ref. No': '',
                'Withdrawal': 0.0,
                'Deposit': 0.0,
                'Balance': 0.0
            }

            # 5. 基于OCR实际模式分配金额
            # 从OCR文本可以看出实际模式：
            # "02-Jul-2018IMPSP2A02070110003794UCBA8183 1.00 20,181.00"
            # "03-Jul-2018FTTOINDUSIND 5,500.00 14,681.00"

            if 'BroughtForward' in line:
                # 期初余额：只有余额
                if valid_amounts:
                    transaction['Balance'] = valid_amounts[-1]
                    print(f"        📊 期初余额: ₹{transaction['Balance']:,.2f}")
            elif len(valid_amounts) >= 2:
                # 标准交易：交易金额 + 余额
                transaction_amount = valid_amounts[0]  # 第一个是交易金额
                transaction['Balance'] = valid_amounts[-1]  # 最后一个是余额

                # 判断是收入还是支出
                if any(keyword in line.upper() for keyword in ['FTFROM', 'CREDIT', 'CR', 'REFUND', 'UPI']):
                    transaction['Deposit'] = transaction_amount
                    print(f"        💰 收入: ₹{transaction['Deposit']:,.2f}, 余额: ₹{transaction['Balance']:,.2f}")
                else:
                    transaction['Withdrawal'] = transaction_amount
                    print(f"        💰 支出: ₹{transaction['Withdrawal']:,.2f}, 余额: ₹{transaction['Balance']:,.2f}")
            elif len(valid_amounts) == 1:
                # 只有一个金额，可能是余额或特殊情况
                transaction['Balance'] = valid_amounts[0]
                print(f"        📊 仅余额: ₹{transaction['Balance']:,.2f}")

            return transaction

        except Exception as e:
            print(f"      ⚠️ 交易行解析失败: {e}")
            return None

    def _clean_particulars(self, text: str, amount_strings: list) -> str:
        """清理交易描述"""
        # 移除金额字符串
        cleaned = text
        for amount_str in amount_strings:
            cleaned = cleaned.replace(amount_str, '')

        # 移除多余的空格和符号
        cleaned = re.sub(r'\s+', ' ', cleaned)
        cleaned = cleaned.strip(' /')

        return cleaned
    
    def _format_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """格式化DataFrame"""
        try:
            # 确保列顺序
            df = df[self.standard_columns]
            
            # 不进行日期排序，保持OCR识别的原始顺序
            print(f"    ✅ 保持OCR原始顺序，共{len(df)}条记录")
            
            return df
            
        except Exception as e:
            print(f"❌ DataFrame格式化失败: {e}")
            return df


    
    def _generate_statistics(self, df: pd.DataFrame):
        """生成统计信息"""
        print(f"\n📊 交易统计:")
        
        try:
            if len(df) == 0:
                print(f"  📊 无交易记录")
                return
            
            total_withdrawals = df['Withdrawal'].sum()
            total_deposits = df['Deposit'].sum()
            
            withdrawal_count = (df['Withdrawal'] > 0).sum()
            deposit_count = (df['Deposit'] > 0).sum()
            
            print(f"  💰 总支出: ₹{total_withdrawals:,.2f} ({withdrawal_count} 笔)")
            print(f"  💰 总收入: ₹{total_deposits:,.2f} ({deposit_count} 笔)")
            print(f"  💰 净变化: ₹{total_deposits - total_withdrawals:,.2f}")
            
            if len(df) > 0:
                opening_balance = df.iloc[0]['Balance'] - df.iloc[0]['Deposit'] + df.iloc[0]['Withdrawal']
                closing_balance = df.iloc[-1]['Balance']
                print(f"  💰 期初余额: ₹{opening_balance:,.2f}")
                print(f"  💰 期末余额: ₹{closing_balance:,.2f}")
                    
        except Exception as e:
            print(f"    ⚠️ 统计生成失败: {e}")
    
    def save_results(self, df: pd.DataFrame) -> str:
        """保存解析结果"""
        if df.empty:
            print("❌ 没有数据可保存")
            return ""
        
        # 保存为CSV
        csv_file = "indusind_tesseract_extracted.csv"
        
        try:
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"\n📁 Tesseract解析结果已保存: {csv_file}")
            return csv_file
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return ""


def main():
    """主函数"""
    parser = IndusIndTesseractParser()
    
    print("🚀 启动IndusInd银行Tesseract OCR解析器")
    print("📋 使用Tesseract OCR + pdf2image技术方案")
    
    df = parser.parse_with_tesseract()
    
    if not df.empty:
        output_file = parser.save_results(df)
        
        print(f"\n🎉 Tesseract OCR解析完成!")
        print(f"📊 提取记录数: {len(df)} 条")
        print(f"📁 输出文件: {output_file}")
        
        # 显示前几条记录
        print(f"\n📋 前5条记录预览:")
        for i in range(min(5, len(df))):
            row = df.iloc[i]
            w = f"₹{row['Withdrawal']:,.2f}" if row['Withdrawal'] > 0 else ""
            d = f"₹{row['Deposit']:,.2f}" if row['Deposit'] > 0 else ""
            print(f"  {i+1}. {row['Date']} | {row['Particulars'][:25]}... | W:{w} D:{d} | ₹{row['Balance']:,.2f}")
        
        return df
    else:
        print(f"\n❌ Tesseract OCR解析失败")
        return None


if __name__ == "__main__":
    main()
