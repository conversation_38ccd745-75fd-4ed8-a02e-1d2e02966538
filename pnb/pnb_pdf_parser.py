#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PNB (Punjab National Bank) 银行对账单PDF解析器
基于Canara银行解析器的成功模式，针对PNB银行的表格结构优化
采用Tabula主导的解析策略
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class PNBBankPDFParser:
    """PNB银行PDF解析器 - Tabula主导策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Transaction Date', 'Cheque Number', 'Withdrawal', 'Deposit', 'Balance', 'Narration'
        ]
        self.standard_columns = [
            'Date', 'Particulars', 'Deposits', 'Withdrawals', 'Balance'
        ]
        
    def parse_pnb_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析PNB银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"PNB银行PDF解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：使用Tabula提取表格数据（PNB表格结构良好）
            print("\n🔄 第一步：使用Tabula提取表格数据...")
            df_tabula = self._extract_tabula_data(pdf_path)
            
            if df_tabula.empty:
                print("❌ Tabula提取失败，尝试PyPDF备选方案...")
                # 备选方案：使用PyPDF
                df_pypdf = self._extract_pypdf_data(pdf_path)
                if df_pypdf.empty:
                    print("❌ PyPDF提取也失败")
                    return pd.DataFrame()
                df_final = df_pypdf
            else:
                df_final = df_tabula
            
            # 第二步：数据清洗和标准化
            print("\n🔄 第二步：数据清洗和标准化...")
            df_final = self._clean_and_standardize_data(df_final)
            
            # 第三步：数据验证
            print("\n🔄 第三步：数据验证...")
            df_final = self._validate_data(df_final)
            
            print(f"\n✅ PNB银行PDF解析完成！提取交易数: {len(df_final)} 条")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_tabula_data(self, pdf_path: str) -> pd.DataFrame:
        """使用Tabula提取表格数据"""
        try:
            # PNB银行表格结构良好，使用lattice模式
            print("  📋 使用Tabula lattice模式提取表格...")
            dfs = tabula.read_pdf(pdf_path, pages='all', lattice=True)
            print(f"  📄 找到 {len(dfs)} 个表格")

            if not dfs:
                print("  ❌ 未找到任何表格")
                return pd.DataFrame()

            # 合并所有页面的表格
            all_transactions = []

            for i, df in enumerate(dfs):
                print(f"  📊 处理表格 {i+1}: 形状 {df.shape}")

                # 清理表格数据
                df_cleaned = self._clean_tabula_table(df, i+1)

                if not df_cleaned.empty:
                    all_transactions.append(df_cleaned)
                    print(f"    ✅ 提取到 {len(df_cleaned)} 条有效交易")
                else:
                    print(f"    ⚠️ 表格 {i+1} 没有有效数据")

            if not all_transactions:
                print("  ❌ 所有表格都没有有效数据")
                return pd.DataFrame()

            # 合并所有交易数据
            df_combined = pd.concat(all_transactions, ignore_index=True)
            print(f"  ✅ 合并后总计 {len(df_combined)} 条交易")

            return df_combined

        except Exception as e:
            print(f"  ❌ Tabula提取失败: {e}")
            return pd.DataFrame()

    def _clean_tabula_table(self, df: pd.DataFrame, table_num: int) -> pd.DataFrame:
        """清理单个tabula表格"""
        if df.empty:
            return pd.DataFrame()

        # 移除空行
        df = df.dropna(how='all')

        # 过滤有效的交易行
        valid_rows = []
        for i, row in df.iterrows():
            # 检查是否有日期
            date_val = row.iloc[0] if len(row) > 0 else None
            
            # 检查是否有金额数据（Withdrawal或Deposit）
            has_amount = False
            if len(row) >= 4:  # 至少要有4列
                withdrawal = row.iloc[2] if len(row) > 2 else None
                deposit = row.iloc[3] if len(row) > 3 else None
                
                if (pd.notna(withdrawal) and str(withdrawal).strip()) or \
                   (pd.notna(deposit) and str(deposit).strip()):
                    has_amount = True

            # 验证日期格式
            if pd.notna(date_val) and self._is_valid_date(date_val) and has_amount:
                valid_rows.append(row)

        if valid_rows:
            df_valid = pd.DataFrame(valid_rows)
            df_valid.reset_index(drop=True, inplace=True)
            return df_valid
        else:
            return pd.DataFrame()

    def _is_valid_date(self, value) -> bool:
        """检查是否为有效的日期格式"""
        if pd.isna(value):
            return False

        date_str = str(value).strip()
        # PNB银行日期格式: DD/MM/YYYY
        return bool(re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_str))

    def _extract_pypdf_data(self, pdf_path: str) -> pd.DataFrame:
        """使用PyPDF提取数据（备选方案）"""
        try:
            print("  📖 使用PyPDF提取文本数据...")
            
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                print(f"  📄 读取 {len(reader.pages)} 页")

                all_transactions = []

                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    text = page.extract_text()

                    print(f"  📄 处理第 {page_num + 1} 页...")
                    page_transactions = self._parse_pypdf_page(text, page_num + 1)

                    if page_transactions:
                        all_transactions.extend(page_transactions)
                        print(f"    ✅ 提取到 {len(page_transactions)} 条交易")

                if all_transactions:
                    df = pd.DataFrame(all_transactions)
                    print(f"  ✅ PyPDF总计提取 {len(df)} 条交易")
                    return df
                else:
                    print("  ❌ PyPDF未提取到任何交易")
                    return pd.DataFrame()

        except Exception as e:
            print(f"  ❌ PyPDF提取失败: {e}")
            return pd.DataFrame()

    def _parse_pypdf_page(self, text: str, page_num: int) -> List[Dict]:
        """解析PyPDF页面文本"""
        transactions = []
        lines = text.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # PNB格式：日期 金额 余额 描述
            # 例如：28/04/2023 110.00 570.53 Cr.UPI/311821144928/P2M/paytmqr28
            match = self._parse_transaction_line(line)
            if match:
                transactions.append(match)

        return transactions

    def _parse_transaction_line(self, line: str) -> Optional[Dict]:
        """解析单行交易数据"""
        # PNB交易行模式：日期 + 金额 + 余额 + 描述
        pattern = r'^(\d{1,2}/\d{1,2}/\d{4})\s+([\d,]+\.\d{2})\s+([\d,]+\.\d{2})\s+Cr\.(.+)$'
        match = re.match(pattern, line)

        if match:
            date = match.group(1)
            amount = match.group(2)
            balance = match.group(3)
            description = match.group(4).strip()

            return {
                'Transaction Date': date,
                'Cheque Number': None,
                'Withdrawal': None,  # 需要根据描述判断
                'Deposit': amount,   # 默认为存款，后续调整
                'Balance': balance + ' Cr.',
                'Narration': description
            }

        return None

    def _clean_and_standardize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗和标准化数据"""
        if df.empty:
            return df

        print("  🧹 清洗和标准化数据...")

        # 确保列名正确
        if len(df.columns) >= 6:
            df.columns = self.expected_columns

        # 清洗日期列
        df = self._clean_date_column(df)

        # 清洗金额列
        df = self._clean_amount_columns(df)

        # 清洗余额列
        df = self._clean_balance_column(df)

        # 清洗描述列
        df = self._clean_narration_column(df)

        # 转换为标准格式
        df = self._convert_to_standard_format(df)

        return df

    def _clean_date_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗日期列"""
        if 'Transaction Date' in df.columns:
            print("    📅 清洗日期列...")
            df['Transaction Date'] = df['Transaction Date'].apply(self._parse_date)
        return df

    def _parse_date(self, value) -> str:
        """解析日期字符串"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()

        # PNB银行日期格式: DD/MM/YYYY
        if re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_str):
            try:
                # 转换为标准格式 DD-MM-YYYY
                date_obj = datetime.strptime(date_str, '%d/%m/%Y')
                return date_obj.strftime('%d-%m-%Y')
            except ValueError:
                return date_str

        return date_str

    def _clean_amount_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗金额列"""
        amount_columns = ['Withdrawal', 'Deposit']

        for col in amount_columns:
            if col in df.columns:
                print(f"    💰 清洗 {col} 列...")
                df[col] = df[col].apply(self._parse_amount)

        return df

    def _parse_amount(self, value) -> Optional[float]:
        """解析金额字符串为浮点数"""
        if pd.isna(value) or value == '' or str(value).strip() == '':
            return None

        try:
            # 转换为字符串并清理
            amount_str = str(value).strip()

            # 移除货币符号和空格
            amount_str = re.sub(r'[₹\s]', '', amount_str)

            # 移除逗号分隔符
            amount_str = re.sub(r',', '', amount_str)

            # 处理负数（可能用括号表示或负号）
            is_negative = '(' in amount_str and ')' in amount_str
            amount_str = re.sub(r'[()]', '', amount_str)

            # 只保留数字和小数点
            amount_str = re.sub(r'[^\d.]', '', amount_str)

            if not amount_str:
                return None

            # 转换为浮点数
            amount = float(amount_str)

            # 处理负数
            if is_negative:
                amount = -amount

            return amount

        except (ValueError, TypeError):
            return None

    def _clean_balance_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗余额列"""
        if 'Balance' in df.columns:
            print("    💳 清洗余额列...")
            df['Balance'] = df['Balance'].apply(self._parse_balance)
        return df

    def _parse_balance(self, value) -> Optional[float]:
        """解析余额字符串"""
        if pd.isna(value) or value == '':
            return None

        try:
            balance_str = str(value).strip()

            # PNB格式：570.53 Cr. 或 570.53 Dr.
            # 移除 Cr. 或 Dr. 标识
            is_credit = 'Cr.' in balance_str or 'Cr' in balance_str
            is_debit = 'Dr.' in balance_str or 'Dr' in balance_str

            # 清理文本
            balance_str = re.sub(r'(Cr\.?|Dr\.?)', '', balance_str).strip()

            # 移除逗号
            balance_str = re.sub(r',', '', balance_str)

            # 只保留数字和小数点
            balance_str = re.sub(r'[^\d.]', '', balance_str)

            if not balance_str:
                return None

            balance = float(balance_str)

            # PNB银行：Cr.表示正余额，Dr.表示负余额
            if is_debit:
                balance = -balance

            return balance

        except (ValueError, TypeError):
            return None

    def _clean_narration_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗描述列"""
        if 'Narration' in df.columns:
            print("    📝 清洗描述列...")
            df['Narration'] = df['Narration'].apply(self._clean_narration_text)
        return df

    def _clean_narration_text(self, value) -> str:
        """清洗描述文本"""
        if pd.isna(value):
            return ""

        text = str(value).strip()

        # 移除换行符，用空格替换
        text = re.sub(r'\n+', ' ', text)
        text = re.sub(r'\r+', ' ', text)
        text = re.sub(r'\t+', ' ', text)

        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def _convert_to_standard_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """转换为标准格式"""
        print("    🔄 转换为标准格式...")

        # 创建标准格式的DataFrame
        standard_df = pd.DataFrame()

        # 映射列
        if 'Transaction Date' in df.columns:
            standard_df['Date'] = df['Transaction Date']

        if 'Narration' in df.columns:
            standard_df['Particulars'] = df['Narration']

        if 'Deposit' in df.columns:
            standard_df['Deposits'] = df['Deposit']

        if 'Withdrawal' in df.columns:
            standard_df['Withdrawals'] = df['Withdrawal']

        if 'Balance' in df.columns:
            standard_df['Balance'] = df['Balance']

        return standard_df

    def _validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据完整性"""
        if df.empty:
            return df

        print("    ✅ 验证数据完整性...")

        # 移除没有日期的行
        before_count = len(df)
        df = df[df['Date'].notna() & (df['Date'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效日期的记录")

        # 确保至少有一个金额列有值
        df = df[
            df['Deposits'].notna() |
            df['Withdrawals'].notna() |
            df['Balance'].notna()
        ]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "pnb_extracted") -> Tuple[str, str, str]:
        """
        保存解析结果为多种格式

        Args:
            df: 解析后的DataFrame
            output_base: 输出文件名前缀

        Returns:
            Tuple[str, str, str]: CSV、JSON、Excel文件路径
        """
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 在保存前进行最终的文本清理
        df_clean = df.copy()
        if 'Particulars' in df_clean.columns:
            df_clean['Particulars'] = df_clean['Particulars'].apply(
                lambda x: self._clean_narration_text(str(x)) if pd.notna(x) else ""
            )

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df_clean.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df_clean.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df_clean.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 PNB银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 金额统计
        withdrawal_count = df['Withdrawals'].notna().sum()
        deposit_count = df['Deposits'].notna().sum()

        print(f"取款交易: {withdrawal_count} 笔")
        print(f"存款交易: {deposit_count} 笔")

        # 计算金额总计
        total_withdrawals = df['Withdrawals'].sum()
        total_deposits = df['Deposits'].sum()

        print(f"\n💰 金额统计:")
        print(f"  总取款金额: ₹{total_withdrawals:,.2f}")
        print(f"  总存款金额: ₹{total_deposits:,.2f}")
        print(f"  净变化: ₹{total_deposits - total_withdrawals:,.2f}")

        # 数据完整性检查
        missing_dates = df['Date'].isna().sum()
        missing_particulars = df['Particulars'].isna().sum()
        missing_balances = df['Balance'].isna().sum()

        print(f"\n📋 数据完整性:")
        print(f"  缺失日期: {missing_dates} 条")
        print(f"  缺失描述: {missing_particulars} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额连续性检查
        self._check_balance_continuity(df)

        # 日期范围
        if not df['Date'].isna().all():
            valid_dates = df[df['Date'].notna() & (df['Date'] != '')]
            if not valid_dates.empty:
                print(f"\n📅 日期范围:")
                print(f"  最早交易: {valid_dates['Date'].iloc[0]}")
                print(f"  最晚交易: {valid_dates['Date'].iloc[-1]}")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            withdrawal = f"₹{row['Withdrawals']:,.2f}" if pd.notna(row['Withdrawals']) else "-"
            deposit = f"₹{row['Deposits']:,.2f}" if pd.notna(row['Deposits']) else "-"
            balance = f"₹{row['Balance']:,.2f}" if pd.notna(row['Balance']) else "-"

            print(f"  {i+1}. {row['Date']} | {str(row['Particulars'])[:30]}...")
            print(f"     取款: {withdrawal} | 存款: {deposit} | 余额: {balance}")

    def _check_balance_continuity(self, df: pd.DataFrame) -> None:
        """检查余额连续性"""
        print(f"\n🔍 余额连续性检查:")

        balance_col = 'Balance'
        if balance_col not in df.columns:
            print("  ⚠️ 没有余额列")
            return

        # 检查余额是否连续
        valid_balances = df[df[balance_col].notna()]

        if len(valid_balances) < 2:
            print("  ⚠️ 余额数据不足，无法检查连续性")
            return

        # 检查首末余额
        first_balance = valid_balances.iloc[0][balance_col]
        last_balance = valid_balances.iloc[-1][balance_col]

        print(f"  期初余额: ₹{first_balance:,.2f}")
        print(f"  期末余额: ₹{last_balance:,.2f}")
        print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")


def main():
    """主函数"""
    parser = PNBBankPDFParser()

    pdf_path = "../files/6-pnb-*********-PNB-BANK-STATEMENT.pdf"

    print("🚀 启动PNB银行PDF解析器")

    # 解析PDF
    df = parser.parse_pnb_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 PNB银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ PNB银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
