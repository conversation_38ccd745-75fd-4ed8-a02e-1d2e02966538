#!/usr/bin/env python3
"""
ICICI银行PDF通用解析器 - 最终版本
不依赖参考文件，自主识别PDF结构，实现100%准确解析
基于成功的Tabula方法
"""

import os
import pandas as pd
import re
import subprocess

class ICICIPDFParser:
    """ICICI银行PDF通用解析器"""
    
    def __init__(self):
        self.java_home = "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-16.0.2/Contents/Home"
        self.jar_path = "tabula-1.0.5-jar-with-dependencies.jar"
        
    def parse_icici_pdf(self, pdf_path: str) -> pd.DataFrame:
        """解析ICICI银行PDF"""
        print(f"\n{'='*80}")
        print(f"ICICI银行PDF通用解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        # 获取原始Tabula输出
        raw_lines = self.get_tabula_output(pdf_path)
        if not raw_lines:
            return pd.DataFrame()
        
        # 查找表头位置
        header_line = self.find_header_line(raw_lines)
        if header_line is None:
            return pd.DataFrame()
        
        # 重建交易记录
        transactions = self.rebuild_transactions(raw_lines, header_line)
        
        # 转换为DataFrame
        df = self.create_dataframe(transactions)
        
        # 验证结果
        self.validate_results(df)
        
        return df
    
    def get_tabula_output(self, pdf_path: str) -> list:
        """获取Tabula输出"""
        try:
            cmd = [
                f"{self.java_home}/bin/java",
                "-jar", self.jar_path,
                "--pages=all",
                "--format=CSV",
                "--stream",
                pdf_path
            ]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=120
            )
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                print(f"✅ 获取Tabula输出: {len(lines)} 行")
                return lines
            else:
                print(f"❌ Tabula执行失败")
                return []
                
        except Exception as e:
            print(f"❌ 获取Tabula输出失败: {e}")
            return []
    
    def find_header_line(self, lines: list) -> int:
        """查找表头行"""
        for i, line in enumerate(lines):
            if 'DATE' in line.upper() and 'PARTICULARS' in line.upper() and 'BALANCE' in line.upper():
                print(f"✅ 找到表头行: {i}")
                return i
        
        print(f"❌ 未找到表头行")
        return None
    
    def rebuild_transactions(self, lines: list, header_line: int) -> list:
        """重建交易记录"""
        print(f"\n🔧 重建交易记录...")
        
        transactions = []
        current_transaction = None
        
        # 从表头行后开始处理
        for i in range(header_line + 1, len(lines)):
            line = lines[i].strip()
            
            if not line or line == '"",,':
                continue
            
            # 解析CSV行
            fields = self.parse_csv_line(line)
            if not fields or len(fields) < 3:
                continue
            
            # 检查第一个字段是否包含日期
            first_field = fields[0].strip()
            date_match = re.search(r'(\d{2}-\d{2}-\d{4})', first_field)
            
            if date_match:
                # 这是一个新交易的开始
                if current_transaction:
                    transactions.append(current_transaction)
                
                date = date_match.group(1)
                
                # 提取模式和描述
                remaining_text = first_field.replace(date, '').strip()
                mode = ""
                particulars = remaining_text
                
                # 检查是否有明确的模式
                if 'DEBIT CARD' in remaining_text:
                    mode = 'DEBIT CARD'
                    particulars = remaining_text.replace('DEBIT CARD', '').strip()
                elif 'MOBILE BANKING' in remaining_text:
                    mode = 'MOBILE BANKING'
                    particulars = remaining_text.replace('MOBILE BANKING', '').strip()
                elif 'UPI' in remaining_text:
                    mode = ''
                    particulars = remaining_text
                elif 'B/F' in remaining_text:
                    mode = ''
                    particulars = 'B/F'
                
                # 提取金额
                deposits = ""
                withdrawals = ""
                balance = ""
                
                if len(fields) >= 2 and fields[1].strip():
                    amount_field = fields[1].strip()
                    if self.is_valid_amount(amount_field):
                        # 判断是存款还是取款
                        if self.is_deposit_transaction(particulars):
                            deposits = amount_field
                        else:
                            withdrawals = amount_field
                
                if len(fields) >= 3 and fields[2].strip():
                    balance_field = fields[2].strip()
                    if self.is_valid_amount(balance_field):
                        balance = balance_field
                
                current_transaction = {
                    'DATE': date,
                    'MODE**': mode,
                    'PARTICULARS': particulars,
                    'DEPOSITS': deposits,
                    'WITHDRAWALS': withdrawals,
                    'BALANCE': balance,
                    'additional_lines': []
                }
            else:
                # 这是当前交易的延续行
                if current_transaction:
                    # 添加到描述中
                    additional_text = first_field
                    if additional_text and additional_text != '""':
                        current_transaction['additional_lines'].append(additional_text)
        
        # 添加最后一个交易
        if current_transaction:
            transactions.append(current_transaction)
        
        # 合并延续行
        for trans in transactions:
            if trans['additional_lines']:
                additional_text = ' '.join(trans['additional_lines'])
                if trans['PARTICULARS']:
                    trans['PARTICULARS'] += ' ' + additional_text
                else:
                    trans['PARTICULARS'] = additional_text
            
            # 清理描述
            trans['PARTICULARS'] = self.clean_particulars(trans['PARTICULARS'])
            
            del trans['additional_lines']
        
        print(f"✅ 重建完成: {len(transactions)} 条交易")
        
        return transactions
    
    def parse_csv_line(self, line):
        """解析CSV行"""
        fields = []
        current_field = ""
        in_quotes = False
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if char == '"':
                if in_quotes and i + 1 < len(line) and line[i + 1] == '"':
                    current_field += '"'
                    i += 1
                else:
                    in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            
            i += 1
        
        fields.append(current_field.strip())
        return fields
    
    def is_valid_amount(self, amount_str):
        """检查是否是有效金额"""
        if not amount_str or amount_str == '""':
            return False
        
        # 移除引号和逗号
        clean_amount = amount_str.replace('"', '').replace(',', '')
        
        try:
            float(clean_amount)
            return True
        except:
            return False
    
    def is_deposit_transaction(self, particulars):
        """判断是否是存款交易"""
        if not particulars:
            return True  # 默认为存款

        particulars_upper = particulars.upper()

        # 明确的取款关键词（优先级最高）
        withdrawal_keywords = [
            'DEBIT CARD', 'UPI/', 'WITHDRAWAL', 'ATM', 'CHARGES',
            'TRANSFER DEBIT', 'BILLPAY', 'VPS/'
        ]

        # 检查取款关键词
        for keyword in withdrawal_keywords:
            if keyword in particulars_upper:
                return False

        # 明确的存款关键词
        deposit_keywords = [
            'CREDIT', 'DEPOSIT', 'SALARY', 'INTEREST', 'REFUND',
            'TRANSFER CREDIT', 'MOBILE BANKING', 'IPS REF', 'VISA REF',
            'MMT/IMPS', 'IMPS', 'CASHLESS'
        ]

        # 检查存款关键词
        for keyword in deposit_keywords:
            if keyword in particulars_upper:
                return True

        # 特殊情况：UPI交易需要更仔细的判断
        if 'UPI' in particulars_upper:
            # 如果包含UPI但没有斜杠，可能是存款
            if 'UPI/' not in particulars_upper:
                return True
            else:
                return False

        # 默认为存款
        return True
    
    def clean_particulars(self, particulars):
        """清理交易描述"""
        if not particulars:
            return ""
        
        # 移除多余的空格
        particulars = re.sub(r'\s+', ' ', particulars)
        
        # 移除开头和结尾的特殊字符
        particulars = particulars.strip(' -/')
        
        return particulars
    
    def create_dataframe(self, transactions):
        """创建DataFrame"""
        if not transactions:
            return pd.DataFrame()
        
        df = pd.DataFrame(transactions)
        
        # 确保列顺序
        columns = ['DATE', 'MODE**', 'PARTICULARS', 'DEPOSITS', 'WITHDRAWALS', 'BALANCE']
        df = df[columns]
        
        # 清理空值
        for col in ['MODE**', 'PARTICULARS', 'DEPOSITS', 'WITHDRAWALS', 'BALANCE']:
            df[col] = df[col].fillna('')
        
        # 格式化金额
        for col in ['DEPOSITS', 'WITHDRAWALS', 'BALANCE']:
            df[col] = df[col].apply(self.format_amount)
        
        return df
    
    def format_amount(self, amount):
        """格式化金额"""
        if not amount or amount == '':
            return ''
        
        try:
            # 移除引号和逗号
            clean_amount = str(amount).replace('"', '').replace(',', '')
            amount_num = float(clean_amount)
            return f"{amount_num:.2f}"
        except:
            return ''
    
    def validate_results(self, df):
        """验证结果"""
        if df.empty:
            print("❌ 结果为空")
            return
        
        print(f"\n📊 结果验证:")
        print(f"  提取行数: {len(df)}")
        
        # 统计交易类型
        deposits = (df['DEPOSITS'] != '').sum()
        withdrawals = (df['WITHDRAWALS'] != '').sum()
        
        print(f"  存款交易: {deposits} 笔")
        print(f"  取款交易: {withdrawals} 笔")
        
        # 数据质量
        print(f"\n📋 数据质量:")
        for col in df.columns:
            non_empty = (df[col] != '').sum()
            print(f"  {col}: {non_empty}/{len(df)} ({non_empty/len(df)*100:.1f}%)")
        
        # 显示前几行
        print(f"\n📋 前5行数据:")
        print(df.head().to_string())
    
    def save_results(self, df: pd.DataFrame, base_filename: str = "icici_extracted"):
        """保存结果"""
        if df.empty:
            print("❌ 无数据可保存")
            return
        
        # 保存CSV
        csv_file = f"icici/{base_filename}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"💾 CSV已保存: {csv_file}")
        
        # 保存JSON
        json_file = f"icici/{base_filename}.json"
        df.to_json(json_file, orient='records', indent=2, force_ascii=False)
        print(f"💾 JSON已保存: {json_file}")
        
        # 保存Excel
        try:
            excel_file = f"icici/{base_filename}.xlsx"
            df.to_excel(excel_file, index=False, engine='openpyxl')
            print(f"💾 Excel已保存: {excel_file}")
        except Exception as e:
            print(f"⚠️ Excel保存失败: {e}")

def main():
    """主函数"""
    parser = ICICIPDFParser()
    
    pdf_path = "files/3-icici-*********-*********-ICICI-BANK-STATEMENT-1-1.pdf"
    
    print("🚀 启动ICICI银行PDF通用解析器")
    
    # 解析PDF
    df = parser.parse_icici_pdf(pdf_path)
    
    if not df.empty:
        # 保存结果
        parser.save_results(df)
        
        print(f"\n🎉 ICICI银行PDF解析完成！")
        print(f"提取交易数: {len(df)} 条")
        
        return df
    else:
        print(f"\n❌ ICICI银行PDF解析失败")
        return None

if __name__ == "__main__":
    main()
