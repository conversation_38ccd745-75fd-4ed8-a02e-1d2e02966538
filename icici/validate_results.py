#!/usr/bin/env python3
"""
验证ICICI银行PDF解析结果
"""

import pandas as pd

def validate_results():
    """验证解析结果"""
    print(f"\n{'='*80}")
    print("ICICI银行PDF解析结果验证")
    print(f"{'='*80}")
    
    # 加载数据
    try:
        ref_df = pd.read_csv("icici/icici_check.csv")
        result_df = pd.read_csv("icici/icici_extracted.csv")
        
        print(f"✅ 数据加载成功")
        print(f"  参考文件: {len(ref_df)} 行")
        print(f"  解析结果: {len(result_df)} 行")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False
    
    # 验证结构
    row_match = len(ref_df) == len(result_df)
    col_match = list(ref_df.columns) == list(result_df.columns)
    
    print(f"\n📊 结构验证:")
    print(f"  行数匹配: {'✅' if row_match else '❌'}")
    print(f"  列名匹配: {'✅' if col_match else '❌'}")
    
    # 验证交易统计
    ref_deposits = (ref_df['DEPOSITS'].notna() & (ref_df['DEPOSITS'] != "")).sum()
    ref_withdrawals = (ref_df['WITHDRAWALS'].notna() & (ref_df['WITHDRAWALS'] != "")).sum()
    
    result_deposits = (result_df['DEPOSITS'].notna() & (result_df['DEPOSITS'] != "")).sum()
    result_withdrawals = (result_df['WITHDRAWALS'].notna() & (result_df['WITHDRAWALS'] != "")).sum()
    
    print(f"\n💰 交易统计:")
    print(f"  存款交易: {result_deposits}/{ref_deposits} {'✅' if ref_deposits == result_deposits else '❌'}")
    print(f"  取款交易: {result_withdrawals}/{ref_withdrawals} {'✅' if ref_withdrawals == result_withdrawals else '❌'}")
    
    # 计算匹配度
    total_matches = 0
    total_rows = min(len(ref_df), len(result_df))
    
    for i in range(total_rows):
        ref_row = ref_df.iloc[i]
        result_row = result_df.iloc[i]
        
        date_match = str(ref_row['DATE']) == str(result_row['DATE'])
        deposits_match = normalize_amount(ref_row['DEPOSITS']) == normalize_amount(result_row['DEPOSITS'])
        withdrawals_match = normalize_amount(ref_row['WITHDRAWALS']) == normalize_amount(result_row['WITHDRAWALS'])
        balance_match = normalize_amount(ref_row['BALANCE']) == normalize_amount(result_row['BALANCE'])
        
        if date_match and deposits_match and withdrawals_match and balance_match:
            total_matches += 1
    
    match_percentage = total_matches / total_rows * 100 if total_rows > 0 else 0
    
    print(f"\n📈 匹配度:")
    print(f"  内容匹配: {match_percentage:.1f}% ({total_matches}/{total_rows})")
    
    # 最终评估
    if match_percentage == 100.0 and row_match and col_match:
        print(f"\n🎉 验证结果: 100%完美匹配！")
        print(f"🏆 评级: A+ (完美)")
        return True
    else:
        print(f"\n⚠️ 验证结果: 需要改进")
        print(f"📊 评级: 需要优化")
        return False

def normalize_amount(value):
    """标准化金额"""
    if pd.isna(value) or value == "" or str(value).strip() == "":
        return ""
    
    try:
        amount_num = float(str(value).replace(',', ''))
        return f"{amount_num:.2f}"
    except:
        return str(value).strip()

def main():
    """主函数"""
    success = validate_results()
    
    print(f"\n{'='*80}")
    if success:
        print(f"🎊 ICICI银行PDF解析验证成功！")
        print(f"✅ 解析器已准备就绪，可用于生产环境")
    else:
        print(f"⚠️ ICICI银行PDF解析需要进一步优化")
    print(f"{'='*80}")
    
    return success

if __name__ == "__main__":
    main()
