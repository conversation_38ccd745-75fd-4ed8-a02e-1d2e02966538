#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IOB (Indian Overseas Bank) 银行对账单PDF解析器
基于BOI的混合解析策略，结合tabula和pypdf解决NARATION列跨行问题
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class IOBPDFParser:
    """IOB银行PDF解析器 - 混合解析策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'DATE', 'CHQ NO', 'NARATION', 'COD', 'DEBIT', 'CREDIT', 'BALANCE'
        ]
        
    def parse_iob_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析IOB银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"IOB银行PDF混合解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：使用Tabula提取表格结构和金额数据
            print("\n🔄 第一步：使用Tabula提取表格结构...")
            df_structured = self._extract_tabula_structure(pdf_path)
            
            if df_structured.empty:
                print("❌ Tabula提取失败")
                return pd.DataFrame()
            
            # 第二步：使用PyPDF提取完整描述文本
            print("\n🔄 第二步：使用PyPDF提取完整描述文本...")
            pypdf_data = self._extract_pypdf_descriptions(pdf_path)
            
            if not pypdf_data:
                print("❌ PyPDF提取失败")
                return df_structured  # 返回tabula结果作为备选
            
            # 第三步：合并数据，修复NARATION列
            print("\n🔄 第三步：合并数据，修复NARATION列...")
            df_final = self._merge_and_fix_data(df_structured, pypdf_data)
            
            # 第四步：数据验证和清理
            print("\n🔄 第四步：数据验证和清理...")
            df_final = self._validate_and_clean_data(df_final)
            
            print(f"\n✅ IOB银行PDF解析完成！提取交易数: {len(df_final)} 条")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_tabula_structure(self, pdf_path: str) -> pd.DataFrame:
        """使用Tabula提取表格结构和金额数据"""
        try:
            # 使用lattice=True模式提取表格
            dfs = tabula.read_pdf(pdf_path, pages='all', lattice=True)
            print(f"  📋 Tabula找到 {len(dfs)} 个表格")

            if not dfs:
                print("  ❌ 未找到任何表格")
                return pd.DataFrame()

            # 合并所有页面的表格
            all_transactions = []

            for i, df in enumerate(dfs):
                print(f"  📄 处理表格 {i+1}: 形状 {df.shape}")

                # 清理表格数据
                df_cleaned = self._clean_tabula_table(df, i+1)

                if not df_cleaned.empty:
                    all_transactions.append(df_cleaned)
                    print(f"    ✅ 提取到 {len(df_cleaned)} 条有效交易")
                else:
                    print(f"    ⚠️ 表格 {i+1} 没有有效数据")

            if not all_transactions:
                print("  ❌ 所有表格都没有有效数据")
                return pd.DataFrame()

            # 标准化每个表格的列名后再合并
            standardized_transactions = []
            for df_trans in all_transactions:
                df_std = self._standardize_columns(df_trans)
                standardized_transactions.append(df_std)

            # 合并所有交易数据
            df_combined = pd.concat(standardized_transactions, ignore_index=True)
            print(f"  ✅ 合并后总计 {len(df_combined)} 条交易")

            return df_combined

        except Exception as e:
            print(f"  ❌ Tabula提取失败: {e}")
            return pd.DataFrame()

    def _clean_tabula_table(self, df: pd.DataFrame, table_num: int) -> pd.DataFrame:
        """清理单个tabula表格"""
        if df.empty:
            return pd.DataFrame()

        # 查找并移除表头行
        header_row_idx = None
        for i, row in df.iterrows():
            row_str = ' '.join(str(val) for val in row.values if pd.notna(val))
            if 'DATE' in row_str and 'NARATION' in row_str and 'BALANCE' in row_str:
                header_row_idx = i
                break

        if header_row_idx is not None:
            # 移除表头行及其之前的所有行
            df = df.iloc[header_row_idx + 1:].copy()
            print(f"    🧹 移除表头行 {header_row_idx} 及之前的行")

        # 移除空行和无效行
        df = df.dropna(how='all')

        # 过滤有效的交易行（必须有DATE和金额信息）
        valid_rows = []
        for i, row in df.iterrows():
            # 检查第一列是否为日期
            date_value = row.iloc[0] if len(row) > 0 else None
            
            if (pd.notna(date_value) and self._is_valid_date(date_value)):
                # 检查是否有金额信息（DEBIT, CREDIT, BALANCE中至少一个有值）
                has_amount = False
                for col_idx in [4, 5, 6]:  # DEBIT, CREDIT, BALANCE列
                    if col_idx < len(row) and pd.notna(row.iloc[col_idx]):
                        try:
                            float(str(row.iloc[col_idx]).replace(',', ''))
                            has_amount = True
                            break
                        except:
                            continue
                
                if has_amount:
                    valid_rows.append(row)

        if valid_rows:
            df_valid = pd.DataFrame(valid_rows)
            df_valid.reset_index(drop=True, inplace=True)
            return df_valid
        else:
            return pd.DataFrame()

    def _is_valid_date(self, value) -> bool:
        """检查是否为有效的日期格式"""
        if pd.isna(value):
            return False

        date_str = str(value).strip()
        # IOB日期格式: DD-MMM-YYYY 或 DD/MM/YYYY
        return bool(re.match(r'\d{2}-[A-Z]{3}-\d{4}', date_str) or 
                   re.match(r'\d{2}/\d{2}/\d{4}', date_str) or
                   re.match(r'\d{2}-\d{2}-\d{4}', date_str))

    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        if df.empty:
            return df

        # 确保有7列
        while len(df.columns) < 7:
            df[f'Col_{len(df.columns)}'] = None

        # 如果列数超过7列，只保留前7列
        if len(df.columns) > 7:
            df = df.iloc[:, :7]

        # 设置标准列名，处理可能的换行符
        new_columns = []
        for i, expected_col in enumerate(self.expected_columns):
            if i < len(df.columns):
                new_columns.append(expected_col)
            else:
                break

        # 确保列名数量匹配
        while len(new_columns) < len(df.columns):
            new_columns.append(f'Col_{len(new_columns)}')

        df.columns = new_columns[:len(df.columns)]

        return df
    
    def _extract_pypdf_descriptions(self, pdf_path: str) -> List[Dict]:
        """使用PyPDF提取完整的描述文本"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                print(f"  📖 PyPDF读取 {len(reader.pages)} 页")

                all_descriptions = []

                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    text = page.extract_text()

                    print(f"  📄 处理第 {page_num + 1} 页...")
                    page_descriptions = self._parse_page_text(text, page_num + 1)

                    if page_descriptions:
                        all_descriptions.extend(page_descriptions)
                        print(f"    ✅ 提取到 {len(page_descriptions)} 条描述")
                    else:
                        print(f"    ⚠️ 第 {page_num + 1} 页没有找到有效描述")

                print(f"  ✅ PyPDF总计提取 {len(all_descriptions)} 条完整描述")
                return all_descriptions

        except Exception as e:
            print(f"  ❌ PyPDF提取失败: {e}")
            return []

    def _parse_page_text(self, text: str, page_num: int) -> List[Dict]:
        """解析单页文本，提取交易描述"""
        descriptions = []
        lines = text.split('\n')

        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if not line:
                i += 1
                continue

            # 查找交易行模式：日期 + 描述
            match = self._match_transaction_line(line)
            if match:
                # 检查下一行是否为当前交易的延续
                complete_description = match['description']
                j = i + 1

                # 查找可能的延续行
                while j < len(lines):
                    next_line = lines[j].strip()
                    if not next_line:
                        j += 1
                        continue

                    # 如果下一行是新的交易行，停止
                    if self._is_transaction_start_line(next_line):
                        break

                    # 如果下一行是表头或其他非交易内容，停止
                    if self._is_non_transaction_line(next_line):
                        break

                    # 如果下一行看起来是当前交易的延续，添加到描述中
                    if self._is_description_continuation(next_line, complete_description):
                        complete_description += " " + next_line
                        j += 1
                    else:
                        break

                # 更新描述并清理格式
                match['description'] = self._clean_description_text(complete_description)
                descriptions.append(match)
                i = j  # 跳过已处理的延续行
            else:
                i += 1

        return descriptions

    def _is_transaction_start_line(self, line: str) -> bool:
        """检查是否为交易开始行"""
        # IOB交易行模式：日期(DD-MMM-YYYY 或 DD/MM/YYYY) + 空格 + 描述
        pattern = r'^\d{2}[-/]\w{2,3}[-/]\d{4}'
        return bool(re.match(pattern, line))

    def _is_non_transaction_line(self, line: str) -> bool:
        """检查是否为非交易行（表头、页脚等）"""
        non_transaction_keywords = [
            'DATE', 'CHQ', 'NARATION', 'COD', 'DEBIT', 'CREDIT', 'BALANCE',
            'INDIAN OVERSEAS BANK', 'Statement', 'Page', 'Total', 'Account Number'
        ]

        line_upper = line.upper()
        for keyword in non_transaction_keywords:
            if keyword.upper() in line_upper:
                return True

        return False

    def _is_description_continuation(self, line: str, current_description: str) -> bool:
        """检查是否为描述的延续行"""
        line_stripped = line.strip()

        # 如果行中包含金额模式，可能不是纯描述延续
        if re.search(r'[\d,]+\.\d{2}', line):
            return False

        # 如果行太短，可能不是有效的延续
        if len(line_stripped) < 1:
            return False

        # 如果行包含典型的交易描述内容，认为是延续
        description_indicators = [
            'UPI', 'NEFT', 'IMPS', 'ATM', 'RTGS', 'TRF', 'PAYMENT', 'TRANSFER'
        ]

        line_upper = line_stripped.upper()
        for indicator in description_indicators:
            if indicator in line_upper:
                return True

        # 如果行看起来是名字或地址的一部分，认为是延续
        if re.match(r'^[A-Z\s/]+$', line_stripped):
            return True

        return False

    def _match_transaction_line(self, line: str) -> Optional[Dict]:
        """匹配交易行，提取日期和完整描述"""
        # IOB交易行模式：日期 描述 [金额信息]
        # 例如: "01-FEB-2022 ATM-SRI KRISHNA NAGAR HYDERABAD TSIN-N001996O-CPR TRF 500.0 8538.95"

        # 正则模式：日期 + 空格 + 描述
        pattern = r'^(\d{2}[-/]\w{2,3}[-/]\d{4})\s+(.+)$'
        match = re.match(pattern, line)

        if not match:
            return None

        date_str = match.group(1)
        rest_content = match.group(2)

        # 提取完整描述（移除末尾的金额部分）
        description = self._extract_description_from_content(rest_content)

        if description:
            return {
                'date': date_str,
                'description': description
            }

        return None

    def _extract_description_from_content(self, content: str) -> str:
        """从内容中提取描述部分，移除金额"""
        if not content:
            return ""

        # 更保守的金额移除策略，避免截断描述文本
        # 找到所有金额模式的位置
        amount_pattern = r'\s+[\d,]+\.\d{2}(?=\s|$)'  # 确保金额后面是空格或结尾
        amounts = list(re.finditer(amount_pattern, content))

        if amounts:
            # 分析金额的位置和上下文
            # 通常最后1-2个数字是金额，但要小心不要截断描述

            # 如果有多个金额，检查它们是否真的是金额而不是描述的一部分
            valid_amounts = []
            for amount_match in amounts:
                amount_text = amount_match.group().strip()
                amount_start = amount_match.start()

                # 检查金额前后的上下文
                before_text = content[:amount_start].strip()
                after_text = content[amount_match.end():].strip()

                # 如果金额后面还有很多文本，可能不是真正的金额
                if len(after_text) > 20:  # 金额后面不应该有太多文本
                    continue

                # 如果金额看起来合理（大于0，格式正确）
                try:
                    amount_value = float(amount_text.replace(',', ''))
                    if amount_value > 0:
                        valid_amounts.append(amount_match)
                except:
                    continue

            # 只移除确认的金额
            if valid_amounts:
                if len(valid_amounts) >= 2:
                    # 移除最后两个有效金额
                    last_amount_start = valid_amounts[-2].start()
                    content = content[:last_amount_start]
                elif len(valid_amounts) == 1:
                    # 只有一个有效金额，移除它
                    last_amount_start = valid_amounts[-1].start()
                    content = content[:last_amount_start]

        # 清理换行符和多余的空格
        content = self._clean_description_text(content)

        # 如果描述太短，可能解析有误
        if len(content) < 3:
            return ""

        return content

    def _clean_description_text(self, text: str) -> str:
        """清理描述文本，处理换行符和格式问题"""
        if not text:
            return ""

        # 移除换行符，用空格替换
        text = re.sub(r'\n+', ' ', text)

        # 移除回车符
        text = re.sub(r'\r+', ' ', text)

        # 移除制表符
        text = re.sub(r'\t+', ' ', text)

        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text)

        # 移除首尾空格
        text = text.strip()

        return text

    def _build_description_lookup(self, pypdf_data: List[Dict]) -> Dict[Tuple[str, str], str]:
        """构建描述查找字典，键为(date, description_prefix)"""
        lookup = {}

        for item in pypdf_data:
            date = item['date']
            description = item['description']

            # 使用描述的前20个字符作为前缀来创建更精确的匹配键
            desc_prefix = description[:20].strip() if description else ""
            key = (date, desc_prefix)

            # 保存完整描述
            lookup[key] = description

        return lookup

    def _merge_and_fix_data(self, df_structured: pd.DataFrame, pypdf_data: List[Dict]) -> pd.DataFrame:
        """合并tabula和pypdf数据，修复NARATION列 - 使用保守策略"""
        if df_structured.empty or not pypdf_data:
            print("  ⚠️ 无法合并：缺少tabula或pypdf数据")
            return df_structured

        print(f"  🔗 合并数据：tabula {len(df_structured)} 行，pypdf {len(pypdf_data)} 条描述")

        # 构建描述查找字典
        description_lookup = self._build_description_lookup(pypdf_data)

        # 统计匹配情况
        matched_count = 0
        improved_count = 0

        # 遍历tabula数据，只在必要时替换NARATION
        for idx, row in df_structured.iterrows():
            try:
                # 获取当前的描述和日期
                date_value = str(row['DATE']).strip()
                current_description = str(row['NARATION']) if pd.notna(row['NARATION']) else ""

                if not date_value or not current_description:
                    continue

                # 检查当前描述是否看起来被截断了
                is_truncated = self._is_description_truncated(current_description)

                if is_truncated:
                    # 尝试找到匹配的完整描述
                    desc_prefix = current_description[:20].strip()
                    key = (date_value, desc_prefix)

                    if key in description_lookup:
                        new_description = description_lookup[key]

                        # 只有当新描述确实更完整时才替换
                        if len(new_description) > len(current_description) * 1.2:  # 至少长20%
                            cleaned_description = self._clean_description_text(new_description)
                            df_structured.at[idx, 'NARATION'] = cleaned_description
                            improved_count += 1
                            matched_count += 1
                    else:
                        # 尝试模糊匹配
                        best_match = self._find_best_description_match(date_value, current_description, description_lookup)
                        if best_match:
                            df_structured.at[idx, 'NARATION'] = best_match
                            improved_count += 1
                            matched_count += 1
                else:
                    # 描述看起来完整，保持不变
                    matched_count += 1

            except Exception as e:
                print(f"    ⚠️ 处理第 {idx} 行时出错: {e}")
                continue

        print(f"  ✅ 处理完成: {matched_count}/{len(df_structured)} 行")
        print(f"  📈 描述改进: {improved_count} 行")

        return df_structured

    def _is_description_truncated(self, description: str) -> bool:
        """检查描述是否看起来被截断了"""
        if not description:
            return True

        # 检查是否以不完整的方式结尾
        truncation_indicators = [
            len(description) < 10,  # 太短
            description.endswith('...'),  # 明显的截断标记
            description.endswith(' '),  # 以空格结尾可能被截断
            not description[-1].isalnum() and description[-1] not in '.)',  # 以奇怪字符结尾
        ]

        return any(truncation_indicators)

    def _find_best_description_match(self, date: str, current_desc: str, lookup: Dict) -> Optional[str]:
        """为给定的描述找到最佳匹配"""
        current_prefix = current_desc[:15].strip().upper()

        # 在同一天的描述中查找最佳匹配
        for (lookup_date, lookup_prefix), full_desc in lookup.items():
            if lookup_date == date:
                if lookup_prefix.upper().startswith(current_prefix) or current_prefix.startswith(lookup_prefix.upper()):
                    if len(full_desc) > len(current_desc) * 1.2:
                        return self._clean_description_text(full_desc)

        return None

    def _validate_and_clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清理最终数据"""
        if df.empty:
            return df

        print("  🧹 清洗数据...")

        # 清洗日期列
        df = self._clean_date_column(df)

        # 清洗金额列
        df = self._clean_amount_columns(df)

        # 清洗描述列
        df = self._clean_naration_column(df)

        # 验证数据完整性
        df = self._validate_data_integrity(df)

        return df

    def _clean_date_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗日期列"""
        if 'DATE' in df.columns:
            print("    📅 清洗日期列...")
            df['DATE'] = df['DATE'].apply(self._parse_date)

        return df

    def _parse_date(self, value) -> str:
        """解析日期字符串"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()

        # IOB日期格式: DD-MMM-YYYY 或 DD/MM/YYYY
        if re.match(r'\d{2}-[A-Z]{3}-\d{4}', date_str) or re.match(r'\d{2}/\d{2}/\d{4}', date_str):
            return date_str

        # 尝试其他常见格式
        try:
            for fmt in ['%d-%b-%Y', '%d/%m/%Y', '%d-%m-%Y']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%d-%b-%Y')  # 统一为IOB格式
                except ValueError:
                    continue
        except:
            pass

        return date_str  # 如果无法解析，返回原值

    def _clean_amount_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗金额列数据"""
        amount_columns = ['DEBIT', 'CREDIT', 'BALANCE']

        for col in amount_columns:
            if col in df.columns:
                print(f"    💰 清洗 {col} 列...")
                df[col] = df[col].apply(self._parse_amount)

        return df

    def _parse_amount(self, value) -> Optional[float]:
        """解析金额字符串为浮点数"""
        if pd.isna(value) or value == '' or str(value).strip() == '':
            return None

        try:
            # 转换为字符串并清理
            amount_str = str(value).strip()

            # 移除货币符号和空格
            amount_str = re.sub(r'[₹\s]', '', amount_str)

            # 移除逗号分隔符
            amount_str = re.sub(r',', '', amount_str)

            # 移除括号（负数表示）
            is_negative = '(' in amount_str and ')' in amount_str
            amount_str = re.sub(r'[()]', '', amount_str)

            # 只保留数字和小数点
            amount_str = re.sub(r'[^\d.-]', '', amount_str)

            if not amount_str or amount_str == '-':
                return None

            # 转换为浮点数
            amount = float(amount_str)

            # 处理负数
            if is_negative:
                amount = -amount

            return amount

        except (ValueError, TypeError):
            return None

    def _clean_naration_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗NARATION列"""
        if 'NARATION' in df.columns:
            print("    📝 清洗NARATION列...")
            df['NARATION'] = df['NARATION'].apply(self._clean_description_text)

        return df

    def _validate_data_integrity(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据完整性"""
        print("    ✅ 验证数据完整性...")

        # 移除没有日期的行
        before_count = len(df)
        df = df[df['DATE'].notna() & (df['DATE'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效日期的记录")

        # 确保至少有金额或余额
        df = df[
            df['DEBIT'].notna() | df['CREDIT'].notna() | df['BALANCE'].notna()
        ]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "iob_extracted") -> Tuple[str, str, str]:
        """
        保存解析结果为多种格式

        Args:
            df: 解析后的DataFrame
            output_base: 输出文件名前缀

        Returns:
            Tuple[str, str, str]: CSV、JSON、Excel文件路径
        """
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 在保存前进行最终的文本清理
        df_clean = df.copy()
        if 'NARATION' in df_clean.columns:
            df_clean['NARATION'] = df_clean['NARATION'].apply(
                lambda x: self._clean_description_text(str(x)) if pd.notna(x) else ""
            )

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df_clean.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df_clean.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df_clean.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 IOB银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 交易类型统计
        if 'COD' in df.columns:
            cod_counts = df['COD'].value_counts()
            print(f"\n🏷️ 交易类型统计:")
            for cod, count in cod_counts.items():
                print(f"  {cod}: {count} 笔")

        # 金额统计
        debit_total = 0
        credit_total = 0

        if 'DEBIT' in df.columns:
            valid_debits = df['DEBIT'].dropna()
            if not valid_debits.empty:
                debit_total = valid_debits.sum()
                debit_count = len(valid_debits)
                print(f"\n💸 借记统计:")
                print(f"  借记交易: {debit_count} 笔")
                print(f"  借记总额: ₹{debit_total:,.2f}")

        if 'CREDIT' in df.columns:
            valid_credits = df['CREDIT'].dropna()
            if not valid_credits.empty:
                credit_total = valid_credits.sum()
                credit_count = len(valid_credits)
                print(f"\n💰 贷记统计:")
                print(f"  贷记交易: {credit_count} 笔")
                print(f"  贷记总额: ₹{credit_total:,.2f}")

        print(f"\n📊 净变化: ₹{credit_total - debit_total:,.2f}")

        # 数据完整性检查
        missing_dates = df['DATE'].isna().sum() if 'DATE' in df.columns else 0
        missing_narations = df['NARATION'].isna().sum() if 'NARATION' in df.columns else 0
        missing_balances = df['BALANCE'].isna().sum() if 'BALANCE' in df.columns else 0

        print(f"\n📋 数据完整性:")
        print(f"  缺失日期: {missing_dates} 条")
        print(f"  缺失描述: {missing_narations} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额连续性检查
        self._check_balance_continuity(df)

        # 日期范围
        if 'DATE' in df.columns and not df['DATE'].isna().all():
            valid_dates = df[df['DATE'].notna() & (df['DATE'] != '')]
            if not valid_dates.empty:
                print(f"\n📅 日期范围:")
                print(f"  最早交易: {valid_dates['DATE'].iloc[0]}")
                print(f"  最晚交易: {valid_dates['DATE'].iloc[-1]}")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            debit = f"₹{row['DEBIT']:,.2f}" if pd.notna(row['DEBIT']) else "-"
            credit = f"₹{row['CREDIT']:,.2f}" if pd.notna(row['CREDIT']) else "-"
            balance = f"₹{row['BALANCE']:,.2f}" if pd.notna(row['BALANCE']) else "-"
            cod = row['COD'] if pd.notna(row['COD']) else "-"

            print(f"  {i+1}. {row['DATE']} | {cod} | {str(row['NARATION'])[:30]}...")
            print(f"     借记: {debit} | 贷记: {credit} | 余额: {balance}")

    def _check_balance_continuity(self, df: pd.DataFrame) -> None:
        """检查余额连续性"""
        print(f"\n🔍 余额连续性检查:")

        if 'BALANCE' not in df.columns:
            print("  ⚠️ 没有余额列")
            return

        # 检查余额是否连续
        valid_balances = df[df['BALANCE'].notna()]

        if len(valid_balances) < 2:
            print("  ⚠️ 余额数据不足，无法检查连续性")
            return

        # 简单检查：余额应该是有变化的
        balance_changes = 0
        for i in range(1, len(valid_balances)):
            prev_balance = valid_balances.iloc[i-1]['BALANCE']
            curr_balance = valid_balances.iloc[i]['BALANCE']

            if abs(curr_balance - prev_balance) > 0.01:  # 忽略小数点误差
                balance_changes += 1

        print(f"  余额变化次数: {balance_changes}")

        # 检查首末余额
        first_balance = valid_balances.iloc[0]['BALANCE']
        last_balance = valid_balances.iloc[-1]['BALANCE']

        print(f"  期初余额: ₹{first_balance:,.2f}")
        print(f"  期末余额: ₹{last_balance:,.2f}")
        print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")


def main():
    """主函数"""
    parser = IOBPDFParser()

    pdf_path = "../files/14-iob-573689117-Statement-125601000023857-2.pdf"

    print("🚀 启动IOB银行PDF混合解析器")

    # 解析PDF
    df = parser.parse_iob_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 IOB银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ IOB银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
