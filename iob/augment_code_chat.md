你先理解一下该项目，并仔细分析下@boi 目录，然后参考 boi 实现的解析策略(结合 tabula 和 pypdf 的优势来实现,混合策略)帮我实现 iob 银行账单(@14-iob-573689117-Statement-125601000023857-2.pdf)的解析
1、要求在 iob 目录实现，在该目录下生成 iob_extracted.csv iob_extracted.json iob_extracted.xlsx 三个文件，并生成解析报告
2、要保留 iob PDF 银行账单里面原始的表格格式，你只是参考 boi 的实现策略和方法
3、你不能随意给我删除列，我要保留 PDF 中原有的所有列和列名
4、如果你觉得格式比较规范用 tabula 就可以实现，你可以单独使用 tabula 就好，不用结合两者
5、因为 iob 目前没有可对比的文件，所以你先不用实现验证逻辑

IOB 银行 PDF 解析器存在 NARATION 列数据错误问题，需要进行调试和修复。具体问题如下：

**问题描述：**

1. 检查生成的 iob_extracted.csv 文件的前 13 条记录
2. 发现 NARATION 列出现重复数据的 bug
3. 相同日期的多条交易记录中，后续记录的 NARATION 被错误地替换为第一条记录的 NARATION
4. 导致后续交易记录的原始 NARATION 描述信息丢失

**需要修复的具体问题：**

1. 在数据合并过程中（\_merge_and_fix_data 方法），基于日期匹配的逻辑存在缺陷
2. 当同一天有多条交易时，PyPDF 提取的描述被错误地应用到所有同日期的记录上
3. 需要改进匹配策略，确保每条交易记录保持其独特的 NARATION 描述

**修复要求：**

1. 分析并修复\_build_description_lookup 和\_merge_and_fix_data 方法中的逻辑错误
2. 改进匹配策略，可能需要结合日期+序号或其他唯一标识符进行精确匹配
3. 确保每条交易记录的 NARATION 描述保持独立和准确
4. 重新运行解析器并验证前 13 条记录的 NARATION 列数据正确性
5. 生成修复后的输出文件并确认问题已解决

**验证标准：**

-   同一日期的不同交易记录应该有不同的 NARATION 描述
-   不应该出现 NARATION 内容重复覆盖的情况
-   保持原始 PDF 中每条交易的完整描述信息
