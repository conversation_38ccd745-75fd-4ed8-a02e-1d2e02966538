#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试IOB银行PDF解析器中NARATION列的问题
"""

import tabula
import pandas as pd
import pypdf

def debug_naration_issue():
    """调试NARATION列重复问题"""
    pdf_path = '../files/14-iob-573689117-Statement-125601000023857-2.pdf'
    
    print('=' * 80)
    print('调试IOB银行NARATION列重复问题')
    print('=' * 80)
    
    # 1. 查看原始tabula数据
    print('\n🔍 1. 原始Tabula数据分析:')
    try:
        dfs = tabula.read_pdf(pdf_path, pages='1', lattice=True)
        if dfs:
            df = dfs[0]
            print(f'   列名: {list(df.columns)}')
            print('   前15行原始NARATION数据:')
            for i in range(min(15, len(df))):
                row = df.iloc[i]
                date = row.iloc[0]
                naration = str(row.iloc[2])[:60]
                debit = row.iloc[4]
                credit = row.iloc[5]
                balance = row.iloc[6]
                print(f'   {i+1:2d}. {date} | {naration} | D:{debit} C:{credit} B:{balance}')
    except Exception as e:
        print(f'   ❌ Tabula分析失败: {e}')
    
    # 2. 查看PyPDF提取的数据
    print('\n🔍 2. PyPDF文本提取分析:')
    try:
        with open(pdf_path, 'rb') as file:
            reader = pypdf.PdfReader(file)
            page = reader.pages[0]
            text = page.extract_text()
            
            lines = text.split('\n')
            print('   前20行文本内容:')
            for i, line in enumerate(lines[:20]):
                if line.strip():
                    print(f'   {i+1:2d}. {line.strip()[:80]}')
                    
            # 查找交易行
            print('\n   识别的交易行:')
            count = 0
            for i, line in enumerate(lines):
                line = line.strip()
                if line and any(date_part in line for date_part in ['01-FEB-2022', '02-FEB-2022']):
                    print(f'   {count+1:2d}. {line[:80]}')
                    count += 1
                    if count >= 10:
                        break
                        
    except Exception as e:
        print(f'   ❌ PyPDF分析失败: {e}')
    
    # 3. 分析当前解析器的匹配逻辑问题
    print('\n🔍 3. 当前匹配逻辑问题分析:')
    print('   问题：使用日期作为唯一匹配键')
    print('   结果：同一天的多条交易被错误地替换为相同描述')
    print('   解决方案：需要更精确的匹配策略')
    
    # 4. 建议的修复方案
    print('\n💡 4. 修复方案建议:')
    print('   方案1：使用日期+金额作为匹配键')
    print('   方案2：使用日期+描述前缀作为匹配键')
    print('   方案3：保持tabula原始描述，只在确实截断时才用pypdf补充')
    print('   推荐：方案3 - 保守策略，避免过度替换')

if __name__ == "__main__":
    debug_naration_issue()
