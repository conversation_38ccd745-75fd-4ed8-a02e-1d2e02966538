#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
银行PDF账单自动识别系统
基于PDF内容分析自动判断账单来源银行
"""

import os
import re
import json
import time
import io
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging

# PDF处理库
try:
    import pypdf
    PYPDF_AVAILABLE = True
except ImportError:
    PYPDF_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

try:
    import tabula
    TABULA_AVAILABLE = True
except ImportError:
    TABULA_AVAILABLE = False

# OCR相关
try:
    import fitz  # PyMuPDF
    from PIL import Image, ImageEnhance, ImageFilter
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False


class BankType(Enum):
    """银行类型枚举"""
    SBI = "State Bank of India"
    HDFC = "HDFC Bank"
    ICICI = "ICICI Bank"
    KOTAK = "Kotak Mahindra Bank"
    BOI = "Bank of India"
    IOB = "Indian Overseas Bank"
    PNB = "Punjab National Bank"
    CANARA = "Canara Bank"
    UBI = "Union Bank of India"
    BOB = "Bank of Baroda"
    CBI = "Central Bank of India"
    INDIAN = "Indian Bank"
    UCO = "UCO Bank"
    YES = "Yes Bank"
    FEDERAL = "Federal Bank"
    BANDHAN = "Bandhan Bank"
    IDBI = "IDBI Bank"
    INDUSIND = "IndusInd Bank"
    SIB = "South Indian Bank"
    UNKNOWN = "Unknown Bank"


@dataclass
class BankFeature:
    """银行特征数据类"""
    bank_type: BankType
    confidence: float
    matched_features: List[str]
    extraction_method: str


@dataclass
class IdentificationResult:
    """识别结果数据类"""
    identified_bank: BankType
    confidence_score: float
    all_matches: List[BankFeature]
    processing_time: float
    extraction_methods_used: List[str]
    error_message: Optional[str] = None


class BankFeatureDatabase:
    """银行特征数据库"""
    
    def __init__(self):
        self.features = self._build_feature_database()
    
    def _build_feature_database(self) -> Dict[BankType, Dict]:
        """构建银行特征数据库"""
        return {
            BankType.SBI: {
                "header_keywords": [
                    "STATE BANK OF INDIA", "SBI", "SBIN", "स्टेट बैंक ऑफ इंडिया"
                ],
                "table_headers": [
                    ["Txn Date", "Value Date", "Description", "Ref No./Cheque No.", "Debit", "Credit", "Balance"],
                    ["Date", "Particulars", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d %b %Y", "%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}", r"[\d,]+\.?\d{0,2}"],
                "unique_identifiers": ["SBIN", "State Bank", "SBI BANK"],
                "footer_patterns": ["This is computer generated statement"],
                "address_patterns": ["Mumbai", "Corporate Centre"]
            },
            
            BankType.HDFC: {
                "header_keywords": [
                    "HDFC BANK", "HDFC", "Housing Development Finance Corporation"
                ],
                "table_headers": [
                    ["Date", "Narration", "Chq./Ref.No.", "ValueDt", "WithdrawalAmt.", "DepositAmt.", "ClosingBalance"],
                    ["Date", "Particulars", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d/%m/%y", "%d-%m-%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["HDFC", "HDFCBANK"],
                "footer_patterns": ["HDFC Bank Ltd"],
                "address_patterns": ["Mumbai", "Nariman Point"]
            },
            
            BankType.ICICI: {
                "header_keywords": [
                    "ICICI BANK", "ICICI", "Industrial Credit and Investment Corporation"
                ],
                "table_headers": [
                    ["DATE", "PARTICULARS", "CHQ/REF NO.", "VALUE DATE", "WITHDRAWAL AMT", "DEPOSIT AMT", "BALANCE"],
                    ["Date", "Description", "Cheque No", "Debit", "Credit", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["ICICI", "ICICBANK", "ICIC"],
                "footer_patterns": ["ICICI Bank Limited"],
                "address_patterns": ["Mumbai", "Bandra Kurla Complex"]
            },
            
            BankType.KOTAK: {
                "header_keywords": [
                    "KOTAK MAHINDRA BANK", "KOTAK", "KOTAK BANK"
                ],
                "table_headers": [
                    ["Date", "Narration", "Chq/Ref No", "Withdrawal (Dr)/ Deposit (Cr)", "Balance"],
                    ["Transaction Date", "Description", "Cheque Number", "Amount", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["KOTAK", "KOTAKBANK", "KKBK"],
                "footer_patterns": ["Kotak Mahindra Bank Limited"],
                "address_patterns": ["Mumbai", "Nariman Point"]
            },
            
            BankType.BOI: {
                "header_keywords": [
                    "BANK OF INDIA", "BOI", "बैंक ऑफ इंडिया"
                ],
                "table_headers": [
                    ["Txn Date", "Value Date", "Description", "Ref No./Cheque No.", "Debit", "Credit", "Balance"],
                    ["Date", "Particulars", "Cheque No", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["BANK OF INDIA", "BOI", "BKID"],
                "footer_patterns": ["Bank of India"],
                "address_patterns": ["Mumbai", "Star House"]
            },
            
            BankType.IOB: {
                "header_keywords": [
                    "INDIAN OVERSEAS BANK", "IOB", "इंडियन ओवरसीज बैंक"
                ],
                "table_headers": [
                    ["DATE", "NARATION", "CHQ NO", "DEBIT", "CREDIT", "BALANCE"],
                    ["Date", "Description", "Cheque No", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["INDIAN OVERSEAS BANK", "IOB", "IOBA"],
                "footer_patterns": ["Indian Overseas Bank"],
                "address_patterns": ["Chennai", "IOB Bhavan"]
            },
            
            BankType.PNB: {
                "header_keywords": [
                    "PUNJAB NATIONAL BANK", "PNB", "पंजाब नेशनल बैंक"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Chq No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["PUNJAB NATIONAL BANK", "PNB", "PUNB"],
                "footer_patterns": ["Punjab National Bank"],
                "address_patterns": ["New Delhi", "Bhavishya Nidhi Bhawan"]
            },

            BankType.CANARA: {
                "header_keywords": [
                    "CANARA BANK", "CANARA", "कैनरा बैंक"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["CANARA BANK", "CANARA", "CNRB"],
                "footer_patterns": ["Canara Bank"],
                "address_patterns": ["Bangalore", "Head Office"]
            },

            BankType.UBI: {
                "header_keywords": [
                    "UNION BANK OF INDIA", "UBI", "यूनियन बैंक ऑफ इंडिया"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["UNION BANK OF INDIA", "UBI", "UBIN"],
                "footer_patterns": ["Union Bank of India"],
                "address_patterns": ["Mumbai", "Union Bank Bhavan"]
            },

            BankType.BOB: {
                "header_keywords": [
                    "BANK OF BARODA", "BOB", "बैंक ऑफ बड़ौदा"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["BANK OF BARODA", "BOB", "BARB"],
                "footer_patterns": ["Bank of Baroda"],
                "address_patterns": ["Vadodara", "Baroda"]
            },

            BankType.CBI: {
                "header_keywords": [
                    "CENTRAL BANK OF INDIA", "CBI", "सेंट्रल बैंक ऑफ इंडिया"
                ],
                "table_headers": [
                    ["Value Date", "Post Date", "Details", "Amount", "Balance"],
                    ["Date", "Particulars", "Chq/Ref No", "Debit", "Credit", "Balance"]
                ],
                "date_formats": ["%d/%m/%y", "%d-%m-%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["CENTRAL BANK OF INDIA", "CBI", "CBIN"],
                "footer_patterns": ["Central Bank of India"],
                "address_patterns": ["Mumbai", "Central Office"]
            },

            BankType.INDIAN: {
                "header_keywords": [
                    "INDIAN BANK", "इंडियन बैंक"
                ],
                "table_headers": [
                    ["Date", "Remarks", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["INDIAN BANK", "INDB"],
                "footer_patterns": ["Indian Bank"],
                "address_patterns": ["Chennai", "Indian Bank"]
            },

            BankType.UCO: {
                "header_keywords": [
                    "UCO BANK", "UCO", "यूको बैंक"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Withdrawals", "Deposits", "Balance"],
                    ["Transaction Date", "Description", "Debit", "Credit", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["UCO BANK", "UCO", "UCBA"],
                "footer_patterns": ["UCO Bank"],
                "address_patterns": ["Kolkata", "UCO Bank"]
            },

            BankType.YES: {
                "header_keywords": [
                    "YES BANK", "YES", "यस बैंक"
                ],
                "table_headers": [
                    ["Date", "Description", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Particulars", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["YES BANK", "YES", "YESB"],
                "footer_patterns": ["Yes Bank"],
                "address_patterns": ["Mumbai", "Yes Bank"]
            },

            BankType.FEDERAL: {
                "header_keywords": [
                    "FEDERAL BANK", "FEDERAL", "फेडरल बैंक"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["FEDERAL BANK", "FEDERAL", "FDRL"],
                "footer_patterns": ["Federal Bank"],
                "address_patterns": ["Kochi", "Federal Bank"]
            },

            BankType.BANDHAN: {
                "header_keywords": [
                    "BANDHAN BANK", "BANDHAN", "बंधन बैंक"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["BANDHAN BANK", "BANDHAN", "BDBL"],
                "footer_patterns": ["Bandhan Bank"],
                "address_patterns": ["Kolkata", "Bandhan Bank"]
            },

            BankType.IDBI: {
                "header_keywords": [
                    "IDBI BANK", "IDBI", "आईडीबीआई बैंक"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["IDBI BANK", "IDBI", "IBKL"],
                "footer_patterns": ["IDBI Bank"],
                "address_patterns": ["Mumbai", "IDBI Bank"]
            },

            BankType.INDUSIND: {
                "header_keywords": [
                    "INDUSIND BANK", "INDUSIND", "इंडसइंड बैंक"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["INDUSIND BANK", "INDUSIND", "INDB"],
                "footer_patterns": ["IndusInd Bank"],
                "address_patterns": ["Mumbai", "IndusInd Bank"]
            },

            BankType.SIB: {
                "header_keywords": [
                    "SOUTH INDIAN BANK", "SIB", "साउथ इंडियन बैंक"
                ],
                "table_headers": [
                    ["Date", "Particulars", "Chq/Ref No", "Debit", "Credit", "Balance"],
                    ["Transaction Date", "Description", "Reference", "Withdrawals", "Deposits", "Balance"]
                ],
                "date_formats": ["%d-%m-%Y", "%d/%m/%Y"],
                "amount_patterns": [r"[\d,]+\.\d{2}"],
                "unique_identifiers": ["SOUTH INDIAN BANK", "SIB", "SIBL"],
                "footer_patterns": ["South Indian Bank"],
                "address_patterns": ["Thrissur", "South Indian Bank"]
            }
        }
    
    def get_bank_features(self, bank_type: BankType) -> Dict:
        """获取指定银行的特征"""
        return self.features.get(bank_type, {})
    
    def get_all_banks(self) -> List[BankType]:
        """获取所有支持的银行类型"""
        return list(self.features.keys())


class PDFContentExtractor:
    """PDF内容提取器"""
    
    def __init__(self):
        self.logger = self._setup_logging()
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('BankIdentifier')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def extract_text_content(self, pdf_path: str) -> Dict[str, Any]:
        """提取PDF文本内容"""
        content = {
            'pypdf_text': '',
            'pdfplumber_text': '',
            'tabula_text': '',
            'ocr_text': '',
            'extraction_success': False,
            'methods_used': []
        }
        
        # 方法1: PyPDF提取
        if PYPDF_AVAILABLE:
            try:
                content['pypdf_text'] = self._extract_with_pypdf(pdf_path)
                content['methods_used'].append('pypdf')
                self.logger.info("PyPDF提取成功")
            except Exception as e:
                self.logger.warning(f"PyPDF提取失败: {e}")
        
        # 方法2: PDFPlumber提取
        if PDFPLUMBER_AVAILABLE:
            try:
                content['pdfplumber_text'] = self._extract_with_pdfplumber(pdf_path)
                content['methods_used'].append('pdfplumber')
                self.logger.info("PDFPlumber提取成功")
            except Exception as e:
                self.logger.warning(f"PDFPlumber提取失败: {e}")
        
        # 方法3: Tabula提取
        if TABULA_AVAILABLE:
            try:
                content['tabula_text'] = self._extract_with_tabula(pdf_path)
                content['methods_used'].append('tabula')
                self.logger.info("Tabula提取成功")
            except Exception as e:
                self.logger.warning(f"Tabula提取失败: {e}")
        
        # 方法4: OCR提取
        if OCR_AVAILABLE:
            try:
                content['ocr_text'] = self._extract_with_ocr(pdf_path)
                content['methods_used'].append('ocr')
                self.logger.info("OCR提取成功")
            except Exception as e:
                self.logger.warning(f"OCR提取失败: {e}")
        
        # 检查是否有成功的提取
        content['extraction_success'] = any([
            content['pypdf_text'],
            content['pdfplumber_text'],
            content['tabula_text'],
            content['ocr_text']
        ])
        
        return content
    
    def _extract_with_pypdf(self, pdf_path: str) -> str:
        """使用PyPDF提取文本"""
        text = ""
        with open(pdf_path, 'rb') as file:
            reader = pypdf.PdfReader(file)
            # 只提取前3页的内容用于识别
            for page_num in range(min(3, len(reader.pages))):
                page = reader.pages[page_num]
                text += page.extract_text() + "\n"
        return text.strip()
    
    def _extract_with_pdfplumber(self, pdf_path: str) -> str:
        """使用PDFPlumber提取文本"""
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            # 只提取前3页的内容用于识别
            for page_num in range(min(3, len(pdf.pages))):
                page = pdf.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text.strip()
    
    def _extract_with_tabula(self, pdf_path: str) -> str:
        """使用Tabula提取表格文本"""
        text = ""
        try:
            # 只提取前3页
            dfs = tabula.read_pdf(pdf_path, pages="1-3", multiple_tables=True)
            for df in dfs:
                if not df.empty:
                    text += df.to_string() + "\n"
        except Exception as e:
            self.logger.warning(f"Tabula提取失败: {e}")
        return text.strip()
    
    def _extract_with_ocr(self, pdf_path: str) -> str:
        """使用OCR提取文本"""
        text = ""
        try:
            doc = fitz.open(pdf_path)
            # 只处理第一页进行OCR识别
            page = doc[0]
            
            # 将页面转换为图像
            mat = fitz.Matrix(2.0, 2.0)  # 放大2倍提高OCR质量
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            # 使用PIL处理图像
            img = Image.open(io.BytesIO(img_data))
            
            # 图像预处理
            img = img.convert('L')  # 转为灰度
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(2.0)  # 增强对比度
            
            # OCR识别
            text = pytesseract.image_to_string(img, lang='eng+hin')
            
            doc.close()
        except Exception as e:
            self.logger.warning(f"OCR提取失败: {e}")
        
        return text.strip()


class BankIdentifier:
    """银行识别器主类"""
    
    def __init__(self):
        self.feature_db = BankFeatureDatabase()
        self.content_extractor = PDFContentExtractor()
        self.logger = self._setup_logging()
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('BankIdentifier')
        logger.setLevel(logging.INFO)
        return logger
    
    def identify_bank(self, pdf_path: str) -> IdentificationResult:
        """识别PDF文件的银行类型"""
        start_time = time.time()
        
        self.logger.info(f"开始识别银行类型: {pdf_path}")
        
        # 检查文件是否存在
        if not os.path.exists(pdf_path):
            return IdentificationResult(
                identified_bank=BankType.UNKNOWN,
                confidence_score=0.0,
                all_matches=[],
                processing_time=0.0,
                extraction_methods_used=[],
                error_message="文件不存在"
            )
        
        try:
            # 提取PDF内容
            content = self.content_extractor.extract_text_content(pdf_path)
            
            if not content['extraction_success']:
                return IdentificationResult(
                    identified_bank=BankType.UNKNOWN,
                    confidence_score=0.0,
                    all_matches=[],
                    processing_time=time.time() - start_time,
                    extraction_methods_used=content['methods_used'],
                    error_message="无法提取PDF内容"
                )
            
            # 分析内容并识别银行
            all_matches = self._analyze_content_for_banks(content)
            
            # 选择最佳匹配
            if all_matches:
                best_match = max(all_matches, key=lambda x: x.confidence)
                identified_bank = best_match.bank_type
                confidence_score = best_match.confidence
            else:
                identified_bank = BankType.UNKNOWN
                confidence_score = 0.0
            
            processing_time = time.time() - start_time
            
            self.logger.info(f"识别完成: {identified_bank.value}, 置信度: {confidence_score:.3f}")
            
            return IdentificationResult(
                identified_bank=identified_bank,
                confidence_score=confidence_score,
                all_matches=all_matches,
                processing_time=processing_time,
                extraction_methods_used=content['methods_used']
            )
            
        except Exception as e:
            self.logger.error(f"识别过程中出错: {e}")
            return IdentificationResult(
                identified_bank=BankType.UNKNOWN,
                confidence_score=0.0,
                all_matches=[],
                processing_time=time.time() - start_time,
                extraction_methods_used=[],
                error_message=str(e)
            )

    def _analyze_content_for_banks(self, content: Dict[str, Any]) -> List[BankFeature]:
        """分析内容并识别所有可能的银行匹配"""
        all_matches = []

        # 合并所有提取的文本
        combined_text = " ".join([
            content.get('pypdf_text', ''),
            content.get('pdfplumber_text', ''),
            content.get('tabula_text', ''),
            content.get('ocr_text', '')
        ]).upper()

        # 对每个银行进行特征匹配
        for bank_type in self.feature_db.get_all_banks():
            features = self.feature_db.get_bank_features(bank_type)

            # 计算匹配分数
            match_result = self._calculate_bank_match_score(combined_text, bank_type, features, content)

            if match_result['confidence'] > 0.1:  # 只保留有一定置信度的匹配
                bank_feature = BankFeature(
                    bank_type=bank_type,
                    confidence=match_result['confidence'],
                    matched_features=match_result['matched_features'],
                    extraction_method=match_result['extraction_method']
                )
                all_matches.append(bank_feature)

        # 按置信度排序
        all_matches.sort(key=lambda x: x.confidence, reverse=True)

        return all_matches

    def _calculate_bank_match_score(self, text: str, bank_type: BankType, features: Dict, content: Dict) -> Dict:
        """计算银行匹配分数"""
        score = 0.0
        matched_features = []
        extraction_method = "combined"

        # 1. 银行名称关键词匹配 (权重: 50%) - 提高权重
        header_score = self._match_header_keywords(text, features.get('header_keywords', []))
        if header_score > 0:
            score += header_score * 0.5
            matched_features.append(f"银行名称匹配({header_score:.2f})")

        # 2. 唯一标识符匹配 (权重: 30%) - 提高权重
        identifier_score = self._match_unique_identifiers(text, features.get('unique_identifiers', []))
        if identifier_score > 0:
            score += identifier_score * 0.3
            matched_features.append(f"唯一标识符匹配({identifier_score:.2f})")

        # 3. 表头结构匹配 (权重: 15%) - 降低权重，因为太宽泛
        table_score = self._match_table_headers(text, features.get('table_headers', []))
        if table_score > 0:
            score += table_score * 0.15
            matched_features.append(f"表头结构匹配({table_score:.2f})")

        # 4. 地址模式匹配 (权重: 3%)
        address_score = self._match_address_patterns(text, features.get('address_patterns', []))
        if address_score > 0:
            score += address_score * 0.03
            matched_features.append(f"地址模式匹配({address_score:.2f})")

        # 5. 页脚模式匹配 (权重: 2%)
        footer_score = self._match_footer_patterns(text, features.get('footer_patterns', []))
        if footer_score > 0:
            score += footer_score * 0.02
            matched_features.append(f"页脚模式匹配({footer_score:.2f})")

        # 6. 银行特有模式匹配 - 新增强化匹配
        specific_score = self._match_bank_specific_patterns(text, bank_type)
        if specific_score > 0:
            score += specific_score * 0.2  # 额外加分
            matched_features.append(f"银行特有模式匹配({specific_score:.2f})")

        return {
            'confidence': min(score, 1.0),  # 确保不超过1.0
            'matched_features': matched_features,
            'extraction_method': extraction_method
        }

    def _match_header_keywords(self, text: str, keywords: List[str]) -> float:
        """匹配银行名称关键词"""
        if not keywords:
            return 0.0

        matches = 0
        total_keywords = len(keywords)

        for keyword in keywords:
            keyword_upper = keyword.upper()
            # 精确匹配和部分匹配
            if keyword_upper in text:
                # 检查是否为完整词匹配（更高权重）
                if self._is_whole_word_match(text, keyword_upper):
                    matches += 1.0
                else:
                    matches += 0.7  # 部分匹配给较低分数

        return min(matches / total_keywords, 1.0)

    def _match_table_headers(self, text: str, table_headers: List[List[str]]) -> float:
        """匹配表头结构"""
        if not table_headers:
            return 0.0

        best_score = 0.0

        for header_set in table_headers:
            matches = 0
            total_headers = len(header_set)

            for header in header_set:
                header_upper = header.upper()

                # 精确匹配给满分
                if header_upper in text:
                    matches += 1.0
                else:
                    # 部分匹配：检查关键词
                    words = header_upper.split()
                    word_matches = 0
                    for word in words:
                        if len(word) > 2 and word in text:  # 忽略太短的词
                            word_matches += 1

                    if word_matches > 0:
                        matches += (word_matches / len(words)) * 0.6  # 部分匹配给较低分数

            score = matches / total_headers
            best_score = max(best_score, score)

        return min(best_score, 1.0)

    def _match_unique_identifiers(self, text: str, identifiers: List[str]) -> float:
        """匹配唯一标识符"""
        if not identifiers:
            return 0.0

        matches = 0
        total_identifiers = len(identifiers)

        for identifier in identifiers:
            identifier_upper = identifier.upper()

            # 检查完整词匹配
            if self._is_whole_word_match(text, identifier_upper):
                matches += 1.0
            elif identifier_upper in text:
                # 部分匹配，但权重较低
                matches += 0.5

        return min(matches / total_identifiers, 1.0)

    def _match_address_patterns(self, text: str, patterns: List[str]) -> float:
        """匹配地址模式"""
        if not patterns:
            return 0.0

        matches = 0
        total_patterns = len(patterns)

        for pattern in patterns:
            if pattern.upper() in text:
                matches += 1

        return matches / total_patterns

    def _match_footer_patterns(self, text: str, patterns: List[str]) -> float:
        """匹配页脚模式"""
        if not patterns:
            return 0.0

        matches = 0
        total_patterns = len(patterns)

        for pattern in patterns:
            if pattern.upper() in text:
                matches += 1

        return matches / total_patterns

    def batch_identify(self, pdf_files: List[str]) -> Dict[str, IdentificationResult]:
        """批量识别多个PDF文件"""
        results = {}

        self.logger.info(f"开始批量识别 {len(pdf_files)} 个文件")

        for i, pdf_path in enumerate(pdf_files, 1):
            self.logger.info(f"处理文件 {i}/{len(pdf_files)}: {os.path.basename(pdf_path)}")

            result = self.identify_bank(pdf_path)
            results[pdf_path] = result

        return results

    def generate_identification_report(self, results: Dict[str, IdentificationResult]) -> str:
        """生成识别报告"""
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("银行PDF账单自动识别报告")
        report_lines.append("=" * 80)

        # 统计信息
        total_files = len(results)
        successful_identifications = sum(1 for r in results.values() if r.identified_bank != BankType.UNKNOWN)
        success_rate = (successful_identifications / total_files * 100) if total_files > 0 else 0

        report_lines.append(f"\n📊 总体统计:")
        report_lines.append(f"  总文件数: {total_files}")
        report_lines.append(f"  成功识别: {successful_identifications}")
        report_lines.append(f"  识别成功率: {success_rate:.1f}%")

        # 银行分布统计
        bank_counts = {}
        for result in results.values():
            bank = result.identified_bank
            bank_counts[bank] = bank_counts.get(bank, 0) + 1

        report_lines.append(f"\n🏦 银行分布:")
        for bank, count in sorted(bank_counts.items(), key=lambda x: x[1], reverse=True):
            report_lines.append(f"  {bank.value}: {count} 个文件")

        # 详细结果
        report_lines.append(f"\n📋 详细识别结果:")
        report_lines.append("=" * 80)

        for pdf_path, result in results.items():
            filename = os.path.basename(pdf_path)
            report_lines.append(f"\n文件: {filename}")
            report_lines.append(f"识别银行: {result.identified_bank.value}")
            report_lines.append(f"置信度: {result.confidence_score:.3f}")
            report_lines.append(f"处理时间: {result.processing_time:.2f}秒")
            report_lines.append(f"提取方法: {', '.join(result.extraction_methods_used)}")

            if result.error_message:
                report_lines.append(f"错误信息: {result.error_message}")

            if result.all_matches:
                report_lines.append("所有匹配结果:")
                for match in result.all_matches[:3]:  # 只显示前3个匹配
                    report_lines.append(f"  - {match.bank_type.value}: {match.confidence:.3f}")
                    report_lines.append(f"    匹配特征: {', '.join(match.matched_features)}")

            report_lines.append("-" * 40)

        return "\n".join(report_lines)

    def _is_whole_word_match(self, text: str, keyword: str) -> bool:
        """检查是否为完整词匹配"""
        import re
        pattern = r'\b' + re.escape(keyword) + r'\b'
        return bool(re.search(pattern, text))

    def _match_bank_specific_patterns(self, text: str, bank_type: BankType) -> float:
        """匹配银行特有模式"""
        specific_patterns = {
            BankType.SBI: [
                "STATE BANK OF INDIA", "SBIN", "स्टेट बैंक", "SBI BANK",
                "CORPORATE CENTRE", "MUMBAI", "IFSC.*SBIN"
            ],
            BankType.HDFC: [
                "HDFC BANK LIMITED", "HDFCBANK", "HOUSING DEVELOPMENT",
                "NARIMAN POINT", "MUMBAI", "IFSC.*HDFC"
            ],
            BankType.ICICI: [
                "ICICI BANK LIMITED", "ICICBANK", "INDUSTRIAL CREDIT",
                "BANDRA KURLA COMPLEX", "MUMBAI", "IFSC.*ICIC"
            ],
            BankType.KOTAK: [
                "KOTAK MAHINDRA BANK", "KOTAKBANK", "KKBK",
                "NARIMAN POINT", "MUMBAI", "IFSC.*KKBK"
            ],
            BankType.BOI: [
                "BANK OF INDIA", "BKID", "बैंक ऑफ इंडिया",
                "STAR HOUSE", "MUMBAI", "IFSC.*BKID"
            ],
            BankType.IOB: [
                "INDIAN OVERSEAS BANK", "IOBA", "इंडियन ओवरसीज",
                "IOB BHAVAN", "CHENNAI", "IFSC.*IOBA"
            ],
            BankType.PNB: [
                "PUNJAB NATIONAL BANK", "PUNB", "पंजाब नेशनल",
                "BHAVISHYA NIDHI BHAWAN", "NEW DELHI", "IFSC.*PUNB"
            ],
            BankType.CANARA: [
                "CANARA BANK", "CNRB", "कैनरा बैंक",
                "HEAD OFFICE", "BANGALORE", "IFSC.*CNRB"
            ],
            BankType.UBI: [
                "UNION BANK OF INDIA", "UBIN", "यूनियन बैंक",
                "UNION BANK BHAVAN", "MUMBAI", "IFSC.*UBIN"
            ],
            BankType.BOB: [
                "BANK OF BARODA", "BARB", "बैंक ऑफ बड़ौदा",
                "VADODARA", "BARODA", "IFSC.*BARB"
            ],
            BankType.CBI: [
                "CENTRAL BANK OF INDIA", "CBIN", "सेंट्रल बैंक",
                "CENTRAL OFFICE", "MUMBAI", "IFSC.*CBIN"
            ],
            BankType.INDIAN: [
                "INDIAN BANK", "INDB", "इंडियन बैंक",
                "CHENNAI", "IFSC.*INDB"
            ],
            BankType.UCO: [
                "UCO BANK", "UCBA", "यूको बैंक",
                "KOLKATA", "IFSC.*UCBA"
            ],
            BankType.YES: [
                "YES BANK", "YESB", "यस बैंक",
                "MUMBAI", "IFSC.*YESB"
            ],
            BankType.FEDERAL: [
                "FEDERAL BANK", "FDRL", "फेडरल बैंक",
                "KOCHI", "IFSC.*FDRL"
            ],
            BankType.BANDHAN: [
                "BANDHAN BANK", "BDBL", "बंधन बैंक",
                "KOLKATA", "IFSC.*BDBL"
            ],
            BankType.IDBI: [
                "IDBI BANK", "IBKL", "आईडीबीआई बैंक",
                "MUMBAI", "IFSC.*IBKL"
            ],
            BankType.INDUSIND: [
                "INDUSIND BANK", "INDB", "इंडसइंड बैंक",
                "MUMBAI", "IFSC.*INDB"
            ],
            BankType.SIB: [
                "SOUTH INDIAN BANK", "SIBL", "साउथ इंडियन बैंक",
                "THRISSUR", "IFSC.*SIBL"
            ]
        }

        patterns = specific_patterns.get(bank_type, [])
        if not patterns:
            return 0.0

        matches = 0
        for pattern in patterns:
            if "IFSC.*" in pattern:
                # 正则表达式匹配
                import re
                if re.search(pattern, text):
                    matches += 2  # IFSC匹配给更高分数
            elif pattern.upper() in text:
                matches += 1

        return min(matches / len(patterns), 1.0)
