#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Federal银行数据后处理器
1. 添加Opening Balance记录
2. 去掉Page列
3. 修复0值显示
"""

import pandas as pd


def process_federal_data():
    """处理Federal银行数据"""
    print("=== Federal银行数据后处理 ===")
    
    # 读取原始数据
    df = pd.read_csv('federal_extracted.csv')
    print(f"📊 原始数据: {len(df)} 条记录")
    
    # 1. 添加Opening Balance记录
    df = add_opening_balance(df)
    
    # 2. 去掉Page列
    if 'Page' in df.columns:
        df = df.drop('Page', axis=1)
        print(f"✅ 已去掉Page列")
    
    # 3. 修复0值显示
    df = fix_zero_values(df)
    
    # 4. 保存处理后的数据
    save_processed_data(df)
    
    return df


def add_opening_balance(df):
    """添加Opening Balance记录"""
    print(f"\n📝 添加Opening Balance记录...")
    
    if len(df) == 0:
        print(f"❌ 没有数据")
        return df
    
    # 获取第一条记录的信息
    first_row = df.iloc[0]
    first_date = first_row['Date']
    first_value_date = first_row['Value Date']
    
    # 计算Opening Balance
    # Opening Balance = 第一条记录的余额 - 第一条记录的存款 + 第一条记录的支出
    opening_balance = first_row['Balance'] - first_row['Deposits'] + first_row['Withdrawals']
    
    # 创建Opening Balance记录
    opening_record = {
        'Date': '',  # Opening Balance的日期应该为空
        'Value Date': '',  # Opening Balance的Value Date应该为空
        'Particulars': 'Opening Balance',
        'Tran Type': '',
        'Cheque Details': '',
        'Withdrawals': 0.0,
        'Deposits': 0.0,
        'Balance': round(opening_balance, 2),  # 修复精度问题
        'Dr/Cr': 'CR'
    }
    
    # 如果有Page列，也添加
    if 'Page' in df.columns:
        opening_record['Page'] = 1
    
    # 插入到第一行
    opening_df = pd.DataFrame([opening_record])
    df = pd.concat([opening_df, df], ignore_index=True)
    
    print(f"✅ 已添加Opening Balance: ₹{opening_balance:,.2f}")
    
    return df


def fix_zero_values(df):
    """修复0值显示"""
    print(f"\n🔧 修复0值显示...")
    
    # 将Withdrawals和Deposits中的0.0替换为空字符串
    df.loc[df['Withdrawals'] == 0.0, 'Withdrawals'] = ''
    df.loc[df['Deposits'] == 0.0, 'Deposits'] = ''
    
    print(f"✅ 0值修复完成")
    
    return df


def save_processed_data(df):
    """保存处理后的数据"""
    print(f"\n📁 保存处理后的数据...")
    
    # 保存为CSV
    df.to_csv('federal_processed.csv', index=False, encoding='utf-8')
    
    # 保存为Excel
    df.to_excel('federal_processed.xlsx', index=False, engine='openpyxl')
    
    print(f"✅ 已保存:")
    print(f"  - CSV格式: federal_processed.csv")
    print(f"  - Excel格式: federal_processed.xlsx")
    
    # 显示前几条记录
    print(f"\n📋 前5条记录:")
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        w = row['Withdrawals'] if row['Withdrawals'] != '' else '(空)'
        d = row['Deposits'] if row['Deposits'] != '' else '(空)'
        print(f"  {i+1}. {row['Date']} | {row['Particulars'][:25]}... | W:{w} D:{d} | ₹{row['Balance']:,.2f}")


def main():
    """主函数"""
    print("🚀 启动Federal银行数据后处理")
    
    df = process_federal_data()
    
    print(f"\n🎉 Federal银行数据处理完成!")
    print(f"📊 最终记录数: {len(df)} 条")


if __name__ == "__main__":
    main()
