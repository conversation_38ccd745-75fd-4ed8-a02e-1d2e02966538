#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Federal银行PDF解析器
基于分析结果，使用表格提取策略解析Federal银行账单
"""

import os
import pandas as pd
import pdfplumber
import re
from datetime import datetime
from typing import List, Dict, Optional


class FederalPDFParser:
    """Federal银行PDF解析器"""
    
    def __init__(self):
        self.pdf_path = "../files/12-federal-426096568-AC-STATEMENT-2055XXXXXX9745-01JUL2019-10JUL2019-10072019191111-1.pdf"
        
        # Federal银行标准列名
        self.standard_columns = [
            'Date', 'Value Date', 'Particulars', 'Tran Type', 
            'Cheque Details', 'Withdrawals', 'Deposits', 'Balance', 'Dr/Cr'
        ]
        
    def parse_federal_pdf(self) -> pd.DataFrame:
        """解析Federal银行PDF"""
        print("=== Federal银行PDF解析器 ===")
        print("🎯 使用表格提取策略")
        
        if not os.path.exists(self.pdf_path):
            print(f"❌ PDF文件不存在: {self.pdf_path}")
            return pd.DataFrame()
        
        try:
            all_transactions = []
            
            with pdfplumber.open(self.pdf_path) as pdf:
                print(f"📄 PDF总页数: {len(pdf.pages)}")
                
                for page_num, page in enumerate(pdf.pages, 1):
                    print(f"\n📄 处理第{page_num}页...")
                    
                    # 提取交易数据
                    page_transactions = self._extract_transactions_from_page(page, page_num)
                    all_transactions.extend(page_transactions)
                    
                    print(f"    ✅ 第{page_num}页提取: {len(page_transactions)} 条记录")
            
            print(f"\n📊 总提取记录: {len(all_transactions)} 条")
            
            if all_transactions:
                # 转换为DataFrame
                df = pd.DataFrame(all_transactions)
                
                # 数据清洗和验证
                df = self._clean_and_validate_data(df)
                
                # 生成统计信息
                self._generate_statistics(df)
                
                return df
            else:
                print("❌ 未提取到任何数据")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"❌ 解析失败: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_transactions_from_page(self, page, page_num: int) -> List[Dict]:
        """从页面提取交易数据"""
        transactions = []
        
        try:
            tables = page.extract_tables()
            
            for table_idx, table in enumerate(tables):
                if table and len(table) > 1:
                    print(f"      📊 处理表格{table_idx + 1}: {len(table)}行")
                    
                    # 检查是否是交易数据表格
                    if self._is_transaction_table(table):
                        print(f"        ✅ 识别为交易数据表格")
                        
                        # 找到表头行
                        header_row_idx = self._find_header_row(table)
                        if header_row_idx is not None:
                            print(f"        📋 表头在第{header_row_idx + 1}行")
                            
                            # 提取数据行
                            for row_idx in range(header_row_idx + 1, len(table)):
                                row = table[row_idx]
                                transaction = self._parse_transaction_row(row, page_num, row_idx)
                                if transaction:
                                    transactions.append(transaction)
                        else:
                            print(f"        ⚠️ 未找到表头行")
                    else:
                        print(f"        ⚠️ 跳过非交易表格")
        
        except Exception as e:
            print(f"      ❌ 页面提取失败: {e}")
        
        return transactions
    
    def _is_transaction_table(self, table: List[List]) -> bool:
        """判断是否是交易数据表格"""
        try:
            # 检查表格是否有足够的列
            if not table or len(table[0]) < 7:
                return False
            
            # 查找包含关键字段的行
            for row in table:
                row_text = ' '.join([str(cell).strip() if cell else '' for cell in row])
                
                # 检查是否包含交易表格的关键字段
                key_fields = ['Date', 'Particulars', 'Withdrawals', 'Deposits', 'Balance']
                if sum(1 for field in key_fields if field in row_text) >= 3:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _find_header_row(self, table: List[List]) -> Optional[int]:
        """查找表头行"""
        try:
            for row_idx, row in enumerate(table):
                row_text = ' '.join([str(cell).strip() if cell else '' for cell in row])
                
                # 检查是否包含表头关键字段
                if ('Date' in row_text and 'Particulars' in row_text and 
                    'Balance' in row_text and ('Withdrawals' in row_text or 'Deposits' in row_text)):
                    return row_idx
            
            return None
            
        except Exception:
            return None
    
    def _parse_transaction_row(self, row: List, page_num: int, row_idx: int) -> Optional[Dict]:
        """解析交易行"""
        try:
            # 确保行有足够的列
            if not row or len(row) < 7:
                return None
            
            # 提取各字段
            date = str(row[0]).strip() if row[0] else ""
            value_date = str(row[1]).strip() if row[1] else ""
            particulars = str(row[2]).strip() if row[2] else ""
            tran_type = str(row[3]).strip() if row[3] else ""
            cheque_details = str(row[4]).strip() if row[4] else ""
            withdrawals = str(row[5]).strip() if row[5] else ""
            deposits = str(row[6]).strip() if row[6] else ""
            balance = str(row[7]).strip() if len(row) > 7 and row[7] else ""
            dr_cr = str(row[8]).strip() if len(row) > 8 and row[8] else ""
            
            # 验证日期格式
            if not self._is_valid_date(date):
                return None
            
            # 验证是否有金额信息
            if not withdrawals and not deposits:
                return None
            
            # 解析金额
            withdrawal_amount = self._parse_amount(withdrawals)
            deposit_amount = self._parse_amount(deposits)
            balance_amount = self._parse_amount(balance)
            
            # 构建交易记录
            transaction = {
                'Date': date,
                'Value Date': value_date,
                'Particulars': particulars,
                'Tran Type': tran_type,
                'Cheque Details': cheque_details,
                'Withdrawals': withdrawal_amount,
                'Deposits': deposit_amount,
                'Balance': balance_amount,
                'Dr/Cr': dr_cr,
                'Page': page_num
            }
            
            return transaction
            
        except Exception as e:
            print(f"        ⚠️ 第{row_idx}行解析失败: {e}")
            return None
    
    def _is_valid_date(self, date_str: str) -> bool:
        """验证日期格式"""
        try:
            # Federal银行日期格式: DD/MM/YYYY
            if re.match(r'\d{2}/\d{2}/\d{4}', date_str):
                return True
            return False
        except:
            return False
    
    def _parse_amount(self, amount_str: str) -> float:
        """解析金额"""
        try:
            if not amount_str or amount_str.strip() == '':
                return 0.0
            
            # 移除逗号和其他非数字字符，保留小数点
            cleaned = re.sub(r'[^\d.]', '', amount_str)
            
            if cleaned:
                return float(cleaned)
            else:
                return 0.0
                
        except:
            return 0.0
    
    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据清洗和验证"""
        print(f"\n🧹 数据清洗和验证...")
        
        original_count = len(df)
        
        # 1. 移除空行
        df = df.dropna(subset=['Date', 'Particulars'])
        
        # 2. 去重
        df = df.drop_duplicates()
        
        # 3. 按日期排序
        try:
            df['Date_parsed'] = pd.to_datetime(df['Date'], format='%d/%m/%Y')
            df = df.sort_values('Date_parsed').reset_index(drop=True)
            df = df.drop('Date_parsed', axis=1)
        except:
            print("    ⚠️ 日期排序失败，保持原顺序")
        
        # 4. 数据验证
        valid_transactions = 0
        for _, row in df.iterrows():
            if (row['Withdrawals'] > 0 or row['Deposits'] > 0) and row['Balance'] > 0:
                valid_transactions += 1
        
        print(f"    📊 原始记录: {original_count} 条")
        print(f"    📊 清洗后记录: {len(df)} 条")
        print(f"    📊 有效交易: {valid_transactions} 条")
        
        return df
    
    def _generate_statistics(self, df: pd.DataFrame):
        """生成统计信息"""
        print(f"\n📊 交易统计:")
        
        try:
            # 基本统计
            total_withdrawals = df['Withdrawals'].sum()
            total_deposits = df['Deposits'].sum()
            
            withdrawal_count = (df['Withdrawals'] > 0).sum()
            deposit_count = (df['Deposits'] > 0).sum()
            
            print(f"  💰 总支出: ₹{total_withdrawals:,.2f} ({withdrawal_count} 笔)")
            print(f"  💰 总收入: ₹{total_deposits:,.2f} ({deposit_count} 笔)")
            print(f"  💰 净变化: ₹{total_deposits - total_withdrawals:,.2f}")
            
            # 余额信息
            if len(df) > 0:
                opening_balance = df.iloc[0]['Balance'] - df.iloc[0]['Deposits'] + df.iloc[0]['Withdrawals']
                closing_balance = df.iloc[-1]['Balance']
                print(f"  💰 期初余额: ₹{opening_balance:,.2f}")
                print(f"  💰 期末余额: ₹{closing_balance:,.2f}")
            
            # 交易类型统计
            if 'Tran Type' in df.columns:
                tran_types = df['Tran Type'].value_counts()
                print(f"  📋 交易类型分布:")
                for tran_type, count in tran_types.head(5).items():
                    print(f"    {tran_type}: {count} 笔")
                    
        except Exception as e:
            print(f"    ⚠️ 统计生成失败: {e}")
    
    def save_results(self, df: pd.DataFrame) -> str:
        """保存解析结果"""
        if df.empty:
            print("❌ 没有数据可保存")
            return ""
        
        # 保存为多种格式
        csv_file = "federal_extracted.csv"
        excel_file = "federal_extracted.xlsx"
        json_file = "federal_extracted.json"
        
        try:
            # CSV格式
            df.to_csv(csv_file, index=False, encoding='utf-8')
            
            # Excel格式
            df.to_excel(excel_file, index=False, engine='openpyxl')
            
            # JSON格式
            df.to_json(json_file, orient='records', indent=2, force_ascii=False)
            
            print(f"\n📁 解析结果已保存:")
            print(f"  - CSV格式: {csv_file}")
            print(f"  - Excel格式: {excel_file}")
            print(f"  - JSON格式: {json_file}")
            
            return csv_file
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return ""


def main():
    """主函数"""
    parser = FederalPDFParser()
    
    print("🚀 启动Federal银行PDF解析器")
    
    df = parser.parse_federal_pdf()
    
    if not df.empty:
        output_file = parser.save_results(df)
        
        print(f"\n🎉 Federal银行PDF解析完成!")
        print(f"📊 提取记录数: {len(df)} 条")
        print(f"📁 主要输出文件: {output_file}")
        
        return df
    else:
        print(f"\n❌ Federal银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
