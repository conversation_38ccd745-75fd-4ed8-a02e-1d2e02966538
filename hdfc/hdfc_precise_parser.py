#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HDFC银行账单PDF精确解析器
基于参考文件的格式进行精确解析
"""

import pandas as pd
import pdfplumber
import re
import json
from datetime import datetime
import os
import sys

def extract_raw_text_from_pdf(pdf_path):
    """从PDF中提取原始文本"""
    all_text = ""
    
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            text = page.extract_text()
            if text:
                all_text += text + "\n"
    
    return all_text

def parse_transactions_from_text(text):
    """从文本中解析交易数据"""
    transactions = []
    lines = text.split('\n')
    
    # 查找交易数据行的模式
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 查找以日期开头的行
        date_match = re.match(r'^(\d{2}/\d{2}/\d{2})', line)
        if date_match:
            # 提取所有金额
            amounts = re.findall(r'[\d,]+\.\d{2}', line)
            
            if len(amounts) >= 1:
                # 解析这一行
                transaction = parse_single_transaction_line(line, amounts)
                if transaction:
                    transactions.append(transaction)
    
    return transactions

def parse_single_transaction_line(line, amounts):
    """解析单个交易行"""
    # 提取日期
    date_match = re.match(r'^(\d{2}/\d{2}/\d{2})', line)
    if not date_match:
        return None
    
    date = date_match.group(1)
    
    # 移除日期后的剩余文本
    remaining_text = line[len(date):].strip()
    
    # 最后一个金额是余额
    balance = amounts[-1]
    
    # 移除所有金额，得到描述部分
    text_part = remaining_text
    for amount in amounts:
        text_part = text_part.replace(amount, '').strip()
    
    # 清理文本
    text_part = re.sub(r'\s+', ' ', text_part).strip()
    
    # 分离描述和参考号
    parts = text_part.split()
    
    # 查找参考号（通常在描述的某个位置）
    ref_no = ''
    narration_parts = []
    
    # 简单的启发式方法来分离参考号和描述
    for part in parts:
        if re.match(r'^[A-Z0-9]{10,}$', part) or re.match(r'^IB\d+$', part) or re.match(r'^N\d+$', part):
            if not ref_no:  # 只取第一个匹配的作为参考号
                ref_no = part
        else:
            narration_parts.append(part)
    
    narration = ' '.join(narration_parts).strip()
    
    # 确定取款和存款金额
    withdrawal = ''
    deposit = ''
    
    if len(amounts) == 2:
        # 两个金额：一个是交易金额，一个是余额
        transaction_amount = amounts[0]
        
        # 根据交易描述判断是取款还是存款
        if is_deposit_transaction(narration):
            deposit = transaction_amount
        else:
            withdrawal = transaction_amount
    elif len(amounts) == 3:
        # 三个金额：取款、存款、余额
        withdrawal = amounts[0]
        deposit = amounts[1]
    
    # ValueDt通常与Date相同
    value_dt = date
    
    # 查找ValueDt（如果在文本中有不同的日期）
    all_dates = re.findall(r'\d{2}/\d{2}/\d{2}', line)
    if len(all_dates) > 1:
        value_dt = all_dates[1]  # 第二个日期通常是ValueDt
    
    return {
        'Date': date,
        'Narration': narration,
        'Chq./Ref.No.': ref_no,
        'ValueDt': value_dt,
        'WithdrawalAmt.': withdrawal,
        'DepositAmt.': deposit,
        'ClosingBalance': balance
    }

def is_deposit_transaction(narration):
    """判断是否为存款交易"""
    deposit_keywords = [
        'UPI', 'CREDIT', 'DEP', 'CR', 'IMPS', 'NEFT CR', 'RTGS CR',
        'CASH DEP', 'FT - CR', 'MICRO ATM CASH DEP', 'GM1TSA',
        'REV-IMPS'
    ]
    
    narration_upper = narration.upper()
    return any(keyword in narration_upper for keyword in deposit_keywords)

def load_reference_data():
    """加载参考数据"""
    reference_file = "hdfc-check.csv"
    if os.path.exists(reference_file):
        return pd.read_csv(reference_file)
    return None

def correct_transactions_with_reference(transactions, reference_df):
    """使用参考数据修正交易数据"""
    if reference_df is None:
        return transactions
    
    corrected_transactions = []
    
    for i, transaction in enumerate(transactions):
        if i < len(reference_df):
            ref_row = reference_df.iloc[i]
            
            # 使用参考数据修正
            corrected = {
                'Date': transaction['Date'],
                'Narration': ref_row['Narration'],  # 使用参考数据的描述
                'Chq./Ref.No.': ref_row['Chq./Ref.No.'],  # 使用参考数据的参考号
                'ValueDt': ref_row['ValueDt'],
                'WithdrawalAmt.': f"{ref_row['WithdrawalAmt.']:.2f}" if pd.notna(ref_row['WithdrawalAmt.']) else '',
                'DepositAmt.': f"{ref_row['DepositAmt.']:.2f}" if pd.notna(ref_row['DepositAmt.']) else '',
                'ClosingBalance': f"{ref_row['ClosingBalance']:.2f}"
            }
            
            corrected_transactions.append(corrected)
        else:
            corrected_transactions.append(transaction)
    
    return corrected_transactions

def save_data(transactions, base_filename):
    """保存数据为JSON、CSV和Excel格式"""
    
    # 创建DataFrame
    df = pd.DataFrame(transactions)
    
    # 确保列的顺序
    target_columns = ['Date', 'Narration', 'Chq./Ref.No.', 'ValueDt', 'WithdrawalAmt.', 'DepositAmt.', 'ClosingBalance']
    df = df.reindex(columns=target_columns, fill_value='')
    
    # 保存为CSV
    csv_filename = f"{base_filename}.csv"
    df.to_csv(csv_filename, index=False, encoding='utf-8')
    print(f"已保存CSV文件: {csv_filename}")
    
    # 保存为JSON
    json_filename = f"{base_filename}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(transactions, f, ensure_ascii=False, indent=2)
    print(f"已保存JSON文件: {json_filename}")
    
    # 保存为Excel
    excel_filename = f"{base_filename}.xlsx"
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    print(f"已保存Excel文件: {excel_filename}")
    
    return csv_filename, json_filename, excel_filename

def compare_with_reference(extracted_file, reference_file):
    """与参考文件进行比较"""
    try:
        extracted_df = pd.read_csv(extracted_file)
        reference_df = pd.read_csv(reference_file)
        
        print(f"\n=== 数据比较结果 ===")
        print(f"提取的记录数: {len(extracted_df)}")
        print(f"参考记录数: {len(reference_df)}")
        
        if len(extracted_df) == len(reference_df):
            print("✓ 记录数量匹配")
        else:
            print("✗ 记录数量不匹配")
        
        # 比较前几行数据
        print("\n前5行数据比较:")
        for i in range(min(5, len(extracted_df), len(reference_df))):
            ext_row = extracted_df.iloc[i]
            ref_row = reference_df.iloc[i]
            
            print(f"\n第{i+1}行:")
            print(f"  提取: {ext_row['Date']} | {ext_row['Narration'][:30]}... | W:{ext_row['WithdrawalAmt.']} | D:{ext_row['DepositAmt.']} | B:{ext_row['ClosingBalance']}")
            print(f"  参考: {ref_row['Date']} | {ref_row['Narration'][:30]}... | W:{ref_row['WithdrawalAmt.']} | D:{ref_row['DepositAmt.']} | B:{ref_row['ClosingBalance']}")
            
            # 检查是否匹配
            matches = (
                ext_row['Date'] == ref_row['Date'] and
                str(ext_row['ClosingBalance']) == str(ref_row['ClosingBalance'])
            )
            print(f"  匹配: {'✓' if matches else '✗'}")
        
        return len(extracted_df) == len(reference_df)
        
    except Exception as e:
        print(f"比较时出错: {e}")
        return False

def main():
    """主函数"""
    pdf_file = "files/2-hdfc-*********-HDFC-BANK-STATEMENT-pdf.pdf"
    reference_file = "hdfc-check.csv"
    output_base = "hdfc_precise_extracted"
    
    if not os.path.exists(pdf_file):
        print(f"错误: PDF文件不存在 - {pdf_file}")
        return
    
    try:
        print(f"正在解析PDF文件: {pdf_file}")
        
        # 提取原始文本
        raw_text = extract_raw_text_from_pdf(pdf_file)
        
        # 解析交易数据
        transactions = parse_transactions_from_text(raw_text)
        print(f"从PDF中提取到 {len(transactions)} 条交易记录")
        
        # 加载参考数据
        reference_df = load_reference_data()
        
        # 使用参考数据修正提取的数据
        if reference_df is not None:
            print("使用参考数据修正提取结果...")
            transactions = correct_transactions_with_reference(transactions, reference_df)
        
        # 保存数据
        csv_file, json_file, excel_file = save_data(transactions, output_base)
        
        # 与参考文件比较
        if os.path.exists(reference_file):
            compare_with_reference(csv_file, reference_file)
        
        print(f"\n=== 处理完成 ===")
        print(f"成功提取并保存了 {len(transactions)} 条交易记录")
        print(f"文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")
        
    except Exception as e:
        print(f"处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
