#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的85条数据逐行比对验证
"""

import pandas as pd
import numpy as np

def compare_all_records():
    """比对全部85条记录"""
    
    print("=" * 80)
    print("HDFC银行账单PDF解析 - 全部85条数据逐行比对验证")
    print("=" * 80)
    
    try:
        # 加载数据
        extracted_df = pd.read_csv("hdfc_precise_extracted.csv")
        reference_df = pd.read_csv("hdfc-check.csv")
        
        print(f"\n📊 数据概览:")
        print(f"   提取文件记录数: {len(extracted_df)}")
        print(f"   参考文件记录数: {len(reference_df)}")
        
        if len(extracted_df) != len(reference_df):
            print("❌ 记录数量不匹配！")
            return
        
        print(f"✅ 记录数量匹配: {len(extracted_df)} 条")
        
        # 逐行比对
        print(f"\n🔍 开始逐行比对全部 {len(extracted_df)} 条记录...")
        print("-" * 80)
        
        total_matches = 0
        mismatches = []
        
        for i in range(len(extracted_df)):
            ext_row = extracted_df.iloc[i]
            ref_row = reference_df.iloc[i]
            
            # 比对各个字段
            date_match = str(ext_row['Date']).strip() == str(ref_row['Date']).strip()
            
            # 处理NaN值的比较
            def safe_compare(val1, val2):
                if pd.isna(val1) and pd.isna(val2):
                    return True
                if pd.isna(val1) or pd.isna(val2):
                    return str(val1).strip() == '' and str(val2).strip() == ''
                return str(val1).strip() == str(val2).strip()
            
            narration_match = safe_compare(ext_row['Narration'], ref_row['Narration'])
            ref_no_match = safe_compare(ext_row['Chq./Ref.No.'], ref_row['Chq./Ref.No.'])
            value_dt_match = safe_compare(ext_row['ValueDt'], ref_row['ValueDt'])
            
            # 金额比较（处理NaN和空值）
            def compare_amount(val1, val2):
                # 处理NaN
                if pd.isna(val1) and pd.isna(val2):
                    return True
                if pd.isna(val1):
                    val1 = ''
                if pd.isna(val2):
                    val2 = ''
                
                # 转换为字符串并清理
                str1 = str(val1).strip()
                str2 = str(val2).strip()
                
                # 如果都为空，则匹配
                if str1 == '' and str2 == '':
                    return True
                
                # 尝试数值比较
                try:
                    if str1 == '':
                        num1 = 0
                    else:
                        num1 = float(str1.replace(',', ''))
                    
                    if str2 == '':
                        num2 = 0
                    else:
                        num2 = float(str2.replace(',', ''))
                    
                    return abs(num1 - num2) < 0.01  # 允许0.01的误差
                except:
                    return str1 == str2
            
            withdrawal_match = compare_amount(ext_row['WithdrawalAmt.'], ref_row['WithdrawalAmt.'])
            deposit_match = compare_amount(ext_row['DepositAmt.'], ref_row['DepositAmt.'])
            balance_match = compare_amount(ext_row['ClosingBalance'], ref_row['ClosingBalance'])
            
            # 判断整行是否匹配
            row_match = all([date_match, narration_match, ref_no_match, value_dt_match, 
                           withdrawal_match, deposit_match, balance_match])
            
            if row_match:
                total_matches += 1
                print(f"✅ 第{i+1:2d}行: {ext_row['Date']} | {ext_row['Narration'][:40]:<40} | 余额: ₹{ext_row['ClosingBalance']}")
            else:
                mismatches.append(i + 1)
                print(f"❌ 第{i+1:2d}行: 不匹配")
                print(f"     提取: {ext_row['Date']} | {ext_row['Narration'][:40]}")
                print(f"     参考: {ref_row['Date']} | {ref_row['Narration'][:40]}")
                
                # 详细显示不匹配的字段
                if not date_match:
                    print(f"       日期不匹配: '{ext_row['Date']}' vs '{ref_row['Date']}'")
                if not narration_match:
                    print(f"       描述不匹配")
                if not ref_no_match:
                    print(f"       参考号不匹配: '{ext_row['Chq./Ref.No.']}' vs '{ref_row['Chq./Ref.No.']}'")
                if not withdrawal_match:
                    print(f"       取款金额不匹配: '{ext_row['WithdrawalAmt.']}' vs '{ref_row['WithdrawalAmt.']}'")
                if not deposit_match:
                    print(f"       存款金额不匹配: '{ext_row['DepositAmt.']}' vs '{ref_row['DepositAmt.']}'")
                if not balance_match:
                    print(f"       余额不匹配: '{ext_row['ClosingBalance']}' vs '{ref_row['ClosingBalance']}'")
                print()
        
        print("-" * 80)
        print(f"\n📈 比对结果统计:")
        print(f"   总记录数: {len(extracted_df)}")
        print(f"   匹配记录数: {total_matches}")
        print(f"   不匹配记录数: {len(mismatches)}")
        print(f"   匹配率: {total_matches/len(extracted_df)*100:.2f}%")
        
        if len(mismatches) == 0:
            print(f"\n🎉 恭喜！全部 {len(extracted_df)} 条记录完全匹配！")
            print("✅ 数据解析准确率: 100%")
        else:
            print(f"\n⚠️  发现 {len(mismatches)} 条不匹配记录:")
            print(f"   不匹配行号: {mismatches}")
        
        # 额外的数据完整性检查
        print(f"\n🔍 数据完整性检查:")
        
        # 检查空值
        empty_dates = extracted_df['Date'].isna().sum()
        empty_narrations = extracted_df['Narration'].apply(lambda x: pd.isna(x) or str(x).strip() == '').sum()
        empty_balances = extracted_df['ClosingBalance'].apply(lambda x: pd.isna(x) or str(x).strip() == '').sum()
        
        print(f"   空日期字段: {empty_dates} 个")
        print(f"   空描述字段: {empty_narrations} 个")
        print(f"   空余额字段: {empty_balances} 个")
        
        # 检查金额字段的分布
        withdrawal_count = extracted_df['WithdrawalAmt.'].apply(
            lambda x: not pd.isna(x) and str(x).strip() != ''
        ).sum()
        deposit_count = extracted_df['DepositAmt.'].apply(
            lambda x: not pd.isna(x) and str(x).strip() != ''
        ).sum()
        
        print(f"   有取款金额的记录: {withdrawal_count} 条")
        print(f"   有存款金额的记录: {deposit_count} 条")
        print(f"   总交易记录: {withdrawal_count + deposit_count} 条")
        
        # 验证每行都有且仅有一个金额（取款或存款）
        both_amounts = 0
        no_amounts = 0
        
        for i in range(len(extracted_df)):
            row = extracted_df.iloc[i]
            has_withdrawal = not pd.isna(row['WithdrawalAmt.']) and str(row['WithdrawalAmt.']).strip() != ''
            has_deposit = not pd.isna(row['DepositAmt.']) and str(row['DepositAmt.']).strip() != ''
            
            if has_withdrawal and has_deposit:
                both_amounts += 1
            elif not has_withdrawal and not has_deposit:
                no_amounts += 1
        
        print(f"   同时有取款和存款的记录: {both_amounts} 条")
        print(f"   既无取款也无存款的记录: {no_amounts} 条")
        
        if both_amounts == 0 and no_amounts == 0:
            print("✅ 每条记录都有且仅有一个交易金额（取款或存款）")
        
        print("\n" + "=" * 80)
        
        return total_matches == len(extracted_df)
        
    except Exception as e:
        print(f"❌ 比对过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = compare_all_records()
    if success:
        print("🎯 验证完成：数据解析100%准确！")
    else:
        print("⚠️  验证发现问题，请检查上述不匹配项。")
