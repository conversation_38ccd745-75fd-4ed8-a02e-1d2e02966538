#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HDFC银行账单PDF解析器
从PDF中提取交易数据并保存为JSON、CSV和Excel格式
"""

import pandas as pd
import pdfplumber
import re
import json
from datetime import datetime
import os
import sys

def clean_text(text):
    """清理文本，去除多余的空格和换行符"""
    if not text:
        return ""
    # 替换多个空格为单个空格
    text = re.sub(r'\s+', ' ', text.strip())
    return text

def parse_amount(amount_str):
    """解析金额字符串，返回浮点数"""
    if not amount_str or amount_str.strip() == '':
        return None
    
    # 移除逗号和其他非数字字符（除了小数点）
    amount_str = re.sub(r'[^\d.-]', '', amount_str.strip())
    
    try:
        return float(amount_str) if amount_str else None
    except ValueError:
        return None

def parse_date(date_str):
    """解析日期字符串"""
    if not date_str:
        return ""
    
    date_str = date_str.strip()
    # 尝试解析DD/MM/YY格式
    try:
        if '/' in date_str and len(date_str.split('/')) == 3:
            return date_str
    except:
        pass
    
    return date_str

def extract_transaction_data_from_pdf(pdf_path):
    """从PDF中提取交易数据"""
    transactions = []

    print(f"正在解析PDF文件: {pdf_path}")

    with pdfplumber.open(pdf_path) as pdf:
        print(f"PDF总页数: {len(pdf.pages)}")

        # 首先尝试使用文本提取方法
        all_text = ""
        for page_num, page in enumerate(pdf.pages, 1):
            print(f"正在处理第 {page_num} 页...")
            text = page.extract_text()
            if text:
                all_text += text + "\n"

        # 解析文本中的交易数据
        transactions = parse_text_transactions(all_text)

        # 如果文本解析失败，尝试表格提取
        if not transactions:
            print("文本解析失败，尝试表格提取...")
            for page_num, page in enumerate(pdf.pages, 1):
                print(f"正在处理第 {page_num} 页...")

                # 提取表格数据
                tables = page.extract_tables()

                if tables:
                    for table_idx, table in enumerate(tables):
                        print(f"  找到表格 {table_idx + 1}，行数: {len(table)}")

                        # 查找表头
                        header_row = None
                        for i, row in enumerate(table):
                            if row and any(cell and 'Date' in str(cell) for cell in row):
                                header_row = i
                                break

                        if header_row is not None:
                            headers = table[header_row]
                            print(f"  找到表头: {headers}")

                            # 处理数据行
                            for row_idx in range(header_row + 1, len(table)):
                                row = table[row_idx]
                                if not row or all(not cell or str(cell).strip() == '' for cell in row):
                                    continue

                                # 确保行有足够的列
                                while len(row) < len(headers):
                                    row.append('')

                                # 创建交易记录
                                transaction = {}
                                for col_idx, header in enumerate(headers):
                                    if header and col_idx < len(row):
                                        cell_value = clean_text(str(row[col_idx]) if row[col_idx] else '')
                                        transaction[clean_text(str(header))] = cell_value

                                # 只添加有效的交易记录（至少有日期）
                                if any(key for key in transaction.keys() if 'Date' in key and transaction[key]):
                                    transactions.append(transaction)

    print(f"总共提取到 {len(transactions)} 条交易记录")
    return transactions

def parse_text_transactions(text):
    """从文本中解析交易数据"""
    transactions = []

    # 按行分割文本
    lines = text.split('\n')

    # 查找交易数据的模式 - 更精确的模式
    # 格式: Date Narration RefNo ValueDate [WithdrawalAmt] [DepositAmt] ClosingBalance

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 查找日期模式开始的行
        date_match = re.match(r'^(\d{2}/\d{2}/\d{2})\s+(.+)', line)
        if date_match:
            date = date_match.group(1)
            rest_of_line = date_match.group(2).strip()

            # 从行尾提取数字（余额、存款、取款）
            # 查找所有的金额（格式：数字,数字.数字）
            amounts = re.findall(r'[\d,]+\.\d{2}', rest_of_line)

            if len(amounts) >= 1:
                # 最后一个金额总是余额
                balance = amounts[-1]

                # 移除金额后的文本部分
                text_part = rest_of_line
                for amount in amounts:
                    text_part = text_part.replace(amount, '').strip()

                # 清理多余的空格
                text_part = re.sub(r'\s+', ' ', text_part).strip()

                # 分离描述和参考号
                # 参考号通常在最后，是字母数字组合
                parts = text_part.split()
                if parts:
                    # 查找可能的参考号（通常是最后几个部分中的一个）
                    ref_no = ''
                    narration_parts = parts[:]

                    # 从后往前查找参考号
                    for i in range(len(parts) - 1, -1, -1):
                        part = parts[i]
                        # 参考号通常包含数字和字母，或者是纯数字
                        if re.match(r'^[A-Z0-9]+$', part) and len(part) > 5:
                            ref_no = part
                            narration_parts = parts[:i] + parts[i+1:]
                            break

                    narration = ' '.join(narration_parts).strip()

                    # 确定取款和存款金额
                    withdrawal = ''
                    deposit = ''

                    if len(amounts) == 3:
                        # 有三个金额：取款、存款、余额
                        withdrawal = amounts[0]
                        deposit = amounts[1]
                    elif len(amounts) == 2:
                        # 有两个金额：可能是取款+余额 或 存款+余额
                        # 需要根据交易类型判断
                        amount_val = float(amounts[0].replace(',', ''))
                        prev_balance = float(balance.replace(',', ''))

                        # 简单的启发式判断：如果描述包含某些关键词
                        if any(keyword in narration.upper() for keyword in ['UPI', 'CREDIT', 'DEP', 'CR']):
                            deposit = amounts[0]
                        else:
                            withdrawal = amounts[0]

                    # 查找ValueDt（通常与Date相同，或在描述中）
                    value_dt_match = re.search(r'(\d{2}/\d{2}/\d{2})', rest_of_line)
                    value_dt = value_dt_match.group(1) if value_dt_match else date

                    transaction = {
                        'Date': date,
                        'Narration': narration,
                        'Chq./Ref.No.': ref_no,
                        'ValueDt': value_dt,
                        'WithdrawalAmt.': withdrawal,
                        'DepositAmt.': deposit,
                        'ClosingBalance': balance
                    }

                    transactions.append(transaction)

    # 如果解析失败，尝试更简单的行解析
    if not transactions:
        print("文本解析失败，尝试简单行解析...")
        transactions = parse_lines_simple(lines)

    return transactions

def parse_lines_simple(lines):
    """简单的行解析方法"""
    transactions = []

    # 查找包含日期的行
    date_pattern = r'\d{2}/\d{2}/\d{2}'

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 检查是否包含日期
        dates = re.findall(date_pattern, line)
        if dates:
            # 尝试分割行数据
            parts = line.split()
            if len(parts) >= 7:  # 至少需要7个部分
                # 这是一个简化的解析，可能需要根据实际PDF格式调整
                transaction = {
                    'Date': dates[0] if dates else '',
                    'Narration': ' '.join(parts[1:-5]) if len(parts) > 6 else '',
                    'Chq./Ref.No.': parts[-5] if len(parts) > 5 else '',
                    'ValueDt': dates[1] if len(dates) > 1 else dates[0] if dates else '',
                    'WithdrawalAmt.': parts[-3] if len(parts) > 3 and ',' in parts[-3] else '',
                    'DepositAmt.': parts[-2] if len(parts) > 2 and ',' in parts[-2] else '',
                    'ClosingBalance': parts[-1] if len(parts) > 1 and ',' in parts[-1] else ''
                }

                if transaction['Date']:
                    transactions.append(transaction)

    return transactions

def normalize_transaction_data(transactions):
    """标准化交易数据，确保列名与参考文件一致"""
    normalized_transactions = []
    
    # 参考文件的列名
    target_columns = ['Date', 'Narration', 'Chq./Ref.No.', 'ValueDt', 'WithdrawalAmt.', 'DepositAmt.', 'ClosingBalance']
    
    for transaction in transactions:
        normalized = {}
        
        # 映射列名
        for target_col in target_columns:
            normalized[target_col] = ''
            
            # 查找匹配的列
            for key, value in transaction.items():
                key_lower = key.lower()
                target_lower = target_col.lower()
                
                if target_lower in key_lower or key_lower in target_lower:
                    normalized[target_col] = value
                    break
                
                # 特殊映射
                if target_col == 'Date' and ('date' in key_lower or 'dt' in key_lower):
                    normalized[target_col] = parse_date(value)
                elif target_col == 'Narration' and ('narration' in key_lower or 'description' in key_lower or 'particulars' in key_lower):
                    normalized[target_col] = clean_text(value)
                elif target_col == 'Chq./Ref.No.' and ('chq' in key_lower or 'ref' in key_lower or 'cheque' in key_lower):
                    normalized[target_col] = clean_text(value)
                elif target_col == 'ValueDt' and ('value' in key_lower and 'dt' in key_lower):
                    normalized[target_col] = parse_date(value)
                elif target_col == 'WithdrawalAmt.' and ('withdrawal' in key_lower or 'debit' in key_lower or 'dr' in key_lower):
                    amount = parse_amount(value)
                    normalized[target_col] = f"{amount:.2f}" if amount else ''
                elif target_col == 'DepositAmt.' and ('deposit' in key_lower or 'credit' in key_lower or 'cr' in key_lower):
                    amount = parse_amount(value)
                    normalized[target_col] = f"{amount:.2f}" if amount else ''
                elif target_col == 'ClosingBalance' and ('balance' in key_lower or 'closing' in key_lower):
                    amount = parse_amount(value)
                    normalized[target_col] = f"{amount:.2f}" if amount else ''
        
        # 只添加有日期的记录
        if normalized['Date']:
            normalized_transactions.append(normalized)
    
    return normalized_transactions

def save_data(transactions, base_filename):
    """保存数据为JSON、CSV和Excel格式"""

    # 创建DataFrame
    df = pd.DataFrame(transactions)

    # 确保列的顺序与参考文件一致
    target_columns = ['Date', 'Narration', 'Chq./Ref.No.', 'ValueDt', 'WithdrawalAmt.', 'DepositAmt.', 'ClosingBalance']

    # 重新排列列的顺序
    df = df.reindex(columns=target_columns, fill_value='')

    # 保存为CSV，使用正确的分隔符和引用设置
    csv_filename = f"{base_filename}.csv"
    df.to_csv(csv_filename, index=False, encoding='utf-8', quoting=1)  # quoting=1 表示引用所有非数字字段
    print(f"已保存CSV文件: {csv_filename}")

    # 保存为JSON
    json_filename = f"{base_filename}.json"
    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(transactions, f, ensure_ascii=False, indent=2)
    print(f"已保存JSON文件: {json_filename}")

    # 保存为Excel
    excel_filename = f"{base_filename}.xlsx"
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    print(f"已保存Excel文件: {excel_filename}")

    return csv_filename, json_filename, excel_filename

def compare_with_reference(extracted_file, reference_file):
    """与参考文件进行比较"""
    try:
        extracted_df = pd.read_csv(extracted_file)
        reference_df = pd.read_csv(reference_file)
        
        print(f"\n=== 数据比较结果 ===")
        print(f"提取的记录数: {len(extracted_df)}")
        print(f"参考记录数: {len(reference_df)}")
        
        if len(extracted_df) == len(reference_df):
            print("✓ 记录数量匹配")
        else:
            print("✗ 记录数量不匹配")
        
        # 比较列名
        extracted_cols = set(extracted_df.columns)
        reference_cols = set(reference_df.columns)
        
        if extracted_cols == reference_cols:
            print("✓ 列名完全匹配")
        else:
            print("✗ 列名不匹配")
            print(f"  缺失列: {reference_cols - extracted_cols}")
            print(f"  多余列: {extracted_cols - reference_cols}")
        
        return len(extracted_df) == len(reference_df) and extracted_cols == reference_cols
        
    except Exception as e:
        print(f"比较时出错: {e}")
        return False

def create_reference_based_parser():
    """基于参考文件创建更精确的解析器"""
    # 读取参考文件来了解数据格式
    reference_file = "hdfc-check.csv"
    if os.path.exists(reference_file):
        ref_df = pd.read_csv(reference_file)
        print(f"参考文件包含 {len(ref_df)} 条记录")

        # 分析参考数据的模式
        print("分析参考数据模式...")
        for i, row in ref_df.head(10).iterrows():
            print(f"  {row['Date']} | {row['Narration'][:50]}... | W:{row['WithdrawalAmt.']} | D:{row['DepositAmt.']} | B:{row['ClosingBalance']}")

def main():
    """主函数"""
    pdf_file = "files/2-hdfc-*********-HDFC-BANK-STATEMENT-pdf.pdf"
    reference_file = "hdfc-check.csv"
    output_base = "hdfc_extracted"

    if not os.path.exists(pdf_file):
        print(f"错误: PDF文件不存在 - {pdf_file}")
        return

    try:
        # 分析参考文件
        create_reference_based_parser()

        # 提取数据
        transactions = extract_transaction_data_from_pdf(pdf_file)

        if not transactions:
            print("未能从PDF中提取到任何交易数据")
            return

        # 标准化数据
        normalized_transactions = normalize_transaction_data(transactions)

        if not normalized_transactions:
            print("数据标准化后没有有效记录")
            return

        # 保存数据
        csv_file, json_file, excel_file = save_data(normalized_transactions, output_base)

        # 与参考文件比较
        if os.path.exists(reference_file):
            compare_with_reference(csv_file, reference_file)

        print(f"\n=== 处理完成 ===")
        print(f"成功提取并保存了 {len(normalized_transactions)} 条交易记录")

    except Exception as e:
        print(f"处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
