#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CBI银行PDF解析器 - 重新设计版本
采用四列标准提取和混合列智能分离策略
精确处理Post Date/Details混合列和Balance格式保真
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Any
from enum import Enum


class CBIPDFParser:
    """CBI银行PDF解析器 - 重新设计版本"""

    def __init__(self):
        """初始化解析器"""
        # CBI银行的标准六列输出格式
        self.expected_columns = [
            'Post Date', 'Value Date', 'Details', 'Debit', 'Credit', 'Balance'
        ]

        # 日期模式：DD/MM/YY格式
        self.date_pattern = r'\d{2}/\d{2}/\d{2}'

        # 非交易记录关键词
        self.non_transaction_keywords = [
            'BROUGHT FORWARD', 'CARRIED FORWARD', 'CLOSING BALANCE',
            'DR. COUNT', 'CR. COUNT', 'STATEMENT SUMMARY'
        ]

        # 页面格式内容关键词
        self.page_format_keywords = [
            'PAGE NO', 'STATEMENT OF ACCOUNT', 'CENTRAL BANK OF INDIA',
            'In Case Your Account Is Operated By A Letter Of Authority',
            'Toll Free No', 'VALUE', 'POST', 'DETAILS', 'DEBIT', 'CREDIT', 'BALANCE'
        ]
        
    def parse_cbi_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析CBI银行PDF - 重新设计的四列标准提取策略

        Args:
            pdf_path: PDF文件路径

        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"CBI银行PDF解析器 - 四列标准提取策略")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")

        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()

        try:
            # 第一步：获取PDF页数
            total_pages = self._get_pdf_page_count(pdf_path)
            print(f"\n📄 PDF总页数: {total_pages}")

            # 第二步：逐页提取四列标准数据
            all_transactions = []

            for page_num in range(1, total_pages + 1):
                print(f"\n🔄 处理第{page_num}页...")
                page_data = self._extract_four_column_data(pdf_path, page_num)

                if not page_data.empty:
                    all_transactions.append(page_data)
                    print(f"  ✅ 第{page_num}页提取 {len(page_data)} 条交易")
                else:
                    print(f"  ⚠️ 第{page_num}页没有提取到数据")

            if not all_transactions:
                print("❌ 所有页面都没有提取到数据")
                return pd.DataFrame()

            # 第三步：合并所有页面的数据
            df_final = pd.concat(all_transactions, ignore_index=True)

            # 第四步：数据清理和验证
            df_final = self._clean_and_validate_data(df_final)

            print(f"\n✅ CBI银行PDF解析完成！总计提取 {len(df_final)} 条交易")
            return df_final

        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _get_pdf_page_count(self, pdf_path: str) -> int:
        """获取PDF页数"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                return len(reader.pages)
        except Exception as e:
            print(f"❌ 获取PDF页数失败: {e}")
            return 0

    def _extract_four_column_data(self, pdf_path: str, page_num: int) -> pd.DataFrame:
        """
        提取四列标准数据：混合列(Post Date+Details), Value Date, Debit, Credit, Balance

        Args:
            pdf_path: PDF文件路径
            page_num: 页码

        Returns:
            pd.DataFrame: 提取的交易数据
        """
        try:
            # 使用stream模式进行精确提取
            dfs = tabula.read_pdf(pdf_path, pages=str(page_num),
                                stream=True, area=[0, 0, 800, 600],
                                pandas_options={'header': None, 'dtype': str})

            if not dfs:
                return pd.DataFrame()

            # 处理提取到的表格
            all_page_transactions = []

            for df in dfs:
                if df.empty:
                    continue

                # 过滤页面格式内容
                filtered_df = self._filter_page_format_content(df)

                if filtered_df.empty:
                    continue

                # 根据列数选择处理策略
                if len(filtered_df.columns) == 1:
                    # 1列格式：所有数据混合在一列中
                    transactions = self._process_single_column_format(filtered_df)
                elif len(filtered_df.columns) >= 4:
                    # 多列格式：标准的四列提取
                    transactions = self._process_multi_column_format(filtered_df)
                else:
                    continue

                if not transactions.empty:
                    all_page_transactions.append(transactions)

            # 合并页面内的所有交易
            if all_page_transactions:
                return pd.concat(all_page_transactions, ignore_index=True)
            else:
                return pd.DataFrame()

        except Exception as e:
            print(f"  ❌ 第{page_num}页提取失败: {e}")
            return pd.DataFrame()

    def _filter_page_format_content(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        过滤页面格式内容和非交易记录

        Args:
            df: 原始数据框

        Returns:
            pd.DataFrame: 过滤后的数据框
        """
        if df.empty:
            return df

        filtered_rows = []

        for _, row in df.iterrows():
            # 将行转换为字符串进行检查
            row_str = ' '.join(str(val) for val in row.values).upper()

            # 跳过空行
            if all(pd.isna(val) or str(val).strip() in ['', 'nan'] for val in row.values):
                continue

            # 跳过非交易记录
            if any(keyword in row_str for keyword in self.non_transaction_keywords):
                continue

            # 跳过页面格式内容
            if any(keyword.upper() in row_str for keyword in self.page_format_keywords):
                continue

            # 跳过分隔线
            if ('_' in row_str and row_str.count('_') > 15) or ('—' in row_str and row_str.count('—') > 10):
                continue

            # 保留有效行
            filtered_rows.append(row)

        if filtered_rows:
            return pd.DataFrame(filtered_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()

    def _process_single_column_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理单列格式：所有数据混合在一列中
        格式示例：'02/10/2102/10/21TO TRF.3,847.001,678.46Cr'

        Args:
            df: 单列数据框

        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        transactions = []

        for _, row in df.iterrows():
            first_col = str(row.iloc[0]).strip()

            if not first_col or first_col.lower() == 'nan':
                continue

            # 使用正则表达式解析混合数据
            # 模式：Value Date + Post Date + 描述 + 金额 + 余额Cr
            pattern = r'(\d{2}/\d{2}/\d{2})(\d{2}/\d{2}/\d{2})(.+?)(\d{1,3}(?:,\d{3})*\.?\d{0,2})(\d{1,3}(?:,\d{3})*\.?\d{0,2}Cr)$'

            match = re.match(pattern, first_col)
            if match:
                value_date = match.group(1)
                post_date = match.group(2)
                description = match.group(3).strip()
                amount_str = match.group(4)
                balance_str = match.group(5)

                # 清理描述
                if description.startswith('.'):
                    description = description[1:].strip()

                # 判断是借记还是贷记
                if 'TO TRF' in description.upper():
                    debit = self._parse_amount(amount_str)
                    credit = None
                elif 'BY TRF' in description.upper():
                    debit = None
                    credit = self._parse_amount(amount_str)
                else:
                    debit = self._parse_amount(amount_str)
                    credit = None

                transactions.append({
                    'Post Date': post_date,
                    'Value Date': value_date,
                    'Details': description,
                    'Debit': debit,
                    'Credit': credit,
                    'Balance': balance_str  # 保持原始格式
                })

        if transactions:
            return pd.DataFrame(transactions)
        else:
            return pd.DataFrame()

    def _process_multi_column_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理多列格式：标准的四列提取
        列结构：[混合Post Date+Details, Value Date, Debit, Credit, Balance]

        Args:
            df: 多列数据框

        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        transactions = []
        current_transaction = None

        for _, row in df.iterrows():
            first_col = str(row.iloc[0]).strip()

            if not first_col or first_col.lower() == 'nan':
                continue

            # 检查第一列是否包含日期（新交易的开始）
            if re.search(self.date_pattern, first_col):
                # 保存之前的交易
                if current_transaction is not None:
                    transactions.append(current_transaction)

                # 开始新交易
                parsed_data = self._parse_mixed_post_date_details(first_col)

                if parsed_data:
                    # 提取其他列的数据
                    if len(row) >= 4:
                        # 根据列数确定数据位置
                        if len(row) == 5:
                            # 5列格式：[混合, nan, 借记, 贷记, 余额]
                            debit_val = str(row.iloc[2]).strip()
                            credit_val = str(row.iloc[3]).strip()
                            balance_val = str(row.iloc[4]).strip()
                        else:
                            # 其他列数格式
                            debit_val = str(row.iloc[-3]).strip()
                            credit_val = str(row.iloc[-2]).strip()
                            balance_val = str(row.iloc[-1]).strip()

                        current_transaction = {
                            'Post Date': parsed_data['post_date'],
                            'Value Date': parsed_data['value_date'],
                            'Details': parsed_data['details'],
                            'Debit': self._parse_amount(debit_val) if debit_val and debit_val != 'nan' else None,
                            'Credit': self._parse_amount(credit_val) if credit_val and credit_val != 'nan' else None,
                            'Balance': balance_val if balance_val and balance_val != 'nan' else ''
                        }
            else:
                # 这是描述的延续行
                if current_transaction is not None:
                    # 合并到当前交易的描述中
                    current_details = current_transaction.get('Details', '')
                    additional_details = first_col
                    current_transaction['Details'] = f"{current_details} {additional_details}".strip()

        # 添加最后一个交易
        if current_transaction is not None:
            transactions.append(current_transaction)

        if transactions:
            return pd.DataFrame(transactions)
        else:
            return pd.DataFrame()

    def _parse_mixed_post_date_details(self, mixed_col: str) -> Optional[Dict]:
        """
        解析混合的Post Date和Details列

        Args:
            mixed_col: 混合列内容

        Returns:
            Dict: 包含post_date, value_date, details的字典
        """
        if not mixed_col:
            return None

        # 查找所有日期
        dates = re.findall(self.date_pattern, mixed_col)

        if len(dates) >= 2:
            # 有两个日期：第一个是Value Date，第二个是Post Date
            value_date = dates[0]
            post_date = dates[1]

            # 提取描述（第二个日期后的内容）
            second_date_pos = mixed_col.find(post_date) + len(post_date)
            details = mixed_col[second_date_pos:].strip()

        elif len(dates) == 1:
            # 只有一个日期：同时作为Value Date和Post Date
            value_date = dates[0]
            post_date = dates[0]

            # 提取描述（日期后的内容）
            date_pos = mixed_col.find(dates[0]) + len(dates[0])
            details = mixed_col[date_pos:].strip()

        else:
            # 没有日期，可能是描述延续行
            return None

        return {
            'post_date': post_date,
            'value_date': value_date,
            'details': details
        }

    def _parse_amount(self, amount_str: str) -> Optional[float]:
        """
        解析金额字符串

        Args:
            amount_str: 金额字符串

        Returns:
            float: 解析后的金额，如果解析失败返回None
        """
        if not amount_str or amount_str.lower() in ['nan', 'nat', '']:
            return None

        try:
            # 移除逗号和其他非数字字符（保留小数点和负号）
            cleaned = re.sub(r'[^\d.-]', '', amount_str)
            if cleaned and cleaned != '-':
                amount = float(cleaned)
                # 检查异常值
                if abs(amount) > 1e12:
                    return None
                return amount
        except:
            pass

        return None

    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理和验证数据

        Args:
            df: 原始数据框

        Returns:
            pd.DataFrame: 清理后的数据框
        """
        if df.empty:
            return df

        print(f"\n🧹 数据清理和验证...")
        print(f"  原始数据: {len(df)} 条")

        # 确保有正确的列
        for col in self.expected_columns:
            if col not in df.columns:
                df[col] = None

        # 只保留期望的列，按正确顺序
        df = df[self.expected_columns]

        # 清理日期列
        df['Post Date'] = df['Post Date'].apply(self._clean_date)
        df['Value Date'] = df['Value Date'].apply(self._clean_date)

        # 清理描述列
        df['Details'] = df['Details'].apply(self._clean_details)

        # 清理金额列（保持Balance列的原始格式）
        df['Debit'] = df['Debit'].apply(lambda x: x if pd.notna(x) else None)
        df['Credit'] = df['Credit'].apply(lambda x: x if pd.notna(x) else None)
        df['Balance'] = df['Balance'].apply(self._clean_balance_preserve_format)

        # 移除无效行
        df = df[df['Post Date'].notna() & (df['Post Date'] != '')]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        print(f"  清理后数据: {len(df)} 条")

        return df

    def _clean_date(self, value) -> str:
        """清理日期值"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()
        if date_str.lower() in ['nan', 'nat', '']:
            return ""

        # 如果已经是正确格式，直接返回
        if re.match(self.date_pattern, date_str):
            return date_str

        return ""

    def _clean_details(self, value) -> str:
        """清理描述值"""
        if pd.isna(value):
            return ""

        details_str = str(value).strip()
        if details_str.lower() in ['nan', 'nat']:
            return ""

        # 清理多余的空格和换行符
        details_str = re.sub(r'\s+', ' ', details_str)
        details_str = details_str.replace('\r', ' ').replace('\n', ' ')

        return details_str

    def _clean_balance_preserve_format(self, value) -> str:
        """清理Balance列值，保持原始格式包括Cr/Dr后缀和逗号"""
        if pd.isna(value):
            return ""

        balance_str = str(value).strip()
        if balance_str.lower() in ['nan', 'nat', '']:
            return ""

        # 保持原始格式，只清理多余的空格
        balance_str = re.sub(r'\s+', ' ', balance_str)

        return balance_str

    def save_results(self, df: pd.DataFrame, output_base: str = "cbi_extracted") -> Tuple[str, str, str]:
        """保存解析结果"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 CBI银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 日期范围
        valid_dates = df[df['Post Date'].notna() & (df['Post Date'] != '')]
        if not valid_dates.empty:
            print(f"日期范围: {valid_dates['Post Date'].iloc[0]} 到 {valid_dates['Post Date'].iloc[-1]}")

        # 交易类型统计
        debits_count = df['Debit'].notna().sum()
        credits_count = df['Credit'].notna().sum()

        debits_total = df['Debit'].sum() if debits_count > 0 else 0
        credits_total = df['Credit'].sum() if credits_count > 0 else 0

        print(f"\n💸 借记统计:")
        print(f"  借记交易: {debits_count} 笔")
        print(f"  借记总额: ₹{debits_total:,.2f}")

        print(f"\n💰 贷记统计:")
        print(f"  贷记交易: {credits_count} 笔")
        print(f"  贷记总额: ₹{credits_total:,.2f}")

        print(f"\n📊 净变化: ₹{credits_total - debits_total:,.2f}")

        # 数据完整性
        missing_post_dates = df['Post Date'].isna().sum()
        missing_details = df['Details'].isna().sum()
        missing_balances = (df['Balance'] == '').sum()

        print(f"\n📋 数据完整性:")
        print(f"  缺失过账日期: {missing_post_dates} 条")
        print(f"  缺失交易详情: {missing_details} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # Balance列格式验证
        balance_with_cr = df['Balance'].astype(str).str.contains('Cr').sum()
        balance_with_dr = df['Balance'].astype(str).str.contains('Dr').sum()
        balance_with_comma = df['Balance'].astype(str).str.contains(',').sum()

        print(f"\n💳 Balance列格式:")
        print(f"  包含Cr后缀: {balance_with_cr} 条")
        print(f"  包含Dr后缀: {balance_with_dr} 条")
        print(f"  包含逗号分隔: {balance_with_comma} 条")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            debit = f"₹{row['Debit']:,.2f}" if pd.notna(row['Debit']) else "-"
            credit = f"₹{row['Credit']:,.2f}" if pd.notna(row['Credit']) else "-"
            balance = str(row['Balance']) if row['Balance'] else "-"

            print(f"  {i+1}. {row['Post Date']} | {str(row['Details'])[:40]}...")
            print(f"     借记: {debit} | 贷记: {credit} | 余额: {balance}")


def main():
    """主函数"""
    parser = CBIPDFParser()

    pdf_path = "../files/15-cbi-*********-Bank-statement.pdf"

    print("🚀 启动CBI银行PDF解析器 - 四列标准提取策略")

    # 解析PDF
    df = parser.parse_cbi_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 CBI银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ CBI银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
    
    def _analyze_all_pages(self, pdf_path: str, total_pages: int) -> Dict[int, Dict]:
        """分析所有页面的表格结构"""
        page_analysis = {}
        
        print(f"\n🔍 分析所有页面的表格结构...")
        
        # 处理全部页面（33页）
        for page_num in range(1, total_pages + 1):
            print(f"  📄 分析第{page_num}页...")
            analysis = self._analyze_page_structure(pdf_path, page_num)
            page_analysis[page_num] = analysis
            
            print(f"    表格类型: {analysis['table_type'].value}")
            print(f"    有边界标识: {analysis['has_boundaries']}")
            print(f"    预估交易数: {analysis['estimated_transactions']}")
        
        return page_analysis
    
    def _analyze_page_structure(self, pdf_path: str, page_num: int) -> Dict:
        """分析单页的表格结构"""
        analysis = {
            'table_type': TableType.UNKNOWN,
            'has_boundaries': False,
            'estimated_transactions': 0,
            'extraction_params': {}
        }
        
        try:
            # 首先检查页面文本，寻找边界标识
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                page_text = reader.pages[page_num - 1].extract_text()
                
                has_brought_forward = "BROUGHT FORWARD" in page_text
                has_carried_forward = "CARRIED FORWARD" in page_text
                analysis['has_boundaries'] = has_brought_forward or has_carried_forward
            
            # 尝试提取表格（基于分析结果，只有stream + area模式有效）
            extraction_params = {'stream': True, 'area': [0, 0, 800, 600]}
            
            dfs = tabula.read_pdf(pdf_path, pages=str(page_num), 
                                pandas_options={'header': None, 'dtype': str}, **extraction_params)
            
            if dfs:
                for df in dfs:
                    if df.empty:
                        continue
                    
                    # 分析这个表格
                    table_analysis = self._analyze_table_structure(df)
                    
                    if table_analysis['transaction_count'] > 0:
                        analysis['table_type'] = TableType.TRANSACTION_TABLE
                        analysis['estimated_transactions'] = table_analysis['transaction_count']
                        analysis['extraction_params'] = extraction_params
                        break
            
        except Exception as e:
            print(f"    ❌ 第{page_num}页分析失败: {e}")
        
        return analysis
    
    def _analyze_table_structure(self, df: pd.DataFrame) -> Dict:
        """分析表格结构，确定表格类型"""
        analysis = {
            'table_type': TableType.UNKNOWN,
            'transaction_count': 0
        }

        if df.empty:
            return analysis

        # 统计有效交易行（包含日期的行）
        transaction_count = 0
        for idx, row in df.iterrows():
            if self._is_transaction_row(row):
                transaction_count += 1

        analysis['transaction_count'] = transaction_count

        if transaction_count > 0:
            analysis['table_type'] = TableType.TRANSACTION_TABLE

        return analysis
    
    def _is_transaction_row(self, row) -> bool:
        """检查是否为交易行"""
        # 检查第一列是否包含日期
        first_val = str(row.iloc[0]).strip()
        
        # CBI银行日期格式: DD/MM/YY
        if re.search(self.cbi_date_pattern, first_val):
            return True
        
        return False

    def _extract_page_data(self, pdf_path: str, page_num: int, analysis: Dict) -> pd.DataFrame:
        """基于分析结果提取页面数据"""
        try:
            # 使用分析得出的提取参数
            extraction_params = analysis.get('extraction_params', {'stream': True, 'area': [0, 0, 800, 600]})

            dfs = tabula.read_pdf(pdf_path, pages=str(page_num),
                                pandas_options={'header': None, 'dtype': str}, **extraction_params)

            if not dfs:
                return pd.DataFrame()

            # 处理所有有效的表格
            all_page_data = []

            for i, df in enumerate(dfs):
                if df.empty:
                    continue

                # 分析这个表格
                table_analysis = self._analyze_table_structure(df)

                # 只处理交易表格
                if table_analysis['table_type'] == TableType.TRANSACTION_TABLE and table_analysis['transaction_count'] > 0:
                    print(f"    📄 处理交易表格 {i+1}: {table_analysis['transaction_count']} 条交易")

                    # 处理交易表格
                    processed_data = self._process_transaction_table(df, analysis)

                    if not processed_data.empty:
                        all_page_data.append(processed_data)

            # 合并所有表格的数据
            if all_page_data:
                return pd.concat(all_page_data, ignore_index=True)
            else:
                return pd.DataFrame()

        except Exception as e:
            print(f"    ❌ 第{page_num}页数据提取失败: {e}")
            return pd.DataFrame()

    def _process_transaction_table(self, df: pd.DataFrame, page_analysis: Dict) -> pd.DataFrame:
        """处理交易表格 - 特别处理CBI银行的格式"""
        if df.empty:
            return df

        # 第一步：过滤边界行（BROUGHT FORWARD和CARRIED FORWARD）
        filtered_df = self._filter_boundary_rows(df)

        # 根据列数选择不同的处理策略
        if len(filtered_df.columns) == 1:
            # 1列格式：直接解析，不需要多行合并
            standardized_df = self._standardize_to_6_columns(filtered_df)
        else:
            # 多列格式：使用原有的处理流程
            # 第二步：解析混合列数据
            parsed_df = self._parse_mixed_columns(filtered_df)

            # 第三步：合并多行描述
            merged_df = self._merge_multiline_descriptions(parsed_df)

            # 第四步：标准化为6列
            standardized_df = self._standardize_to_6_columns(merged_df)

        # 第五步：过滤有效交易
        return self._filter_valid_transactions(standardized_df)

    def _filter_boundary_rows(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤掉边界行（BROUGHT FORWARD和CARRIED FORWARD）和其他非交易行"""
        if df.empty:
            return df

        filtered_rows = []

        for _, row in df.iterrows():
            row_str = ' '.join(str(val) for val in row.values).upper()

            # 跳过边界行（包括CLOSING BALANCE）
            if any(keyword in row_str for keyword in self.boundary_keywords):
                continue

            # 跳过页面标题和格式行
            if any(keyword in row_str for keyword in ['PAGE NO', 'STATEMENT', 'SUMMARY', 'VALUEPOSTDETAILS', 'CHQNO', 'DEBITCREDITBALANCE', 'DR. COUNT', 'CR. COUNT', 'TOLL FREE', 'VALUE', 'POST', 'DETAILS']):
                continue

            # 跳过页面说明文字
            if 'IN CASE YOUR ACCOUNT IS OPERATED BY A LETTER OF AUTHORITY' in row_str:
                continue

            # 跳过双横线分隔符
            if any(pattern in row_str for pattern in self.double_line_patterns):
                continue

            # 跳过包含大量下划线或横线的行
            if ('_' in row_str and row_str.count('_') > 15) or ('—' in row_str and row_str.count('—') > 10):
                continue

            # 跳过包含大量下划线的描述行
            first_col = str(row.iloc[0]).strip()
            if ('_' in first_col and first_col.count('_') > 15) or ('—' in first_col and first_col.count('—') > 10):
                continue

            # 跳过空行
            if all(pd.isna(val) or str(val).strip() in ['', 'nan'] for val in row.values):
                continue

            filtered_rows.append(row)

        if filtered_rows:
            return pd.DataFrame(filtered_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()

    def _parse_mixed_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """解析混合列数据 - 分离日期和描述"""
        if df.empty:
            return df

        parsed_rows = []

        for _, row in df.iterrows():
            first_col = str(row.iloc[0]).strip()

            # 尝试分离日期信息
            parsed_row = self._parse_first_column(first_col, row)
            if parsed_row:
                parsed_rows.append(parsed_row)

        if parsed_rows:
            return pd.DataFrame(parsed_rows)
        else:
            return pd.DataFrame()

    def _parse_first_column(self, first_col: str, row) -> Optional[Dict]:
        """解析第一列的混合数据"""
        if not first_col or first_col.lower() == 'nan':
            return None

        # 查找日期模式
        date_matches = list(re.finditer(self.cbi_date_pattern, first_col))

        if len(date_matches) >= 2:
            # 有两个日期：Post Date和Value Date
            post_date = date_matches[0].group()
            value_date = date_matches[1].group()

            # 提取描述（日期后的内容）
            description_start = date_matches[1].end()
            description = first_col[description_start:].strip()

        elif len(date_matches) == 1:
            # 只有一个日期
            post_date = date_matches[0].group()
            value_date = post_date  # 假设相同

            # 提取描述
            description_start = date_matches[0].end()
            description = first_col[description_start:].strip()

        else:
            # 没有日期，可能是描述的延续行
            return {
                'Post Date': '',
                'Value Date': '',
                'Details': first_col,
                'Debit': self._extract_amount(row, -3) if len(row) >= 3 else None,
                'Credit': self._extract_amount(row, -2) if len(row) >= 2 else None,
                'Balance': self._extract_amount(row, -1) if len(row) >= 1 else None,
                'is_continuation': True
            }

        return {
            'Post Date': post_date,
            'Value Date': value_date,
            'Details': description,
            'Debit': self._extract_amount(row, -3) if len(row) >= 3 else None,
            'Credit': self._extract_amount(row, -2) if len(row) >= 2 else None,
            'Balance': self._extract_amount(row, -1) if len(row) >= 1 else None,
            'is_continuation': False
        }

    def _extract_amount(self, row, col_index: int) -> Optional[float]:
        """从指定列提取金额"""
        if abs(col_index) > len(row):
            return None

        value = row.iloc[col_index]
        if pd.isna(value):
            return None

        value_str = str(value).strip()
        if value_str.lower() in ['nan', 'nat', '']:
            return None

        try:
            # 清理金额格式（移除逗号、Cr等）
            cleaned = re.sub(r'[^\d.-]', '', value_str.replace('Cr', ''))
            if cleaned and cleaned != '-':
                return float(cleaned)
        except:
            pass

        return None

    def _merge_multiline_descriptions(self, df: pd.DataFrame) -> pd.DataFrame:
        """合并多行描述"""
        if df.empty:
            return df

        merged_rows = []
        current_transaction = None

        for _, row in df.iterrows():
            if row.get('is_continuation', False):
                # 这是描述的延续行
                if current_transaction is not None:
                    # 合并到当前交易的描述中
                    current_details = current_transaction.get('Details', '')
                    additional_details = row.get('Details', '')
                    current_transaction['Details'] = f"{current_details} {additional_details}".strip()
            else:
                # 这是新的交易行
                if current_transaction is not None:
                    # 保存之前的交易
                    merged_rows.append(current_transaction)

                # 开始新的交易
                current_transaction = {
                    'Post Date': row.get('Post Date', ''),
                    'Value Date': row.get('Value Date', ''),
                    'Details': row.get('Details', ''),
                    'Debit': row.get('Debit'),
                    'Credit': row.get('Credit'),
                    'Balance': row.get('Balance')
                }

        # 添加最后一个交易
        if current_transaction is not None:
            merged_rows.append(current_transaction)

        if merged_rows:
            return pd.DataFrame(merged_rows)
        else:
            return pd.DataFrame()

    def _standardize_to_6_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化为6列格式，基于实际的CBI银行表格结构"""
        if df.empty:
            return df



        # 根据实际列数处理
        if len(df.columns) == 1:
            # 1列格式：所有数据混合在第一列中（CBI银行的实际格式）
            standardized_rows = []

            for _, row in df.iterrows():
                parsed_row = self._parse_1_column_row(row)
                if parsed_row:
                    standardized_rows.append(parsed_row)

            if standardized_rows:
                result_df = pd.DataFrame(standardized_rows)
            else:
                result_df = pd.DataFrame()

        elif len(df.columns) == 4:
            # 4列格式：[混合日期描述, 空列, 金额1, 余额]
            standardized_rows = []

            for _, row in df.iterrows():
                parsed_row = self._parse_4_column_row(row)
                if parsed_row:
                    standardized_rows.append(parsed_row)

            if standardized_rows:
                result_df = pd.DataFrame(standardized_rows)
            else:
                result_df = pd.DataFrame()

        elif len(df.columns) == 5:
            # 5列格式：[混合日期描述, 空列, 借记, 贷记, 余额]
            standardized_rows = []

            for _, row in df.iterrows():
                parsed_row = self._parse_5_column_row(row)
                if parsed_row:
                    standardized_rows.append(parsed_row)

            if standardized_rows:
                result_df = pd.DataFrame(standardized_rows)
            else:
                result_df = pd.DataFrame()

        else:
            # 其他列数，尝试通用处理
            result_df = df.copy()

        # 确保有6列
        for col in self.expected_columns:
            if col not in result_df.columns:
                result_df[col] = None

        # 只保留期望的列，按正确顺序
        result_df = result_df[self.expected_columns]

        return result_df

    def _parse_1_column_row(self, row) -> Optional[Dict]:
        """解析1列格式的行 - CBI银行的实际格式"""
        first_col = str(row.iloc[0]).strip()

        if not first_col or first_col.lower() == 'nan':
            return None

        # 过滤页面说明文字
        if 'In Case Your Account Is Operated By A Letter Of Authority' in first_col:
            return None



        # CBI银行1列格式示例：'02/10/2102/10/21TO TRF.3,847.001,678.46Cr'
        # 格式：日期1日期2描述金额余额Cr

        # 使用正则表达式解析
        import re

        # 改进的匹配模式：处理实际的数据格式
        # 匹配：Value Date + Post Date + 描述 + 金额 + 余额Cr
        pattern = r'(\d{2}/\d{2}/\d{2})(\d{2}/\d{2}/\d{2})(.+?)(\d{1,3}(?:,\d{3})*\.?\d{0,2})(\d{1,3}(?:,\d{3})*\.?\d{0,2}Cr)$'

        match = re.match(pattern, first_col)
        if match:
            value_date = match.group(1)  # 第一个日期是Value Date
            post_date = match.group(2)   # 第二个日期是Post Date
            description = match.group(3).strip()
            amount_str = match.group(4)
            balance_str = match.group(5)

            # 清理描述，移除多余的点号
            if description.startswith('.'):
                description = description[1:].strip()

            # 判断是借记还是贷记
            if 'TO TRF' in description.upper():
                debit = self._clean_amount_for_parsing(amount_str)
                credit = None
            elif 'BY TRF' in description.upper():
                debit = None
                credit = self._clean_amount_for_parsing(amount_str)
            else:
                # 默认处理
                debit = self._clean_amount_for_parsing(amount_str)
                credit = None

            return {
                'Post Date': post_date,
                'Value Date': value_date,
                'Details': description,
                'Debit': debit,
                'Credit': credit,
                'Balance': balance_str  # 保持原始格式，包括Cr后缀
            }

        return None

    def _parse_4_column_row(self, row) -> Optional[Dict]:
        """解析4列格式的行"""
        first_col = str(row.iloc[0]).strip()

        if not first_col or first_col.lower() == 'nan':
            return None

        # 解析第一列的混合数据
        parsed_data = self._parse_first_column(first_col, row)
        if not parsed_data:
            return None

        # 4列格式：[混合, 空, 金额, 余额]
        amount_col = str(row.iloc[2]).strip() if len(row) > 2 else ''
        balance_col = str(row.iloc[3]).strip() if len(row) > 3 else ''

        # 判断金额是借记还是贷记（基于余额变化或关键词）
        if 'TO TRF' in first_col.upper():
            # 转出，是借记
            debit = self._clean_amount_for_parsing(amount_col)
            credit = None
        elif 'BY TRF' in first_col.upper():
            # 转入，是贷记
            debit = None
            credit = self._clean_amount_for_parsing(amount_col)
        else:
            # 默认处理
            debit = self._clean_amount_for_parsing(amount_col)
            credit = None

        return {
            'Post Date': parsed_data.get('Post Date', ''),
            'Value Date': parsed_data.get('Value Date', ''),
            'Details': parsed_data.get('Details', ''),
            'Debit': debit,
            'Credit': credit,
            'Balance': balance_col if balance_col and balance_col.lower() != 'nan' else ''  # 保持原始格式
        }

    def _parse_5_column_row(self, row) -> Optional[Dict]:
        """解析5列格式的行"""
        first_col = str(row.iloc[0]).strip()

        if not first_col or first_col.lower() == 'nan':
            return None

        # 过滤页面说明文字
        if 'In Case Your Account Is Operated By A Letter Of Authority' in first_col:
            return None

        # 解析第一列的混合数据
        parsed_data = self._parse_first_column(first_col, row)
        if not parsed_data:
            return None

        # 5列格式：[混合日期描述, nan, 借记金额, 贷记金额, 余额Cr]
        # 根据分析结果：['10/10/2110/10/21TO TRF.', 'nan', '1,000.00', 'nan', '331.46Cr']
        debit_col = str(row.iloc[2]).strip() if len(row) > 2 else ''
        credit_col = str(row.iloc[3]).strip() if len(row) > 3 else ''
        balance_col = str(row.iloc[4]).strip() if len(row) > 4 else ''

        # 处理金额列
        debit = self._clean_amount_for_parsing(debit_col) if debit_col and debit_col.lower() != 'nan' else None
        credit = self._clean_amount_for_parsing(credit_col) if credit_col and credit_col.lower() != 'nan' else None

        return {
            'Post Date': parsed_data.get('Post Date', ''),
            'Value Date': parsed_data.get('Value Date', ''),
            'Details': parsed_data.get('Details', ''),
            'Debit': debit,
            'Credit': credit,
            'Balance': balance_col if balance_col and balance_col.lower() != 'nan' else ''  # 保持原始格式，包括Cr后缀
        }

    def _clean_amount_for_parsing(self, value) -> Optional[float]:
        """清理金额值用于解析（不影响Balance列）"""
        if not value or value.lower() in ['nan', 'nat', '']:
            return None

        try:
            # 移除逗号和Cr/Dr标识符
            cleaned = re.sub(r'[^\d.-]', '', value.replace('Cr', '').replace('Dr', ''))
            if cleaned and cleaned != '-':
                amount = float(cleaned)
                # 检查异常值
                if abs(amount) > 1e12:
                    return None
                return amount
        except:
            pass

        return None

    def _filter_valid_transactions(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤有效的交易行"""
        if df.empty:
            return df

        valid_rows = []

        for _, row in df.iterrows():
            # 检查日期是否有效
            post_date = str(row['Post Date']).strip()
            if not self._is_valid_date(post_date):
                continue

            # 检查是否有有效的金额或余额
            if not self._has_valid_amount_data(row):
                continue

            valid_rows.append(row)

        if valid_rows:
            return pd.DataFrame(valid_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()

    def _is_valid_date(self, date_str: str) -> bool:
        """检查是否为有效日期"""
        if not date_str or date_str.lower() in ['nan', 'nat', '']:
            return False

        return bool(re.match(self.cbi_date_pattern, date_str))

    def _has_valid_amount_data(self, row) -> bool:
        """检查是否有有效的金额数据"""
        for col in ['Debit', 'Credit', 'Balance']:
            val = row[col]
            if pd.notna(val) and val is not None:
                return True
        return False

    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据"""
        if df.empty:
            return df

        print(f"\n🧹 数据清理和验证...")
        print(f"  原始数据: {len(df)} 条")

        # 清理日期列
        df['Post Date'] = df['Post Date'].apply(self._clean_date)
        df['Value Date'] = df['Value Date'].apply(self._clean_date)

        # 清理金额列（Balance列保持原始格式）
        for col in ['Debit', 'Credit']:
            df[col] = df[col].apply(self._clean_amount)

        # Balance列保持原始字符格式，包括Cr/Dr后缀和逗号
        df['Balance'] = df['Balance'].apply(self._clean_balance_preserve_format)

        # 特殊处理：从原始数据中提取Balance列的Cr/Dr后缀
        df = self._restore_balance_format(df)

        # 清理描述列
        df['Details'] = df['Details'].apply(self._clean_details)

        # 移除无效行
        df = df[df['Post Date'].notna() & (df['Post Date'] != '')]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        print(f"  清理后数据: {len(df)} 条")

        return df

    def _clean_date(self, value) -> str:
        """清理日期值"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()
        if date_str.lower() in ['nan', 'nat', '']:
            return ""

        # 如果已经是正确格式，直接返回
        if re.match(self.cbi_date_pattern, date_str):
            return date_str

        return ""

    def _clean_amount(self, value) -> Optional[float]:
        """清理金额值"""
        if pd.isna(value) or value is None:
            return None

        if isinstance(value, (int, float)):
            # 检查是否为异常大的数值（可能是解析错误）
            if abs(value) > 1e12:  # 超过1万亿的数值可能是错误
                return None
            return float(value)

        amount_str = str(value).strip()
        if amount_str.lower() in ['nan', 'nat', '']:
            return None

        try:
            # 移除逗号、Cr等标识符，保留数字和小数点
            cleaned = re.sub(r'[^\d.-]', '', amount_str.replace('Cr', ''))
            if cleaned and cleaned != '-':
                amount = float(cleaned)
                # 检查是否为异常大的数值
                if abs(amount) > 1e12:  # 超过1万亿的数值可能是错误
                    return None
                return amount
        except:
            pass

        return None

    def _clean_details(self, value) -> str:
        """清理描述值"""
        if pd.isna(value):
            return ""

        details_str = str(value).strip()
        if details_str.lower() in ['nan', 'nat']:
            return ""

        # 清理多余的空格和换行符
        details_str = re.sub(r'\s+', ' ', details_str)
        # 清理换行符
        details_str = details_str.replace('\r', ' ').replace('\n', ' ')

        return details_str

    def _clean_balance_preserve_format(self, value) -> str:
        """清理Balance列值，保持原始格式包括Cr/Dr后缀和逗号"""
        if pd.isna(value):
            return ""

        balance_str = str(value).strip()
        if balance_str.lower() in ['nan', 'nat', '']:
            return ""

        # 保持原始格式，只清理多余的空格
        balance_str = re.sub(r'\s+', ' ', balance_str)

        return balance_str

    def _restore_balance_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """从原始数据中恢复Balance列的Cr/Dr格式"""
        if df.empty:
            return df

        # 这是一个简化的实现
        # 在实际应用中，我们需要从原始PDF数据中提取真正的Balance格式
        # 目前先返回原始数据框
        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "cbi_extracted") -> Tuple[str, str, str]:
        """保存解析结果"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 CBI银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 日期范围
        valid_dates = df[df['Post Date'].notna() & (df['Post Date'] != '')]
        if not valid_dates.empty:
            print(f"日期范围: {valid_dates['Post Date'].iloc[0]} 到 {valid_dates['Post Date'].iloc[-1]}")

        # 交易类型统计
        debits_count = df['Debit'].notna().sum()
        credits_count = df['Credit'].notna().sum()

        debits_total = df['Debit'].sum() if debits_count > 0 else 0
        credits_total = df['Credit'].sum() if credits_count > 0 else 0

        print(f"\n💸 借记统计:")
        print(f"  借记交易: {debits_count} 笔")
        print(f"  借记总额: ₹{debits_total:,.2f}")

        print(f"\n💰 贷记统计:")
        print(f"  贷记交易: {credits_count} 笔")
        print(f"  贷记总额: ₹{credits_total:,.2f}")

        print(f"\n📊 净变化: ₹{credits_total - debits_total:,.2f}")

        # 数据完整性
        missing_post_dates = df['Post Date'].isna().sum()
        missing_details = df['Details'].isna().sum()
        missing_balances = df['Balance'].isna().sum()

        print(f"\n📋 数据完整性:")
        print(f"  缺失过账日期: {missing_post_dates} 条")
        print(f"  缺失交易详情: {missing_details} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额信息（保持原始格式）
        valid_balances = df[df['Balance'].notna() & (df['Balance'] != '')]
        if not valid_balances.empty:
            first_balance = valid_balances['Balance'].iloc[0]
            last_balance = valid_balances['Balance'].iloc[-1]

            print(f"\n💳 余额信息:")
            print(f"  期初余额: {first_balance}")
            print(f"  期末余额: {last_balance}")
            print(f"  余额格式: 保持原始格式（包含Cr/Dr后缀和逗号）")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            debit = f"₹{row['Debit']:,.2f}" if pd.notna(row['Debit']) else "-"
            credit = f"₹{row['Credit']:,.2f}" if pd.notna(row['Credit']) else "-"
            balance = str(row['Balance']) if pd.notna(row['Balance']) and str(row['Balance']) != '' else "-"

            print(f"  {i+1}. {row['Post Date']} | {str(row['Details'])[:40]}...")
            print(f"     借记: {debit} | 贷记: {credit} | 余额: {balance}")


def main():
    """主函数"""
    parser = CBIPDFParser()

    pdf_path = "../files/15-cbi-*********-Bank-statement.pdf"

    print("🚀 启动CBI银行PDF解析器")

    # 解析PDF
    df = parser.parse_cbi_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 CBI银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ CBI银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
