#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
银行PDF账单自动识别系统 - 使用指南和集成示例
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bank_identifier import BankIdentifier, BankType, IdentificationResult


def basic_usage_example():
    """基本使用示例"""
    print("=" * 60)
    print("基本使用示例")
    print("=" * 60)
    
    # 创建银行识别器实例
    identifier = BankIdentifier()
    
    # 识别单个PDF文件
    pdf_path = "files/2-hdfc-*********-HDFC-BANK-STATEMENT-pdf.pdf"
    
    if os.path.exists(pdf_path):
        print(f"正在识别文件: {os.path.basename(pdf_path)}")
        
        # 执行识别
        result = identifier.identify_bank(pdf_path)
        
        # 输出结果
        print(f"识别银行: {result.identified_bank.value}")
        print(f"置信度: {result.confidence_score:.3f}")
        print(f"处理时间: {result.processing_time:.2f}秒")
        print(f"提取方法: {', '.join(result.extraction_methods_used)}")
        
        if result.error_message:
            print(f"错误信息: {result.error_message}")
        
        # 显示所有匹配结果
        if result.all_matches:
            print("\n所有匹配结果:")
            for i, match in enumerate(result.all_matches[:3], 1):
                print(f"  {i}. {match.bank_type.value}: {match.confidence:.3f}")
                print(f"     匹配特征: {', '.join(match.matched_features)}")
    else:
        print(f"文件不存在: {pdf_path}")


def batch_processing_example():
    """批量处理示例"""
    print("\n" + "=" * 60)
    print("批量处理示例")
    print("=" * 60)
    
    # 创建银行识别器实例
    identifier = BankIdentifier()
    
    # 获取所有PDF文件
    files_dir = "files"
    pdf_files = []
    
    if os.path.exists(files_dir):
        for file_path in Path(files_dir).glob("*.pdf"):
            pdf_files.append(str(file_path))
    
    if pdf_files:
        print(f"找到 {len(pdf_files)} 个PDF文件")
        
        # 批量识别（只处理前3个文件作为示例）
        sample_files = pdf_files[:3]
        results = identifier.batch_identify(sample_files)
        
        # 输出结果
        for pdf_path, result in results.items():
            filename = os.path.basename(pdf_path)
            print(f"\n文件: {filename}")
            print(f"  银行: {result.identified_bank.value}")
            print(f"  置信度: {result.confidence_score:.3f}")
            print(f"  处理时间: {result.processing_time:.2f}秒")
    else:
        print("未找到PDF文件")


def integration_with_existing_parser():
    """与现有解析器集成示例"""
    print("\n" + "=" * 60)
    print("与现有解析器集成示例")
    print("=" * 60)
    
    def parse_bank_statement(pdf_path):
        """解析银行账单的完整流程"""
        print(f"开始处理: {os.path.basename(pdf_path)}")
        
        # 步骤1: 识别银行类型
        identifier = BankIdentifier()
        result = identifier.identify_bank(pdf_path)
        
        print(f"识别银行: {result.identified_bank.value}")
        print(f"置信度: {result.confidence_score:.3f}")
        
        # 步骤2: 根据银行类型选择相应的解析器
        if result.confidence_score >= 0.7:  # 高置信度
            bank_type = result.identified_bank
            
            if bank_type == BankType.SBI:
                print("使用SBI解析器...")
                # 这里调用SBI特定的解析逻辑
                # from sbi.sbi_parser import parse_sbi_statement
                # return parse_sbi_statement(pdf_path)
                
            elif bank_type == BankType.HDFC:
                print("使用HDFC解析器...")
                # 这里调用HDFC特定的解析逻辑
                # from hdfc.hdfc_parser import parse_hdfc_statement
                # return parse_hdfc_statement(pdf_path)
                
            elif bank_type == BankType.ICICI:
                print("使用ICICI解析器...")
                # 这里调用ICICI特定的解析逻辑
                # from icici.icici_parser import parse_icici_statement
                # return parse_icici_statement(pdf_path)
                
            else:
                print(f"暂不支持 {bank_type.value} 的自动解析")
                return None
                
        else:
            print("银行识别置信度较低，建议手动确认")
            return None
        
        return {"bank": bank_type.value, "confidence": result.confidence_score}
    
    # 示例使用
    test_file = "files/2-hdfc-*********-HDFC-BANK-STATEMENT-pdf.pdf"
    if os.path.exists(test_file):
        result = parse_bank_statement(test_file)
        if result:
            print(f"解析结果: {result}")


def confidence_based_processing():
    """基于置信度的处理策略示例"""
    print("\n" + "=" * 60)
    print("基于置信度的处理策略示例")
    print("=" * 60)
    
    identifier = BankIdentifier()
    
    def process_with_confidence_strategy(pdf_path):
        """根据置信度采用不同的处理策略"""
        result = identifier.identify_bank(pdf_path)
        
        filename = os.path.basename(pdf_path)
        print(f"\n处理文件: {filename}")
        print(f"识别银行: {result.identified_bank.value}")
        print(f"置信度: {result.confidence_score:.3f}")
        
        if result.confidence_score >= 0.9:
            print("策略: 高置信度 - 自动处理")
            return "auto_process"
            
        elif result.confidence_score >= 0.7:
            print("策略: 中等置信度 - 自动处理但需要验证")
            return "auto_with_verification"
            
        elif result.confidence_score >= 0.5:
            print("策略: 低置信度 - 提供候选选项供用户选择")
            if result.all_matches:
                print("候选银行:")
                for i, match in enumerate(result.all_matches[:3], 1):
                    print(f"  {i}. {match.bank_type.value} (置信度: {match.confidence:.3f})")
            return "manual_selection"
            
        else:
            print("策略: 极低置信度 - 完全手动处理")
            return "manual_process"
    
    # 测试不同置信度的文件
    test_files = [
        "files/2-hdfc-*********-HDFC-BANK-STATEMENT-pdf.pdf",
        "files/11-idbi-*********-idbi-bankin.pdf",
        "files/1-sbi-**********-recent-sbi-statement.pdf"
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            strategy = process_with_confidence_strategy(test_file)
            print(f"采用策略: {strategy}")


def error_handling_example():
    """错误处理示例"""
    print("\n" + "=" * 60)
    print("错误处理示例")
    print("=" * 60)
    
    identifier = BankIdentifier()
    
    def safe_identify_bank(pdf_path):
        """安全的银行识别函数"""
        try:
            # 检查文件是否存在
            if not os.path.exists(pdf_path):
                return {
                    "success": False,
                    "error": "文件不存在",
                    "bank": None,
                    "confidence": 0.0
                }
            
            # 检查文件大小
            file_size = os.path.getsize(pdf_path)
            if file_size == 0:
                return {
                    "success": False,
                    "error": "文件为空",
                    "bank": None,
                    "confidence": 0.0
                }
            
            if file_size > 50 * 1024 * 1024:  # 50MB
                return {
                    "success": False,
                    "error": "文件过大",
                    "bank": None,
                    "confidence": 0.0
                }
            
            # 执行识别
            result = identifier.identify_bank(pdf_path)
            
            if result.error_message:
                return {
                    "success": False,
                    "error": result.error_message,
                    "bank": None,
                    "confidence": 0.0
                }
            
            return {
                "success": True,
                "error": None,
                "bank": result.identified_bank.value,
                "confidence": result.confidence_score,
                "processing_time": result.processing_time
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"处理异常: {str(e)}",
                "bank": None,
                "confidence": 0.0
            }
    
    # 测试正常文件
    test_file = "files/2-hdfc-*********-HDFC-BANK-STATEMENT-pdf.pdf"
    if os.path.exists(test_file):
        result = safe_identify_bank(test_file)
        print(f"正常文件测试: {result}")
    
    # 测试不存在的文件
    result = safe_identify_bank("non_existent_file.pdf")
    print(f"不存在文件测试: {result}")


def performance_monitoring_example():
    """性能监控示例"""
    print("\n" + "=" * 60)
    print("性能监控示例")
    print("=" * 60)
    
    import time
    
    identifier = BankIdentifier()
    
    def monitor_performance(pdf_files):
        """监控批量处理的性能"""
        start_time = time.time()
        
        results = []
        for i, pdf_path in enumerate(pdf_files, 1):
            file_start = time.time()
            
            result = identifier.identify_bank(pdf_path)
            
            file_time = time.time() - file_start
            
            results.append({
                "file": os.path.basename(pdf_path),
                "bank": result.identified_bank.value,
                "confidence": result.confidence_score,
                "processing_time": file_time,
                "success": result.error_message is None
            })
            
            print(f"[{i}/{len(pdf_files)}] {os.path.basename(pdf_path)}: "
                  f"{result.identified_bank.value} ({result.confidence_score:.3f}) "
                  f"- {file_time:.2f}s")
        
        total_time = time.time() - start_time
        
        # 性能统计
        successful_files = [r for r in results if r["success"]]
        avg_time = sum(r["processing_time"] for r in results) / len(results)
        avg_confidence = sum(r["confidence"] for r in successful_files) / len(successful_files) if successful_files else 0
        
        print(f"\n性能统计:")
        print(f"  总文件数: {len(pdf_files)}")
        print(f"  成功处理: {len(successful_files)}")
        print(f"  总时间: {total_time:.2f}秒")
        print(f"  平均处理时间: {avg_time:.2f}秒/文件")
        print(f"  平均置信度: {avg_confidence:.3f}")
        
        return results
    
    # 获取测试文件
    files_dir = "files"
    if os.path.exists(files_dir):
        pdf_files = [str(f) for f in Path(files_dir).glob("*.pdf")][:5]  # 只测试前5个文件
        if pdf_files:
            monitor_performance(pdf_files)


def main():
    """主函数 - 运行所有示例"""
    print("🚀 银行PDF账单自动识别系统 - 使用指南和集成示例")
    print("=" * 80)
    
    # 运行各种示例
    basic_usage_example()
    batch_processing_example()
    integration_with_existing_parser()
    confidence_based_processing()
    error_handling_example()
    performance_monitoring_example()
    
    print("\n" + "=" * 80)
    print("🎉 所有示例运行完成！")
    print("\n集成建议:")
    print("1. 在现有解析器前添加银行识别步骤")
    print("2. 根据置信度采用不同的处理策略")
    print("3. 实现完善的错误处理机制")
    print("4. 监控识别性能和准确率")
    print("5. 定期更新银行特征库以提高准确率")


if __name__ == "__main__":
    main()
