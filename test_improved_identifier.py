#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的银行PDF账单自动识别系统测试脚本
"""

import os
import sys
import time
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bank_identifier import BankIdentifier, BankType


def main():
    """主测试函数"""
    print("🚀 启动改进的银行PDF账单自动识别系统测试")
    print("=" * 80)
    
    # 检查files目录
    files_dir = "files"
    if not os.path.exists(files_dir):
        print(f"❌ 测试目录不存在: {files_dir}")
        return
    
    # 获取所有PDF文件
    pdf_files = []
    for file_path in Path(files_dir).glob("*.pdf"):
        pdf_files.append(str(file_path))
    
    if not pdf_files:
        print(f"❌ 在 {files_dir} 目录中未找到PDF文件")
        return
    
    print(f"📁 找到 {len(pdf_files)} 个PDF文件")
    
    # 创建银行识别器
    identifier = BankIdentifier()
    
    # 执行批量识别
    print("\n🔍 开始批量识别...")
    start_time = time.time()
    
    results = identifier.batch_identify(sorted(pdf_files))
    
    total_time = time.time() - start_time
    
    # 分析准确率（基于文件名中的银行标识）
    print(f"\n🎯 准确率分析（基于文件名）:")
    accuracy_results = analyze_accuracy_by_filename(results)
    
    # 生成改进报告
    print("\n📊 生成改进识别报告...")
    report = generate_improved_report(results, accuracy_results, total_time)
    
    # 显示报告
    print(report)
    
    # 保存报告到文件
    report_file = "improved_bank_identification_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 改进报告已保存到: {report_file}")
    print(f"\n🎉 改进的银行PDF账单自动识别测试完成！")


def analyze_accuracy_by_filename(results):
    """基于文件名分析识别准确率"""
    # 文件名到银行类型的映射
    filename_bank_mapping = {
        'sbi': BankType.SBI,
        'hdfc': BankType.HDFC,
        'icici': BankType.ICICI,
        'kotak': BankType.KOTAK,
        'boi': BankType.BOI,
        'iob': BankType.IOB,
        'pnb': BankType.PNB,
        'canara': BankType.CANARA,
        'ubi': BankType.UBI,
        'bob': BankType.BOB,
        'cbi': BankType.CBI,
        'indian': BankType.INDIAN,
        'uco': BankType.UCO,
        'yes': BankType.YES,
        'federal': BankType.FEDERAL,
        'bandhan': BankType.BANDHAN,
        'idbi': BankType.IDBI,
        'indusind': BankType.INDUSIND,
        'sib': BankType.SIB
    }
    
    correct_predictions = 0
    total_predictions = 0
    detailed_results = []
    confidence_scores = []
    
    for pdf_path, result in results.items():
        filename = os.path.basename(pdf_path).lower()
        
        # 从文件名中推断银行类型
        expected_bank = None
        for bank_key, bank_type in filename_bank_mapping.items():
            if bank_key in filename:
                expected_bank = bank_type
                break
        
        if expected_bank is None:
            continue  # 跳过无法从文件名推断银行的文件
        
        total_predictions += 1
        predicted_bank = result.identified_bank
        
        is_correct = (predicted_bank == expected_bank)
        if is_correct:
            correct_predictions += 1
            confidence_scores.append(result.confidence_score)
        
        detailed_results.append({
            'filename': os.path.basename(pdf_path),
            'expected': expected_bank.value,
            'predicted': predicted_bank.value,
            'confidence': result.confidence_score,
            'correct': is_correct,
            'processing_time': result.processing_time
        })
    
    # 计算统计信息
    accuracy = (correct_predictions / total_predictions * 100) if total_predictions > 0 else 0
    avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
    
    return {
        'total_files': total_predictions,
        'correct_predictions': correct_predictions,
        'accuracy': accuracy,
        'avg_confidence': avg_confidence,
        'detailed_results': detailed_results
    }


def generate_improved_report(results, accuracy_results, total_time):
    """生成改进的识别报告"""
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("改进的银行PDF账单自动识别报告")
    report_lines.append("=" * 80)
    
    # 总体统计
    total_files = len(results)
    successful_identifications = sum(1 for r in results.values() if r.identified_bank != BankType.UNKNOWN)
    success_rate = (successful_identifications / total_files * 100) if total_files > 0 else 0
    
    report_lines.append(f"\n📊 总体统计:")
    report_lines.append(f"  总文件数: {total_files}")
    report_lines.append(f"  成功识别: {successful_identifications}")
    report_lines.append(f"  识别成功率: {success_rate:.1f}%")
    report_lines.append(f"  总处理时间: {total_time:.2f}秒")
    report_lines.append(f"  平均处理时间: {total_time/total_files:.2f}秒/文件")
    
    # 准确率分析
    report_lines.append(f"\n🎯 准确率分析（基于文件名）:")
    report_lines.append(f"  测试文件数: {accuracy_results['total_files']}")
    report_lines.append(f"  正确识别数: {accuracy_results['correct_predictions']}")
    report_lines.append(f"  识别准确率: {accuracy_results['accuracy']:.1f}%")
    report_lines.append(f"  平均置信度: {accuracy_results['avg_confidence']:.3f}")
    
    # 银行分布统计
    bank_counts = {}
    for result in results.values():
        bank = result.identified_bank
        bank_counts[bank] = bank_counts.get(bank, 0) + 1
    
    report_lines.append(f"\n🏦 识别结果分布:")
    for bank, count in sorted(bank_counts.items(), key=lambda x: x[1], reverse=True):
        report_lines.append(f"  {bank.value}: {count} 个文件")
    
    # 高置信度正确案例
    correct_cases = [r for r in accuracy_results['detailed_results'] if r['correct'] and r['confidence'] > 0.7]
    if correct_cases:
        report_lines.append(f"\n✅ 高置信度正确识别案例 (置信度 > 0.7):")
        for result in sorted(correct_cases, key=lambda x: x['confidence'], reverse=True)[:5]:
            report_lines.append(f"  📄 {result['filename']}")
            report_lines.append(f"    银行: {result['predicted']}")
            report_lines.append(f"    置信度: {result['confidence']:.3f}")
            report_lines.append(f"    处理时间: {result['processing_time']:.2f}秒")
    
    # 识别错误案例
    error_cases = [r for r in accuracy_results['detailed_results'] if not r['correct']]
    if error_cases:
        report_lines.append(f"\n❌ 识别错误案例:")
        for result in sorted(error_cases, key=lambda x: x['confidence'], reverse=True):
            report_lines.append(f"  📄 {result['filename']}")
            report_lines.append(f"    期望: {result['expected']}")
            report_lines.append(f"    识别: {result['predicted']}")
            report_lines.append(f"    置信度: {result['confidence']:.3f}")
            report_lines.append(f"    处理时间: {result['processing_time']:.2f}秒")
    
    # 性能分析
    processing_times = [r.processing_time for r in results.values()]
    avg_time = sum(processing_times) / len(processing_times)
    max_time = max(processing_times)
    min_time = min(processing_times)
    
    report_lines.append(f"\n⏱️ 性能分析:")
    report_lines.append(f"  平均处理时间: {avg_time:.2f}秒")
    report_lines.append(f"  最长处理时间: {max_time:.2f}秒")
    report_lines.append(f"  最短处理时间: {min_time:.2f}秒")
    
    # 改进建议
    report_lines.append(f"\n💡 改进建议:")
    if accuracy_results['accuracy'] >= 80:
        report_lines.append(f"  ✅ 识别准确率良好 ({accuracy_results['accuracy']:.1f}%)")
    elif accuracy_results['accuracy'] >= 60:
        report_lines.append(f"  🔶 识别准确率中等，建议优化银行特征库")
    else:
        report_lines.append(f"  ⚠️ 识别准确率较低，需要重新设计特征匹配算法")
    
    if accuracy_results['avg_confidence'] >= 0.8:
        report_lines.append(f"  ✅ 平均置信度高 ({accuracy_results['avg_confidence']:.3f})")
    elif accuracy_results['avg_confidence'] >= 0.6:
        report_lines.append(f"  🔶 平均置信度中等，可以进一步优化")
    else:
        report_lines.append(f"  ⚠️ 平均置信度较低，建议调整权重分配")
    
    if avg_time <= 2.0:
        report_lines.append(f"  ✅ 处理速度良好 (平均 {avg_time:.2f}秒)")
    else:
        report_lines.append(f"  🔶 处理速度可以优化 (平均 {avg_time:.2f}秒)")
    
    return "\n".join(report_lines)


if __name__ == "__main__":
    main()
