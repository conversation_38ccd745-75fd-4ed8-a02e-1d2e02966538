#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UCO银行PDF解析器 - 通用版本
基于表格结构特征的智能识别，支持任意页数的UCO银行账单
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Any
from enum import Enum


class TableType(Enum):
    """表格类型枚举"""
    STANDARD_WITH_HEADER = "standard_with_header"    # 标准表格（有表头）
    STANDARD_WITHOUT_HEADER = "standard_without_header"  # 标准表格（无表头）
    MERGED_FORMAT = "merged_format"  # 合并格式（日期+描述在一列）
    UNKNOWN = "unknown"


class UCOPDFParser:
    """UCO银行PDF解析器 - 通用版本"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Date', 'Particulars', 'Withdrawals', 'Deposits', 'Balance'
        ]
        self.uco_date_pattern = r'\d{2}-[A-Za-z]{3}-\d{4}'
        self.header_keywords = ['DATE', 'PARTICULARS', 'WITHDRAWALS', 'DEPOSITS', 'BALANCE']
        
    def parse_uco_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析UCO银行PDF的主要方法 - 通用策略
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"UCO银行PDF解析器 - 通用架构")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：分析PDF结构，获取页数
            total_pages = self._get_pdf_page_count(pdf_path)
            print(f"\n📄 PDF总页数: {total_pages}")
            
            # 第二步：逐页分析表格结构
            page_analysis = self._analyze_all_pages(pdf_path, total_pages)
            
            # 第三步：基于分析结果提取数据
            all_transactions = []
            
            for page_num, analysis in page_analysis.items():
                print(f"\n🔄 处理第{page_num}页...")
                page_data = self._extract_page_data(pdf_path, page_num, analysis)
                
                if not page_data.empty:
                    all_transactions.append(page_data)
                    print(f"  ✅ 第{page_num}页提取 {len(page_data)} 条交易")
                else:
                    print(f"  ⚠️ 第{page_num}页没有提取到数据")
            
            if not all_transactions:
                print("❌ 所有页面都没有提取到数据")
                return pd.DataFrame()
            
            # 第四步：合并所有页面的数据
            df_final = pd.concat(all_transactions, ignore_index=True)
            
            # 第五步：数据清理和验证
            df_final = self._clean_and_validate_data(df_final)
            
            print(f"\n✅ UCO银行PDF解析完成！总计提取 {len(df_final)} 条交易")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _get_pdf_page_count(self, pdf_path: str) -> int:
        """获取PDF页数"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                return len(reader.pages)
        except Exception as e:
            print(f"❌ 获取PDF页数失败: {e}")
            return 0
    
    def _analyze_all_pages(self, pdf_path: str, total_pages: int) -> Dict[int, Dict]:
        """分析所有页面的表格结构"""
        page_analysis = {}
        
        print(f"\n🔍 分析所有页面的表格结构...")
        
        for page_num in range(1, total_pages + 1):
            print(f"  📄 分析第{page_num}页...")
            analysis = self._analyze_page_structure(pdf_path, page_num)
            page_analysis[page_num] = analysis
            
            print(f"    表格类型: {analysis['table_type'].value}")
            print(f"    表格数量: {analysis['table_count']}")
            print(f"    预估交易数: {analysis['estimated_transactions']}")
        
        return page_analysis
    
    def _analyze_page_structure(self, pdf_path: str, page_num: int) -> Dict:
        """分析单页的表格结构"""
        analysis = {
            'table_type': TableType.UNKNOWN,
            'table_count': 0,
            'estimated_transactions': 0,
            'has_header': False,
            'column_count': 0,
            'extraction_params': {}
        }
        
        try:
            # 尝试多种提取方式
            extraction_methods = [
                {'stream': True},
                {'lattice': True},
                {'stream': True, 'area': [0, 0, 800, 600]},
                {'stream': True, 'area': [50, 0, 800, 600]},
            ]
            
            best_result = None
            max_transactions = 0
            
            for method in extraction_methods:
                try:
                    dfs = tabula.read_pdf(pdf_path, pages=str(page_num), 
                                        pandas_options={'header': None}, **method)
                    
                    if not dfs:
                        continue
                    
                    for df in dfs:
                        if df.empty:
                            continue
                        
                        # 分析这个表格
                        table_analysis = self._analyze_table_structure(df)
                        
                        if table_analysis['transaction_count'] > max_transactions:
                            max_transactions = table_analysis['transaction_count']
                            best_result = table_analysis
                            analysis['extraction_params'] = method
                
                except Exception:
                    continue
            
            if best_result:
                analysis.update(best_result)
                analysis['table_count'] = 1
                analysis['estimated_transactions'] = max_transactions
            
        except Exception as e:
            print(f"    ❌ 第{page_num}页分析失败: {e}")
        
        return analysis
    
    def _analyze_table_structure(self, df: pd.DataFrame) -> Dict:
        """分析表格结构，确定表格类型"""
        analysis = {
            'table_type': TableType.UNKNOWN,
            'has_header': False,
            'column_count': len(df.columns),
            'transaction_count': 0
        }
        
        if df.empty:
            return analysis
        
        # 检查是否有表头
        header_row_idx = self._find_header_row(df)
        analysis['has_header'] = header_row_idx is not None
        
        # 确定数据起始行
        data_start_row = header_row_idx + 1 if header_row_idx is not None else 0
        
        # 统计有效交易行
        transaction_count = 0
        for idx in range(data_start_row, len(df)):
            row = df.iloc[idx]
            if self._is_transaction_row(row):
                transaction_count += 1
        
        analysis['transaction_count'] = transaction_count
        
        # 确定表格类型
        if analysis['has_header']:
            if analysis['column_count'] >= 5:
                analysis['table_type'] = TableType.STANDARD_WITH_HEADER
            else:
                analysis['table_type'] = TableType.MERGED_FORMAT
        else:
            # 检查第一列是否包含合并的日期+描述
            if self._is_merged_format(df):
                analysis['table_type'] = TableType.MERGED_FORMAT
            elif analysis['column_count'] >= 5:
                analysis['table_type'] = TableType.STANDARD_WITHOUT_HEADER
            else:
                analysis['table_type'] = TableType.MERGED_FORMAT
        
        return analysis
    
    def _find_header_row(self, df: pd.DataFrame) -> Optional[int]:
        """查找表头行"""
        for idx, row in df.iterrows():
            row_str = ' '.join(str(val) for val in row.values).upper()
            
            # 检查是否包含所有关键表头词
            keyword_count = sum(1 for keyword in self.header_keywords if keyword in row_str)
            
            if keyword_count >= 3:  # 至少包含3个关键词
                return idx
        
        return None
    
    def _is_transaction_row(self, row) -> bool:
        """检查是否为交易行"""
        # 检查第一列是否包含日期
        first_val = str(row.iloc[0]).strip()
        
        # 直接的日期格式
        if re.match(self.uco_date_pattern, first_val):
            return True
        
        # 合并格式：日期+描述
        if re.search(self.uco_date_pattern, first_val):
            return True
        
        return False
    
    def _is_merged_format(self, df: pd.DataFrame) -> bool:
        """检查是否为合并格式（日期+描述在一列）"""
        if df.empty:
            return False
        
        # 检查前几行的第一列
        for idx in range(min(5, len(df))):
            first_val = str(df.iloc[idx, 0]).strip()
            
            # 如果第一列包含日期但不是纯日期，可能是合并格式
            if re.search(self.uco_date_pattern, first_val):
                # 检查是否还有其他内容
                date_match = re.search(self.uco_date_pattern, first_val)
                if date_match:
                    remaining = first_val[date_match.end():].strip()
                    if remaining:  # 日期后还有内容
                        return True
        
        return False

    def _extract_page_data(self, pdf_path: str, page_num: int, analysis: Dict) -> pd.DataFrame:
        """基于分析结果提取页面数据"""
        try:
            # 使用分析得出的最佳提取参数
            extraction_params = analysis.get('extraction_params', {'stream': True})

            dfs = tabula.read_pdf(pdf_path, pages=str(page_num),
                                pandas_options={'header': None}, **extraction_params)

            if not dfs:
                return pd.DataFrame()

            # 处理所有有效的表格，而不是只选择最佳的
            all_page_data = []

            for i, df in enumerate(dfs):
                if df.empty:
                    continue

                # 分析这个表格
                table_analysis = self._analyze_table_structure(df)

                # 只处理有交易数据的表格
                if table_analysis['transaction_count'] > 0:
                    print(f"    📄 处理表格 {i+1}: {table_analysis['transaction_count']} 条交易")

                    # 根据表格类型处理数据
                    processed_data = self._process_table_by_type(df, table_analysis)

                    if not processed_data.empty:
                        all_page_data.append(processed_data)

            # 合并所有表格的数据
            if all_page_data:
                return pd.concat(all_page_data, ignore_index=True)
            else:
                return pd.DataFrame()

        except Exception as e:
            print(f"    ❌ 第{page_num}页数据提取失败: {e}")
            return pd.DataFrame()

    def _process_table_by_type(self, df: pd.DataFrame, analysis: Dict) -> pd.DataFrame:
        """根据表格类型处理数据"""
        table_type = analysis['table_type']

        if table_type == TableType.STANDARD_WITH_HEADER:
            return self._process_standard_with_header(df, analysis)
        elif table_type == TableType.STANDARD_WITHOUT_HEADER:
            return self._process_standard_without_header(df)
        elif table_type == TableType.MERGED_FORMAT:
            return self._process_merged_format(df)
        else:
            print(f"    ⚠️ 未知表格类型: {table_type}")
            return pd.DataFrame()

    def _process_standard_with_header(self, df: pd.DataFrame, analysis: Dict) -> pd.DataFrame:
        """处理有表头的标准表格"""
        if df.empty:
            return df

        # 找到表头行
        header_row_idx = self._find_header_row(df)
        if header_row_idx is None:
            return pd.DataFrame()

        # 跳过表头，提取数据
        data_df = df.iloc[header_row_idx + 1:].copy()

        # 标准化为5列
        standardized_df = self._standardize_to_5_columns(data_df)

        # 过滤有效交易
        return self._filter_valid_transactions(standardized_df)

    def _process_standard_without_header(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理无表头的标准表格"""
        if df.empty:
            return df

        # 标准化为5列
        standardized_df = self._standardize_to_5_columns(df)

        # 过滤有效交易
        return self._filter_valid_transactions(standardized_df)

    def _process_merged_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理合并格式表格（日期+描述在一列）"""
        if df.empty:
            return df

        processed_rows = []

        for _, row in df.iterrows():
            # 检查是否为交易行
            if not self._is_transaction_row(row):
                continue

            # 处理第一列的合并数据
            first_col = str(row.iloc[0]) if len(row) > 0 else ""

            # 分离日期和描述
            parsed_data = self._parse_merged_column(first_col)
            if not parsed_data:
                continue

            # 构建标准5列数据
            new_row = {
                'Date': parsed_data['date'],
                'Particulars': parsed_data['particulars'],
                'Withdrawals': parsed_data.get('withdrawals'),
                'Deposits': self._extract_amount(row, 2) if len(row) > 2 else None,
                'Balance': self._extract_amount(row, -1) if len(row) > 0 else None  # 最后一列通常是余额
            }

            processed_rows.append(new_row)

        if processed_rows:
            return pd.DataFrame(processed_rows)
        else:
            return pd.DataFrame()

    def _parse_merged_column(self, merged_text: str) -> Optional[Dict]:
        """解析合并列（日期+描述+可能的金额）"""
        if not merged_text or merged_text.strip().lower() in ['nan', '']:
            return None

        # 查找日期
        date_match = re.search(self.uco_date_pattern, merged_text)
        if not date_match:
            return None

        date_part = date_match.group()
        remaining_text = merged_text[date_match.end():].strip()

        # 检查剩余文本末尾是否有金额
        amount_match = re.search(r'^(.+?)\s+(\d+\.?\d*)\s*$', remaining_text)

        if amount_match:
            # 找到了末尾的金额
            particulars_part = amount_match.group(1).strip()
            withdrawal_amount = float(amount_match.group(2))

            return {
                'date': date_part,
                'particulars': particulars_part,
                'withdrawals': withdrawal_amount
            }
        else:
            # 没有末尾金额
            return {
                'date': date_part,
                'particulars': remaining_text,
                'withdrawals': None
            }

    def _extract_amount(self, row, col_index: int) -> Optional[float]:
        """从指定列提取金额"""
        if col_index >= len(row) or col_index < -len(row):
            return None

        value = row.iloc[col_index]
        if pd.isna(value):
            return None

        value_str = str(value).strip()
        if value_str.lower() in ['nan', 'nat', '']:
            return None

        try:
            # 清理并转换为浮点数
            cleaned = re.sub(r'[^\d.-]', '', value_str)
            if cleaned and cleaned != '-':
                return float(cleaned)
        except:
            pass

        return None

    def _standardize_to_5_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化为5列格式"""
        if df.empty:
            return df

        # 确保有5列
        while len(df.columns) < 5:
            df[f'Col_{len(df.columns)}'] = None

        # 如果超过5列，只保留前5列
        if len(df.columns) > 5:
            df = df.iloc[:, :5]

        # 设置标准列名
        df.columns = self.expected_columns

        return df

    def _filter_valid_transactions(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤有效的交易行"""
        if df.empty:
            return df

        valid_rows = []

        for _, row in df.iterrows():
            # 检查日期是否有效
            date_val = str(row['Date']).strip()
            if not self._is_valid_date(date_val):
                continue

            # 检查是否有有效的金额或余额
            if not self._has_valid_amount_data(row):
                continue

            valid_rows.append(row)

        if valid_rows:
            return pd.DataFrame(valid_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()

    def _is_valid_date(self, date_str: str) -> bool:
        """检查是否为有效日期"""
        if not date_str or date_str.lower() in ['nan', 'nat', '']:
            return False

        return bool(re.match(self.uco_date_pattern, date_str))

    def _has_valid_amount_data(self, row) -> bool:
        """检查是否有有效的金额数据"""
        for col in ['Withdrawals', 'Deposits', 'Balance']:
            val = str(row[col]).strip()
            if val and val.lower() not in ['nan', 'nat', '']:
                try:
                    float(val)
                    return True
                except:
                    continue
        return False

    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据"""
        if df.empty:
            return df

        print(f"\n🧹 数据清理和验证...")
        print(f"  原始数据: {len(df)} 条")

        # 清理日期列
        df['Date'] = df['Date'].apply(self._clean_date)

        # 清理金额列
        for col in ['Withdrawals', 'Deposits', 'Balance']:
            df[col] = df[col].apply(self._clean_amount)

        # 清理描述列
        df['Particulars'] = df['Particulars'].apply(self._clean_particulars)

        # 移除无效行
        df = df[df['Date'].notna() & (df['Date'] != '')]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        print(f"  清理后数据: {len(df)} 条")

        return df

    def _clean_date(self, value) -> str:
        """清理日期值"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()
        if date_str.lower() in ['nan', 'nat', '']:
            return ""

        # 如果已经是正确格式，直接返回
        if re.match(self.uco_date_pattern, date_str):
            return date_str

        return ""

    def _clean_amount(self, value) -> Optional[float]:
        """清理金额值"""
        if pd.isna(value):
            return None

        amount_str = str(value).strip()
        if amount_str.lower() in ['nan', 'nat', '']:
            return None

        try:
            # 移除逗号和其他非数字字符（保留小数点和负号）
            cleaned = re.sub(r'[^\d.-]', '', amount_str)
            if cleaned and cleaned != '-':
                return float(cleaned)
        except:
            pass

        return None

    def _clean_particulars(self, value) -> str:
        """清理描述值"""
        if pd.isna(value):
            return ""

        particulars_str = str(value).strip()
        if particulars_str.lower() in ['nan', 'nat']:
            return ""

        # 清理多余的空格和换行符
        particulars_str = re.sub(r'\s+', ' ', particulars_str)

        return particulars_str

    def save_results(self, df: pd.DataFrame, output_base: str = "uco_extracted") -> Tuple[str, str, str]:
        """保存解析结果"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 UCO银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 日期范围
        valid_dates = df[df['Date'].notna() & (df['Date'] != '')]
        if not valid_dates.empty:
            print(f"日期范围: {valid_dates['Date'].iloc[0]} 到 {valid_dates['Date'].iloc[-1]}")

        # 交易类型统计
        withdrawals_count = df['Withdrawals'].notna().sum()
        deposits_count = df['Deposits'].notna().sum()

        withdrawals_total = df['Withdrawals'].sum() if withdrawals_count > 0 else 0
        deposits_total = df['Deposits'].sum() if deposits_count > 0 else 0

        print(f"\n💸 提取统计:")
        print(f"  提取交易: {withdrawals_count} 笔")
        print(f"  提取总额: ₹{withdrawals_total:,.2f}")

        print(f"\n💰 存入统计:")
        print(f"  存入交易: {deposits_count} 笔")
        print(f"  存入总额: ₹{deposits_total:,.2f}")

        print(f"\n📊 净变化: ₹{deposits_total - withdrawals_total:,.2f}")

        # 数据完整性
        missing_dates = df['Date'].isna().sum()
        missing_particulars = df['Particulars'].isna().sum()
        missing_balances = df['Balance'].isna().sum()

        print(f"\n📋 数据完整性:")
        print(f"  缺失日期: {missing_dates} 条")
        print(f"  缺失描述: {missing_particulars} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额信息
        valid_balances = df[df['Balance'].notna()]
        if not valid_balances.empty:
            first_balance = valid_balances['Balance'].iloc[0]
            last_balance = valid_balances['Balance'].iloc[-1]

            print(f"\n💳 余额信息:")
            print(f"  期初余额: ₹{first_balance:,.2f}")
            print(f"  期末余额: ₹{last_balance:,.2f}")
            print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            withdrawals = f"₹{row['Withdrawals']:,.2f}" if pd.notna(row['Withdrawals']) else "-"
            deposits = f"₹{row['Deposits']:,.2f}" if pd.notna(row['Deposits']) else "-"
            balance = f"₹{row['Balance']:,.2f}" if pd.notna(row['Balance']) else "-"

            print(f"  {i+1}. {row['Date']} | {str(row['Particulars'])[:30]}...")
            print(f"     提取: {withdrawals} | 存入: {deposits} | 余额: {balance}")


def main():
    """主函数"""
    parser = UCOPDFParser()

    pdf_path = "../files/17-uco-*********-Account-Statement-UCO-Dec23-May24.pdf"

    print("🚀 启动UCO银行PDF解析器")

    # 解析PDF
    df = parser.parse_uco_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 UCO银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ UCO银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
