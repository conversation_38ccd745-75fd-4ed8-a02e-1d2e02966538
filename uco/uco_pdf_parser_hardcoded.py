#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UCO银行PDF解析器 - 统一解析策略
基于深入的PDF结构分析，处理有表头和无表头的页面
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class UCOPDFParser:
    """UCO银行PDF解析器 - 统一解析策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Date', 'Particulars', 'Withdrawals', 'Deposits', 'Balance'
        ]
        
    def parse_uco_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析UCO银行PDF的主要方法 - 统一策略
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"UCO银行PDF解析器 - 统一解析策略")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 逐页解析，使用统一策略
            all_transactions = []
            
            # 第1页：有表头的页面
            print("\n🔄 解析第1页（有表头）...")
            page1_data = self._extract_page_with_header(pdf_path, 1)
            if not page1_data.empty:
                all_transactions.append(page1_data)
                print(f"  ✅ 第1页提取 {len(page1_data)} 条交易")
            
            # 第2-3页：无表头的标准页面
            for page in [2, 3]:
                print(f"\n🔄 解析第{page}页（无表头）...")
                page_data = self._extract_page_without_header(pdf_path, page)
                if not page_data.empty:
                    all_transactions.append(page_data)
                    print(f"  ✅ 第{page}页提取 {len(page_data)} 条交易")
            
            # 第4页：特殊处理
            print(f"\n🔄 解析第4页（特殊处理）...")
            page4_data = self._extract_page4_special(pdf_path)
            if not page4_data.empty:
                all_transactions.append(page4_data)
                print(f"  ✅ 第4页提取 {len(page4_data)} 条交易")
            
            if not all_transactions:
                print("❌ 所有页面都没有提取到数据")
                return pd.DataFrame()
            
            # 合并所有页面的数据
            df_final = pd.concat(all_transactions, ignore_index=True)
            
            # 数据清理和验证
            df_final = self._clean_and_validate_data(df_final)
            
            print(f"\n✅ UCO银行PDF解析完成！总计提取 {len(df_final)} 条交易")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_page_with_header(self, pdf_path: str, page: int) -> pd.DataFrame:
        """提取有表头的页面（第1页）"""
        try:
            # 使用header=None获取原始数据，然后手动处理表头
            dfs = tabula.read_pdf(pdf_path, pages=str(page), stream=True, pandas_options={'header': None})
            
            if not dfs:
                return pd.DataFrame()
            
            all_data = []
            
            for i, df in enumerate(dfs):
                print(f"    📄 处理第1页表格 {i+1}: 形状 {df.shape}")
                
                if df.empty:
                    continue
                
                # 查找表头行
                header_row_idx = None
                for idx, row in df.iterrows():
                    row_str = ' '.join(str(val) for val in row.values).upper()
                    if 'DATE' in row_str and 'PARTICULARS' in row_str and 'BALANCE' in row_str:
                        header_row_idx = idx
                        break
                
                if header_row_idx is not None:
                    # 跳过表头行，提取数据
                    data_rows = df.iloc[header_row_idx + 1:]
                    print(f"    📋 找到表头在第 {header_row_idx} 行，数据行数: {len(data_rows)}")
                else:
                    # 没有表头，直接处理数据
                    data_rows = df
                    print(f"    📋 未找到表头，直接处理 {len(data_rows)} 行数据")
                
                # 标准化为5列
                standardized_data = self._standardize_to_5_columns(data_rows)
                if not standardized_data.empty:
                    # 过滤有效交易行
                    valid_data = self._filter_valid_transactions(standardized_data)
                    if not valid_data.empty:
                        all_data.append(valid_data)
                        print(f"    ✅ 表格 {i+1} 提取 {len(valid_data)} 条有效交易")
            
            if all_data:
                return pd.concat(all_data, ignore_index=True)
            else:
                return pd.DataFrame()
                
        except Exception as e:
            print(f"    ❌ 第1页提取失败: {e}")
            return pd.DataFrame()
    
    def _extract_page_without_header(self, pdf_path: str, page: int) -> pd.DataFrame:
        """提取无表头的页面（第2-3页）"""
        try:
            # 使用header=None获取原始数据
            dfs = tabula.read_pdf(pdf_path, pages=str(page), stream=True, pandas_options={'header': None})
            
            if not dfs:
                return pd.DataFrame()
            
            all_data = []
            
            for i, df in enumerate(dfs):
                print(f"    📄 处理第{page}页表格 {i+1}: 形状 {df.shape}")
                
                if df.empty:
                    continue
                
                # 标准化为5列
                standardized_data = self._standardize_to_5_columns(df)
                if not standardized_data.empty:
                    # 过滤有效交易行
                    valid_data = self._filter_valid_transactions(standardized_data)
                    if not valid_data.empty:
                        all_data.append(valid_data)
                        print(f"    ✅ 表格 {i+1} 提取 {len(valid_data)} 条有效交易")
            
            if all_data:
                return pd.concat(all_data, ignore_index=True)
            else:
                return pd.DataFrame()
                
        except Exception as e:
            print(f"    ❌ 第{page}页提取失败: {e}")
            return pd.DataFrame()
    
    def _extract_page4_special(self, pdf_path: str) -> pd.DataFrame:
        """特殊处理第4页"""
        try:
            # 尝试多种区域设置
            areas = [
                [0, 0, 800, 600],    # 全页面
                [50, 0, 800, 600],   # 稍微调整
            ]
            
            best_result = pd.DataFrame()
            max_transactions = 0
            
            for i, area in enumerate(areas):
                try:
                    print(f"    🔧 尝试区域 {i+1}: {area}")
                    dfs = tabula.read_pdf(pdf_path, pages='4', area=area, stream=True, pandas_options={'header': None})
                    
                    if not dfs:
                        continue
                    
                    for j, df in enumerate(dfs):
                        if df.empty:
                            continue
                        
                        print(f"    📄 区域 {i+1} 表格 {j+1}: 形状 {df.shape}")
                        
                        # 处理第4页的特殊格式
                        processed_data = self._process_page4_format(df)
                        if not processed_data.empty:
                            valid_data = self._filter_valid_transactions(processed_data)
                            if len(valid_data) > max_transactions:
                                max_transactions = len(valid_data)
                                best_result = valid_data
                                print(f"    ✅ 区域 {i+1} 表格 {j+1} 提取 {len(valid_data)} 条交易（最佳）")
                
                except Exception as e:
                    print(f"    ⚠️ 区域 {i+1} 失败: {e}")
                    continue
            
            return best_result
                
        except Exception as e:
            print(f"    ❌ 第4页特殊处理失败: {e}")
            return pd.DataFrame()
    
    def _process_page4_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理第4页的特殊格式 - 修复列对齐问题"""
        if df.empty:
            return df

        processed_rows = []

        for _, row in df.iterrows():
            # 第4页的第一列包含：日期+描述+提取金额（如果有）
            first_col = str(row.iloc[0]) if len(row) > 0 else ""

            # 尝试分离日期、描述和可能的提取金额
            # 模式：DD-MMM-YYYY 描述 [金额]
            date_match = re.match(r'(\d{2}-[A-Za-z]{3}-\d{4})\s+(.+)', first_col)
            if date_match:
                date_part = date_match.group(1)
                rest_content = date_match.group(2)

                # 检查描述末尾是否有金额（提取金额）
                # 模式：描述 + 空格 + 数字
                amount_match = re.search(r'^(.+?)\s+(\d+\.?\d*)\s*$', rest_content)

                if amount_match:
                    # 找到了末尾的金额，这是提取金额
                    particulars_part = amount_match.group(1).strip()
                    withdrawal_amount = float(amount_match.group(2))

                    new_row = {
                        'Date': date_part,
                        'Particulars': particulars_part,
                        'Withdrawals': withdrawal_amount,
                        'Deposits': row.iloc[2] if len(row) > 2 and pd.notna(row.iloc[2]) and str(row.iloc[2]).strip().lower() != 'nan' else None,
                        'Balance': row.iloc[3] if len(row) > 3 and pd.notna(row.iloc[3]) else None
                    }
                else:
                    # 没有找到末尾金额，可能是存入交易
                    particulars_part = rest_content.strip()

                    new_row = {
                        'Date': date_part,
                        'Particulars': particulars_part,
                        'Withdrawals': None,
                        'Deposits': row.iloc[2] if len(row) > 2 and pd.notna(row.iloc[2]) and str(row.iloc[2]).strip().lower() != 'nan' else None,
                        'Balance': row.iloc[3] if len(row) > 3 and pd.notna(row.iloc[3]) else None
                    }

                processed_rows.append(new_row)

        if processed_rows:
            return pd.DataFrame(processed_rows)
        else:
            return pd.DataFrame()
    
    def _standardize_to_5_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化为5列格式"""
        if df.empty:
            return df
        
        # 确保有5列
        while len(df.columns) < 5:
            df[f'Col_{len(df.columns)}'] = None
        
        # 如果超过5列，只保留前5列
        if len(df.columns) > 5:
            df = df.iloc[:, :5]
        
        # 设置标准列名
        df.columns = self.expected_columns
        
        return df
    
    def _filter_valid_transactions(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤有效的交易行"""
        if df.empty:
            return df
        
        valid_rows = []
        
        for _, row in df.iterrows():
            # 检查日期是否有效
            date_val = str(row['Date']).strip()
            if not self._is_valid_date(date_val):
                continue
            
            # 检查是否有有效的金额或余额
            if not self._has_valid_amount_data(row):
                continue
            
            valid_rows.append(row)
        
        if valid_rows:
            return pd.DataFrame(valid_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()
    
    def _is_valid_date(self, date_str: str) -> bool:
        """检查是否为有效日期"""
        if not date_str or date_str.lower() in ['nan', 'nat', '']:
            return False
        
        # UCO日期格式: DD-MMM-YYYY
        return bool(re.match(r'\d{2}-[A-Za-z]{3}-\d{4}', date_str))
    
    def _has_valid_amount_data(self, row) -> bool:
        """检查是否有有效的金额数据"""
        for col in ['Withdrawals', 'Deposits', 'Balance']:
            val = str(row[col]).strip()
            if val and val.lower() not in ['nan', 'nat', '']:
                try:
                    float(val)
                    return True
                except:
                    continue
        return False

    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据"""
        if df.empty:
            return df

        print(f"\n🧹 数据清理和验证...")
        print(f"  原始数据: {len(df)} 条")

        # 清理日期列
        df['Date'] = df['Date'].apply(self._clean_date)

        # 清理金额列
        for col in ['Withdrawals', 'Deposits', 'Balance']:
            df[col] = df[col].apply(self._clean_amount)

        # 清理描述列
        df['Particulars'] = df['Particulars'].apply(self._clean_particulars)

        # 移除无效行
        df = df[df['Date'].notna() & (df['Date'] != '')]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        print(f"  清理后数据: {len(df)} 条")

        return df

    def _clean_date(self, value) -> str:
        """清理日期值"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()
        if date_str.lower() in ['nan', 'nat', '']:
            return ""

        # 如果已经是正确格式，直接返回
        if re.match(r'\d{2}-[A-Za-z]{3}-\d{4}', date_str):
            return date_str

        return ""

    def _clean_amount(self, value) -> Optional[float]:
        """清理金额值"""
        if pd.isna(value):
            return None

        amount_str = str(value).strip()
        if amount_str.lower() in ['nan', 'nat', '']:
            return None

        try:
            # 移除逗号和其他非数字字符（保留小数点和负号）
            cleaned = re.sub(r'[^\d.-]', '', amount_str)
            if cleaned and cleaned != '-':
                return float(cleaned)
        except:
            pass

        return None

    def _clean_particulars(self, value) -> str:
        """清理描述值"""
        if pd.isna(value):
            return ""

        particulars_str = str(value).strip()
        if particulars_str.lower() in ['nan', 'nat']:
            return ""

        # 清理多余的空格和换行符
        particulars_str = re.sub(r'\s+', ' ', particulars_str)

        return particulars_str

    def save_results(self, df: pd.DataFrame, output_base: str = "uco_extracted") -> Tuple[str, str, str]:
        """保存解析结果"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 UCO银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 日期范围
        valid_dates = df[df['Date'].notna() & (df['Date'] != '')]
        if not valid_dates.empty:
            print(f"日期范围: {valid_dates['Date'].iloc[0]} 到 {valid_dates['Date'].iloc[-1]}")

        # 交易类型统计
        withdrawals_count = df['Withdrawals'].notna().sum()
        deposits_count = df['Deposits'].notna().sum()

        withdrawals_total = df['Withdrawals'].sum() if withdrawals_count > 0 else 0
        deposits_total = df['Deposits'].sum() if deposits_count > 0 else 0

        print(f"\n💸 提取统计:")
        print(f"  提取交易: {withdrawals_count} 笔")
        print(f"  提取总额: ₹{withdrawals_total:,.2f}")

        print(f"\n💰 存入统计:")
        print(f"  存入交易: {deposits_count} 笔")
        print(f"  存入总额: ₹{deposits_total:,.2f}")

        print(f"\n📊 净变化: ₹{deposits_total - withdrawals_total:,.2f}")

        # 数据完整性
        missing_dates = df['Date'].isna().sum()
        missing_particulars = df['Particulars'].isna().sum()
        missing_balances = df['Balance'].isna().sum()

        print(f"\n📋 数据完整性:")
        print(f"  缺失日期: {missing_dates} 条")
        print(f"  缺失描述: {missing_particulars} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额信息
        valid_balances = df[df['Balance'].notna()]
        if not valid_balances.empty:
            first_balance = valid_balances['Balance'].iloc[0]
            last_balance = valid_balances['Balance'].iloc[-1]

            print(f"\n💳 余额信息:")
            print(f"  期初余额: ₹{first_balance:,.2f}")
            print(f"  期末余额: ₹{last_balance:,.2f}")
            print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            withdrawals = f"₹{row['Withdrawals']:,.2f}" if pd.notna(row['Withdrawals']) else "-"
            deposits = f"₹{row['Deposits']:,.2f}" if pd.notna(row['Deposits']) else "-"
            balance = f"₹{row['Balance']:,.2f}" if pd.notna(row['Balance']) else "-"

            print(f"  {i+1}. {row['Date']} | {str(row['Particulars'])[:30]}...")
            print(f"     提取: {withdrawals} | 存入: {deposits} | 余额: {balance}")


def main():
    """主函数"""
    parser = UCOPDFParser()

    pdf_path = "../files/17-uco-*********-Account-Statement-UCO-Dec23-May24.pdf"

    print("🚀 启动UCO银行PDF解析器")

    # 解析PDF
    df = parser.parse_uco_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 UCO银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ UCO银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
