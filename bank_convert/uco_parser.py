#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UCO (United Commercial Bank) 银行对账单PDF解析器 - 重构版本
原始来源: uco/uco_pdf_parser.py
重构为独立的解析器，包含所有必要功能，无外部依赖

解析策略：通用架构，动态页面检测，格式自适应，规则驱动解析
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class UCOPDFParser:
    """UCO银行PDF解析器 - 通用版本"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Date', 'Particulars', 'Withdrawals', 'Deposits', 'Balance'
        ]
        self.uco_date_pattern = r'\d{2}-[A-Za-z]{3}-\d{4}'
        self.header_keywords = ['DATE', 'PARTICULARS', 'WITHDRAWALS', 'DEPOSITS', 'BALANCE']
        
    def parse_uco_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析UCO银行PDF的主要方法 - 通用策略
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"UCO银行PDF解析器 - 通用架构")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：分析PDF结构，获取页数
            total_pages = self._get_pdf_page_count(pdf_path)
            print(f"\n📄 PDF总页数: {total_pages}")
            
            # 第二步：逐页分析表格结构
            page_analysis = self._analyze_all_pages(pdf_path, total_pages)
            
            # 第三步：基于分析结果提取数据
            all_transactions = []
            
            for page_num, analysis in page_analysis.items():
                print(f"\n🔄 处理第{page_num}页...")
                page_data = self._extract_page_data(pdf_path, page_num, analysis)
                
                if not page_data.empty:
                    all_transactions.append(page_data)
                    print(f"  ✅ 第{page_num}页提取 {len(page_data)} 条交易")
                else:
                    print(f"  ⚠️ 第{page_num}页没有提取到数据")
            
            if not all_transactions:
                print("❌ 所有页面都没有提取到数据")
                return pd.DataFrame()
            
            # 第四步：合并所有页面的数据
            df_final = pd.concat(all_transactions, ignore_index=True)
            
            # 第五步：数据清理和验证
            df_final = self._clean_and_validate_data(df_final)
            
            print(f"\n✅ UCO银行PDF解析完成！总计提取 {len(df_final)} 条交易")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _get_pdf_page_count(self, pdf_path: str) -> int:
        """获取PDF页数"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                return len(reader.pages)
        except Exception as e:
            print(f"❌ 获取PDF页数失败: {e}")
            return 0
    
    def _analyze_all_pages(self, pdf_path: str, total_pages: int) -> Dict[int, Dict]:
        """分析所有页面的表格结构"""
        page_analysis = {}
        
        for page_num in range(1, total_pages + 1):
            print(f"  🔍 分析第{page_num}页结构...")
            analysis = self._analyze_page_structure(pdf_path, page_num)
            page_analysis[page_num] = analysis
            
            if analysis['has_transactions']:
                print(f"    ✅ 第{page_num}页：发现 {analysis['transaction_count']} 条交易")
            else:
                print(f"    ⚠️ 第{page_num}页：未发现交易数据")
        
        return page_analysis
    
    def _analyze_page_structure(self, pdf_path: str, page_num: int) -> Dict:
        """分析单页的表格结构"""
        analysis = {
            'page_num': page_num,
            'has_transactions': False,
            'transaction_count': 0,
            'table_structure': None,
            'extraction_params': None,
            'has_header': False
        }
        
        try:
            # 尝试多种提取方式
            extraction_methods = [
                {'stream': True},
                {'lattice': True},
                {'stream': True, 'area': [0, 0, 800, 600]},
                {'stream': True, 'area': [50, 0, 800, 600]},
            ]
            
            best_result = None
            max_transactions = 0
            
            for method in extraction_methods:
                try:
                    dfs = tabula.read_pdf(pdf_path, pages=str(page_num), 
                                        pandas_options={'header': None}, **method)
                    
                    if not dfs:
                        continue
                    
                    for df in dfs:
                        if df.empty:
                            continue
                        
                        # 分析这个表格
                        table_analysis = self._analyze_table_structure(df)
                        
                        if table_analysis['transaction_count'] > max_transactions:
                            max_transactions = table_analysis['transaction_count']
                            best_result = table_analysis
                            analysis['extraction_params'] = method
                
                except Exception:
                    continue
            
            if best_result:
                analysis.update(best_result)
                analysis['has_transactions'] = max_transactions > 0
                analysis['transaction_count'] = max_transactions
        
        except Exception as e:
            print(f"    ❌ 分析第{page_num}页失败: {e}")
        
        return analysis
    
    def _analyze_table_structure(self, df: pd.DataFrame) -> Dict:
        """分析表格结构"""
        analysis = {
            'transaction_count': 0,
            'has_header': False,
            'column_count': len(df.columns),
            'row_count': len(df),
            'table_structure': 'unknown'
        }
        
        if df.empty:
            return analysis
        
        # 检查是否有表头
        first_row = df.iloc[0] if len(df) > 0 else None
        if first_row is not None:
            first_row_str = ' '.join([str(val) for val in first_row if pd.notna(val)]).upper()
            header_count = sum(1 for keyword in self.header_keywords if keyword in first_row_str)
            analysis['has_header'] = header_count >= 3
        
        # 统计可能的交易行数
        transaction_count = 0
        for idx, row in df.iterrows():
            if self._is_potential_transaction_row(row):
                transaction_count += 1
        
        analysis['transaction_count'] = transaction_count
        
        # 判断表格结构类型
        if analysis['column_count'] == 5 and analysis['has_header']:
            analysis['table_structure'] = 'standard_5_column'
        elif analysis['column_count'] == 5:
            analysis['table_structure'] = 'headerless_5_column'
        else:
            analysis['table_structure'] = f'{analysis["column_count"]}_column'
        
        return analysis
    
    def _is_potential_transaction_row(self, row) -> bool:
        """检查是否是潜在的交易行"""
        # 检查第一列是否包含日期模式
        first_col = str(row.iloc[0]).strip() if len(row) > 0 else ""
        if re.search(self.uco_date_pattern, first_col):
            return True
        
        # 检查是否有金额信息
        for i in range(len(row)):
            cell_value = str(row.iloc[i]).strip()
            if re.search(r'\d{1,3}(?:,\d{3})*\.\d{2}', cell_value):
                return True
        
        return False

    def _extract_page_data(self, pdf_path: str, page_num: int, analysis: Dict) -> pd.DataFrame:
        """基于分析结果提取页面数据"""
        if not analysis['has_transactions']:
            return pd.DataFrame()

        try:
            # 使用最佳提取参数
            extraction_params = analysis.get('extraction_params', {'stream': True})

            dfs = tabula.read_pdf(pdf_path, pages=str(page_num),
                                pandas_options={'header': None}, **extraction_params)

            if not dfs:
                return pd.DataFrame()

            # 找到最佳表格
            best_df = None
            max_transactions = 0

            for df in dfs:
                if df.empty:
                    continue

                # 清理和处理表格
                processed_df = self._process_table_data(df, analysis)

                if not processed_df.empty and len(processed_df) > max_transactions:
                    max_transactions = len(processed_df)
                    best_df = processed_df

            return best_df if best_df is not None else pd.DataFrame()

        except Exception as e:
            print(f"    ❌ 提取第{page_num}页数据失败: {e}")
            return pd.DataFrame()

    def _process_table_data(self, df: pd.DataFrame, analysis: Dict) -> pd.DataFrame:
        """处理表格数据"""
        if df.empty:
            return df

        # 确保列数正确
        if len(df.columns) != 5:
            if len(df.columns) < 5:
                # 补充缺失的列
                for i in range(len(df.columns), 5):
                    df[f'col_{i}'] = ''
            else:
                # 截取前5列
                df = df.iloc[:, :5]

        # 设置标准列名
        df.columns = self.expected_columns

        # 移除表头行（如果存在）
        if analysis.get('has_header', False):
            df = df.iloc[1:].reset_index(drop=True)

        # 过滤有效的交易行
        valid_rows = []
        for idx, row in df.iterrows():
            if self._is_valid_uco_transaction(row):
                valid_rows.append(row)

        if valid_rows:
            return pd.DataFrame(valid_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()

    def _is_valid_uco_transaction(self, row) -> bool:
        """检查是否是有效的UCO交易行"""
        # 检查日期列
        date_str = str(row['Date']).strip()
        if not date_str or date_str.lower() == 'nan':
            return False

        # 必须包含UCO日期格式
        if not re.search(self.uco_date_pattern, date_str):
            return False

        # 检查是否有描述
        particulars = str(row['Particulars']).strip()
        if not particulars or particulars.lower() == 'nan' or len(particulars) < 3:
            return False

        # 检查是否至少有一个金额列有值
        amount_columns = ['Withdrawals', 'Deposits', 'Balance']
        has_amount = False
        for col in amount_columns:
            amount_str = str(row[col]).strip()
            if amount_str and amount_str.lower() != 'nan':
                if re.search(r'\d+', amount_str):
                    has_amount = True
                    break

        return has_amount

    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据"""
        if df.empty:
            return df

        print("  🧹 清理和验证数据...")

        # 清理日期列
        df = self._clean_date_column(df)

        # 清理金额列
        df = self._clean_amount_columns(df)

        # 清理描述列
        df = self._clean_particulars_column(df)

        # 验证数据完整性
        df = self._validate_data_integrity(df)

        return df

    def _clean_date_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理日期列"""
        if 'Date' in df.columns:
            print("    📅 清理日期列...")
            df['Date'] = df['Date'].apply(self._parse_uco_date)

        return df

    def _parse_uco_date(self, value) -> str:
        """解析UCO日期格式"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()

        # UCO日期格式: DD-MMM-YYYY (如 01-Jan-2022)
        if re.match(self.uco_date_pattern, date_str):
            return date_str

        # 尝试其他格式转换
        try:
            # 尝试解析各种日期格式
            for fmt in ['%d-%b-%Y', '%d/%m/%Y', '%d-%m-%Y']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%d-%b-%Y')  # 统一为UCO格式
                except ValueError:
                    continue
        except:
            pass

        return date_str  # 如果无法解析，返回原值

    def _clean_amount_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理金额列"""
        amount_columns = ['Withdrawals', 'Deposits', 'Balance']

        for col in amount_columns:
            if col in df.columns:
                print(f"    💰 清理 {col} 列...")
                df[col] = df[col].apply(self._parse_amount)

        return df

    def _parse_amount(self, value) -> float:
        """解析金额字符串为浮点数"""
        if pd.isna(value):
            return 0.0

        # 转换为字符串并清理
        amount_str = str(value).strip()

        if not amount_str or amount_str.lower() in ['nan', 'none', '']:
            return 0.0

        try:
            # 移除逗号和其他非数字字符（保留小数点和负号）
            cleaned = re.sub(r'[^\d.-]', '', amount_str)

            if cleaned:
                return float(cleaned)
            else:
                return 0.0
        except (ValueError, TypeError):
            return 0.0

    def _clean_particulars_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理描述列"""
        if 'Particulars' in df.columns:
            print("    📝 清理描述列...")
            df['Particulars'] = df['Particulars'].apply(self._clean_description_text)

        return df

    def _clean_description_text(self, text) -> str:
        """清理描述文本"""
        if pd.isna(text):
            return ""

        text_str = str(text).strip()

        # 移除换行符和多余空格
        text_str = re.sub(r'\s+', ' ', text_str)

        return text_str

    def _validate_data_integrity(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据完整性"""
        print("    ✅ 验证数据完整性...")

        # 移除没有日期的行
        before_count = len(df)
        df = df[df['Date'].notna() & (df['Date'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效日期的记录")

        # 确保至少有一个金额列有值
        df = df[
            (df['Withdrawals'] > 0) |
            (df['Deposits'] > 0) |
            (df['Balance'] > 0)
        ]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "uco_extracted") -> Tuple[str, str, str]:
        """
        保存解析结果为多种格式

        Args:
            df: 解析后的DataFrame
            output_base: 输出文件名前缀

        Returns:
            Tuple[str, str, str]: CSV、JSON、Excel文件路径
        """
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 确保输出目录存在
        output_dir = "bank_convert"
        os.makedirs(output_dir, exist_ok=True)

        # 在保存前进行最终的文本清理
        df_clean = df.copy()
        if 'Particulars' in df_clean.columns:
            df_clean['Particulars'] = df_clean['Particulars'].apply(
                lambda x: self._clean_description_text(x) if pd.notna(x) else ""
            )

        # 生成文件路径
        csv_file = os.path.join(output_dir, f"{output_base}.csv")
        json_file = os.path.join(output_dir, f"{output_base}.json")
        excel_file = os.path.join(output_dir, f"{output_base}.xlsx")

        try:
            # 保存为CSV
            df_clean.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ CSV文件已保存: {csv_file}")

            # 保存为JSON
            df_clean.to_json(json_file, orient='records', indent=2, force_ascii=False)
            print(f"✅ JSON文件已保存: {json_file}")

            # 保存为Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df_clean.to_excel(writer, sheet_name='UCO_Transactions', index=False)
            print(f"✅ Excel文件已保存: {excel_file}")

            return csv_file, json_file, excel_file

        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            return "", "", ""

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成数据验证报告"""
        if df.empty:
            print("❌ 没有数据可生成报告")
            return

        print(f"\n{'='*60}")
        print(f"UCO银行PDF解析验证报告")
        print(f"{'='*60}")

        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总交易数: {len(df)}")

        # 日期范围
        if 'Date' in df.columns:
            valid_dates = df[df['Date'].notna() & (df['Date'] != '')]
            if not valid_dates.empty:
                print(f"  日期范围: {valid_dates['Date'].min()} 到 {valid_dates['Date'].max()}")

        # 金额统计
        self._generate_amount_statistics(df)

        # 数据质量检查
        self._generate_quality_report(df)

    def _generate_amount_statistics(self, df: pd.DataFrame) -> None:
        """生成金额统计报告"""
        print(f"\n💰 金额统计:")

        # 取款统计
        if 'Withdrawals' in df.columns:
            withdrawals = df[df['Withdrawals'] > 0]['Withdrawals']
            if not withdrawals.empty:
                print(f"  取款交易: {len(withdrawals)} 笔")
                print(f"  取款总额: ₹{withdrawals.sum():,.2f}")
                print(f"  平均取款: ₹{withdrawals.mean():,.2f}")

        # 存款统计
        if 'Deposits' in df.columns:
            deposits = df[df['Deposits'] > 0]['Deposits']
            if not deposits.empty:
                print(f"  存款交易: {len(deposits)} 笔")
                print(f"  存款总额: ₹{deposits.sum():,.2f}")
                print(f"  平均存款: ₹{deposits.mean():,.2f}")

        # 余额统计
        if 'Balance' in df.columns:
            self._generate_balance_statistics(df)

    def _generate_balance_statistics(self, df: pd.DataFrame) -> None:
        """生成余额统计"""
        balance_col = 'Balance'
        valid_balances = df[df[balance_col] > 0]

        if valid_balances.empty:
            print(f"  ⚠️ 没有有效的余额数据")
            return

        print(f"\n📈 余额统计:")
        print(f"  最高余额: ₹{valid_balances[balance_col].max():,.2f}")
        print(f"  最低余额: ₹{valid_balances[balance_col].min():,.2f}")

        # 检查首末余额
        first_balance = valid_balances.iloc[0][balance_col]
        last_balance = valid_balances.iloc[-1][balance_col]

        print(f"  期初余额: ₹{first_balance:,.2f}")
        print(f"  期末余额: ₹{last_balance:,.2f}")
        print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")

    def _generate_quality_report(self, df: pd.DataFrame) -> None:
        """生成数据质量报告"""
        print(f"\n🔍 数据质量检查:")

        # 检查必填字段
        required_fields = ['Date', 'Particulars']
        for field in required_fields:
            if field in df.columns:
                missing_count = df[field].isna().sum() + (df[field] == '').sum()
                if missing_count > 0:
                    print(f"  ⚠️ {field} 缺失: {missing_count} 行")
                else:
                    print(f"  ✅ {field} 完整")

        # 检查描述质量
        if 'Particulars' in df.columns:
            short_descriptions = (df['Particulars'].str.len() < 5).sum()
            if short_descriptions > 0:
                print(f"  ⚠️ 描述过短: {short_descriptions} 行")
            else:
                print(f"  ✅ 描述质量良好")


def main():
    """主函数 - 使用示例"""
    parser = UCOPDFParser()

    # 示例PDF文件路径（需要根据实际情况修改）
    pdf_path = "files/17-uco-*********-Account-Statement-UCO-Dec23-May24.pdf"

    print("🚀 启动UCO银行PDF解析器")

    # 解析PDF
    df = parser.parse_uco_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        csv_file, json_file, excel_file = parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 UCO银行PDF解析完成！")
        print(f"📁 输出文件:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")
        return df
    else:
        print(f"\n❌ UCO银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
