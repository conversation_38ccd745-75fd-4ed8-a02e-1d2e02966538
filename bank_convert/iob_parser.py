#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IOB (Indian Overseas Bank) 银行对账单PDF解析器 - 重构版本
原始来源: iob/iob_pdf_parser.py
重构为独立的解析器，包含所有必要功能，无外部依赖

解析策略：基于BOI的混合解析策略，结合tabula和pypdf解决NARATION列跨行问题
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class IOBPDFParser:
    """IOB银行PDF解析器 - 混合解析策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'DATE', 'CHQ NO', 'NARATION', 'COD', 'DEBIT', 'CREDIT', 'BALANCE'
        ]
        
    def parse_iob_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析IOB银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"IOB银行PDF混合解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：使用Tabula提取表格结构和金额数据
            print("\n🔄 第一步：使用Tabula提取表格结构...")
            df_structured = self._extract_tabula_structure(pdf_path)
            
            if df_structured.empty:
                print("❌ Tabula提取失败")
                return pd.DataFrame()
            
            # 第二步：使用PyPDF提取完整描述文本
            print("\n🔄 第二步：使用PyPDF提取完整描述文本...")
            pypdf_data = self._extract_pypdf_descriptions(pdf_path)
            
            if not pypdf_data:
                print("❌ PyPDF提取失败")
                return df_structured  # 返回tabula结果作为备选
            
            # 第三步：合并数据，修复NARATION列
            print("\n🔄 第三步：合并数据，修复NARATION列...")
            df_final = self._merge_and_fix_data(df_structured, pypdf_data)
            
            # 第四步：数据验证和清理
            print("\n🔄 第四步：数据验证和清理...")
            df_final = self._validate_and_clean_data(df_final)
            
            print(f"\n✅ IOB银行PDF解析完成！提取交易数: {len(df_final)} 条")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_tabula_structure(self, pdf_path: str) -> pd.DataFrame:
        """使用Tabula提取表格结构和金额数据"""
        try:
            # 使用lattice=True模式提取表格
            dfs = tabula.read_pdf(pdf_path, pages='all', lattice=True)
            print(f"  📋 Tabula找到 {len(dfs)} 个表格")

            if not dfs:
                print("  ❌ 未找到任何表格")
                return pd.DataFrame()

            # 合并所有页面的表格
            all_transactions = []

            for i, df in enumerate(dfs):
                print(f"  📄 处理表格 {i+1}: 形状 {df.shape}")

                # 清理表格数据
                df_cleaned = self._clean_tabula_table(df, i+1)

                if not df_cleaned.empty:
                    all_transactions.append(df_cleaned)
                    print(f"    ✅ 表格 {i+1} 清理后: {len(df_cleaned)} 行")
                else:
                    print(f"    ⚠️ 表格 {i+1} 清理后为空")

            if not all_transactions:
                print("  ❌ 所有表格清理后都为空")
                return pd.DataFrame()

            # 合并所有表格
            df_combined = pd.concat(all_transactions, ignore_index=True)
            print(f"  ✅ 合并后总计: {len(df_combined)} 行")

            return df_combined

        except Exception as e:
            print(f"  ❌ Tabula提取失败: {e}")
            return pd.DataFrame()
    
    def _clean_tabula_table(self, df: pd.DataFrame, table_num: int) -> pd.DataFrame:
        """清理Tabula提取的表格数据"""
        if df.empty:
            return df

        print(f"    🧹 清理表格 {table_num}...")

        # 移除完全空白的行
        df = df.dropna(how='all')

        if df.empty:
            return df

        # 检查列数，确保是7列
        if len(df.columns) != 7:
            print(f"    ⚠️ 表格列数不匹配: {len(df.columns)} (期望7列)")
            
            # 尝试调整列数
            if len(df.columns) < 7:
                # 补充缺失的列
                for i in range(len(df.columns), 7):
                    df[f'col_{i}'] = ''
            elif len(df.columns) > 7:
                # 截取前7列
                df = df.iloc[:, :7]

        # 设置标准列名
        df.columns = self.expected_columns

        # 过滤掉表头行和无效行
        df = self._filter_valid_transactions(df)

        return df
    
    def _filter_valid_transactions(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤有效的交易行"""
        if df.empty:
            return df

        valid_rows = []

        for idx, row in df.iterrows():
            # 检查是否是表头行
            if self._is_header_row(row):
                continue

            # 检查是否是有效的交易行
            if self._is_valid_transaction_row(row):
                valid_rows.append(row)

        if valid_rows:
            return pd.DataFrame(valid_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()
    
    def _is_header_row(self, row) -> bool:
        """检查是否是表头行"""
        row_str = ' '.join([str(val) for val in row if pd.notna(val)]).upper()
        
        header_keywords = ['DATE', 'CHQ NO', 'NARATION', 'COD', 'DEBIT', 'CREDIT', 'BALANCE']
        
        # 如果包含多个表头关键词，认为是表头行
        keyword_count = sum(1 for keyword in header_keywords if keyword in row_str)
        return keyword_count >= 3
    
    def _is_valid_transaction_row(self, row) -> bool:
        """检查是否是有效的交易行"""
        # 检查DATE列是否包含日期格式
        date_str = str(row.iloc[0]).strip()
        if date_str and date_str.lower() != 'nan':
            # 检查是否包含日期模式 (IOB格式: DD/MM/YYYY)
            if re.search(r'\d{1,2}/\d{1,2}/\d{2,4}', date_str):
                return True
        
        # 检查是否有金额信息
        for i in [4, 5, 6]:  # DEBIT, CREDIT, BALANCE列
            if i < len(row):
                amount_str = str(row.iloc[i]).strip()
                if amount_str and amount_str.lower() != 'nan':
                    if re.search(r'\d+', amount_str):
                        return True
        
        return False

    def _extract_pypdf_descriptions(self, pdf_path: str) -> List[Dict]:
        """使用PyPDF提取完整的描述文本"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                print(f"  📖 PyPDF读取 {len(reader.pages)} 页")

                all_descriptions = []

                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    text = page.extract_text()

                    print(f"  📄 处理第 {page_num + 1} 页...")
                    page_descriptions = self._parse_page_text(text, page_num + 1)

                    if page_descriptions:
                        all_descriptions.extend(page_descriptions)
                        print(f"    ✅ 提取到 {len(page_descriptions)} 条描述")
                    else:
                        print(f"    ⚠️ 第 {page_num + 1} 页没有找到有效描述")

                print(f"  ✅ PyPDF总计提取 {len(all_descriptions)} 条完整描述")
                return all_descriptions

        except Exception as e:
            print(f"  ❌ PyPDF提取失败: {e}")
            return []

    def _parse_page_text(self, text: str, page_num: int) -> List[Dict]:
        """解析页面文本，提取交易描述"""
        descriptions = []
        lines = text.split('\n')

        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if not line:
                i += 1
                continue

            # 查找交易行模式：日期 + 描述
            match = self._match_transaction_line(line)
            if match:
                # 检查下一行是否为当前交易的延续
                complete_description = match['description']
                j = i + 1

                # 查找可能的延续行
                while j < len(lines):
                    next_line = lines[j].strip()
                    if not next_line:
                        j += 1
                        continue

                    # 如果下一行是新的交易行，停止
                    if self._is_transaction_start_line(next_line):
                        break

                    # 如果下一行是表头或其他非交易内容，停止
                    if self._is_non_transaction_line(next_line):
                        break

                    # 如果下一行看起来是当前交易的延续，添加到描述中
                    if self._is_description_continuation(next_line, complete_description):
                        complete_description += " " + next_line
                        j += 1
                    else:
                        break

                # 更新描述并清理格式
                match['description'] = self._clean_description_text(complete_description)
                descriptions.append(match)
                i = j  # 跳过已处理的延续行
            else:
                i += 1

        return descriptions

    def _match_transaction_line(self, line: str) -> Optional[Dict]:
        """匹配交易行，提取日期和描述"""
        # IOB交易行模式：日期 支票号 描述 [金额信息]
        # 例如：01/02/2022 CHQ123 NEFT-SBIN0000123-JOHN DOE-SALARY 5000.00 10000.00

        # 匹配模式：日期(DD/MM/YYYY) + 可选支票号 + 描述
        pattern = r'^(\d{1,2}/\d{1,2}/\d{4})\s+(.+)$'
        match = re.match(pattern, line)

        if match:
            txn_date = match.group(1)
            rest_content = match.group(2)

            # 从描述部分移除金额信息，保留纯描述
            clean_description = self._extract_description_from_line(rest_content)

            return {
                'txn_date': txn_date,
                'description': clean_description
            }

        return None

    def _extract_description_from_line(self, content: str) -> str:
        """从行内容中提取纯描述，移除金额信息"""
        if not content:
            return ""

        # 查找所有可能的金额模式
        amount_patterns = [
            r'\d{1,3}(?:,\d{3})*\.\d{2}',  # 标准金额格式：1,234.56
            r'\d+\.\d{2}',                 # 简单金额格式：1234.56
            r'\d{1,3}(?:,\d{3})*',         # 整数金额：1,234
        ]

        # 找到所有金额匹配
        valid_amounts = []
        for pattern in amount_patterns:
            matches = list(re.finditer(pattern, content))
            valid_amounts.extend(matches)

        # 按位置排序
        valid_amounts.sort(key=lambda x: x.start())

        # 移除金额信息（通常在行尾）
        if valid_amounts:
            # 只移除确认的金额
            if len(valid_amounts) >= 2:
                # 移除最后两个有效金额
                last_amount_start = valid_amounts[-2].start()
                content = content[:last_amount_start]
            elif len(valid_amounts) == 1:
                # 只有一个有效金额，移除它
                last_amount_start = valid_amounts[-1].start()
                content = content[:last_amount_start]

        # 清理换行符和多余的空格
        content = self._clean_description_text(content)

        # 如果描述太短，可能解析有误
        if len(content) < 5:
            return ""

        return content

    def _is_transaction_start_line(self, line: str) -> bool:
        """检查是否是新交易行的开始"""
        # 检查是否以日期开始
        pattern = r'^\d{1,2}/\d{1,2}/\d{4}'
        return bool(re.match(pattern, line))

    def _is_non_transaction_line(self, line: str) -> bool:
        """检查是否是非交易内容行"""
        non_transaction_keywords = [
            'DATE', 'CHQ NO', 'NARATION', 'COD', 'DEBIT', 'CREDIT', 'BALANCE',
            'INDIAN OVERSEAS BANK', 'ACCOUNT STATEMENT', 'PAGE', 'BROUGHT FORWARD', 'CARRIED FORWARD'
        ]

        line_upper = line.upper()
        return any(keyword in line_upper for keyword in non_transaction_keywords)

    def _is_description_continuation(self, line: str, current_description: str) -> bool:
        """检查是否是描述的延续行"""
        # 如果行包含金额模式，可能不是描述延续
        if re.search(r'\d{1,3}(?:,\d{3})*\.\d{2}', line):
            return False

        # 如果行以日期开头，可能是新的交易
        if re.match(r'^\d{1,2}/\d{1,2}/\d{4}', line):
            return False

        # 如果行看起来是描述的自然延续
        if len(line) > 5 and not line.isupper():
            return True

        return False

    def _clean_description_text(self, text: str) -> str:
        """清理描述文本，处理换行符和格式问题"""
        if not text:
            return ""

        # 移除换行符，用空格替换
        text = re.sub(r'\n+', ' ', text)

        # 移除回车符
        text = re.sub(r'\r+', ' ', text)

        # 移除制表符
        text = re.sub(r'\t+', ' ', text)

        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text)

        # 移除首尾空格
        text = text.strip()

        return text

    def _build_description_lookup(self, pypdf_data: List[Dict]) -> Dict[str, str]:
        """构建描述查找字典，键为txn_date"""
        lookup = {}

        for item in pypdf_data:
            key = item['txn_date']
            lookup[key] = item['description']

        return lookup

    def _merge_and_fix_data(self, df_structured: pd.DataFrame, pypdf_data: List[Dict]) -> pd.DataFrame:
        """合并tabula和pypdf数据，修复NARATION列"""
        if df_structured.empty or not pypdf_data:
            print("  ⚠️ 无法合并：缺少tabula或pypdf数据")
            return df_structured

        print(f"  🔗 合并数据：tabula {len(df_structured)} 行，pypdf {len(pypdf_data)} 条描述")

        # 构建描述查找字典
        description_lookup = self._build_description_lookup(pypdf_data)

        # 统计匹配情况
        matched_count = 0
        improved_count = 0

        # 遍历tabula数据，替换NARATION
        for idx, row in df_structured.iterrows():
            try:
                # 获取日期作为匹配键
                txn_date = str(row['DATE']).strip()

                if not txn_date or txn_date.lower() == 'nan':
                    continue

                # 查找匹配的完整描述
                if txn_date in description_lookup:
                    old_description = str(row['NARATION']) if pd.notna(row['NARATION']) else ""
                    new_description = description_lookup[txn_date]

                    # 只有当新描述更完整时才替换
                    if len(new_description) > len(old_description):
                        # 清理描述文本格式
                        cleaned_description = self._clean_description_text(new_description)
                        df_structured.at[idx, 'NARATION'] = cleaned_description
                        improved_count += 1

                    matched_count += 1

            except Exception as e:
                print(f"    ⚠️ 处理第 {idx} 行时出错: {e}")
                continue

        print(f"  ✅ 匹配成功: {matched_count}/{len(df_structured)} 行")
        print(f"  📈 描述改进: {improved_count} 行")

        return df_structured

    def _validate_and_clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清理最终数据"""
        if df.empty:
            return df

        print("  🧹 清洗数据...")

        # 清洗金额列
        df = self._clean_amount_columns(df)

        # 清洗日期列
        df = self._clean_date_column(df)

        # 清洗CHQ NO列
        df = self._clean_chq_no_column(df)

        # 验证数据完整性
        df = self._validate_data_integrity(df)

        return df

    def _clean_amount_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗金额列数据"""
        amount_columns = ['DEBIT', 'CREDIT', 'BALANCE']

        for col in amount_columns:
            if col in df.columns:
                print(f"    💰 清洗 {col} 列...")
                df[col] = df[col].apply(self._parse_amount)

        return df

    def _parse_amount(self, value) -> float:
        """解析金额字符串为浮点数"""
        if pd.isna(value):
            return 0.0

        # 转换为字符串并清理
        amount_str = str(value).strip()

        if not amount_str or amount_str.lower() in ['nan', 'none', '']:
            return 0.0

        try:
            # 移除逗号和其他非数字字符（保留小数点和负号）
            cleaned = re.sub(r'[^\d.-]', '', amount_str)

            if cleaned:
                return float(cleaned)
            else:
                return 0.0
        except (ValueError, TypeError):
            return 0.0

    def _clean_date_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗日期列"""
        if 'DATE' in df.columns:
            print("    📅 清洗日期列...")
            df['DATE'] = df['DATE'].apply(self._parse_date)

        return df

    def _parse_date(self, value) -> str:
        """解析日期字符串"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()

        # IOB日期格式: DD/MM/YYYY
        if re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_str):
            return date_str

        # 尝试其他常见格式
        try:
            # 尝试解析各种日期格式
            for fmt in ['%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%d/%m/%Y')  # 统一为IOB格式
                except ValueError:
                    continue
        except:
            pass

        return date_str  # 如果无法解析，返回原值

    def _clean_chq_no_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理CHQ NO列的跨列污染"""
        if 'CHQ NO' in df.columns and 'NARATION' in df.columns:
            print("    🧽 清理CHQ NO列跨列污染...")

            # 使用改进的清洗策略
            for idx, row in df.iterrows():
                chq_value = row['CHQ NO']
                naration_value = row['NARATION']

                cleaned_chq = self._clean_chq_no_improved(chq_value, naration_value)
                df.at[idx, 'CHQ NO'] = cleaned_chq

        return df

    def _clean_chq_no_improved(self, chq_value, naration_value) -> str:
        """改进的CHQ NO清洗方法"""
        if pd.isna(chq_value):
            return ""

        chq_str = str(chq_value).strip()

        # 如果CHQ NO看起来像描述文本，清空它
        if len(chq_str) > 20:  # 支票号通常不会很长
            return ""

        # 如果包含明显的描述关键词，清空它
        description_keywords = [
            'NEFT', 'RTGS', 'UPI', 'IMPS', 'TRANSFER', 'PAYMENT', 'SALARY',
            'REFUND', 'CREDIT', 'DEBIT', 'WITHDRAWAL', 'DEPOSIT'
        ]

        chq_upper = chq_str.upper()
        if any(keyword in chq_upper for keyword in description_keywords):
            return ""

        # 如果看起来是有效的支票号（数字或字母数字组合），保留
        if re.match(r'^[A-Z0-9]{1,15}$', chq_upper):
            return chq_str

        return ""

    def _validate_data_integrity(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据完整性"""
        print("    ✅ 验证数据完整性...")

        # 移除没有日期的行
        before_count = len(df)
        df = df[df['DATE'].notna() & (df['DATE'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效日期的记录")

        # 确保至少有一个金额列有值
        df = df[
            df['DEBIT'].notna() |
            df['CREDIT'].notna() |
            df['BALANCE'].notna()
        ]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "iob_extracted") -> Tuple[str, str, str]:
        """
        保存解析结果为多种格式

        Args:
            df: 解析后的DataFrame
            output_base: 输出文件名前缀

        Returns:
            Tuple[str, str, str]: CSV、JSON、Excel文件路径
        """
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 确保输出目录存在
        output_dir = "bank_convert"
        os.makedirs(output_dir, exist_ok=True)

        # 在保存前进行最终的文本清理
        df_clean = df.copy()
        if 'NARATION' in df_clean.columns:
            df_clean['NARATION'] = df_clean['NARATION'].apply(
                lambda x: self._clean_description_text(str(x)) if pd.notna(x) else ""
            )

        # 生成文件路径
        csv_file = os.path.join(output_dir, f"{output_base}.csv")
        json_file = os.path.join(output_dir, f"{output_base}.json")
        excel_file = os.path.join(output_dir, f"{output_base}.xlsx")

        try:
            # 保存为CSV
            df_clean.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ CSV文件已保存: {csv_file}")

            # 保存为JSON
            df_clean.to_json(json_file, orient='records', indent=2, force_ascii=False)
            print(f"✅ JSON文件已保存: {json_file}")

            # 保存为Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df_clean.to_excel(writer, sheet_name='IOB_Transactions', index=False)
            print(f"✅ Excel文件已保存: {excel_file}")

            return csv_file, json_file, excel_file

        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            return "", "", ""

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成数据验证报告"""
        if df.empty:
            print("❌ 没有数据可生成报告")
            return

        print(f"\n{'='*60}")
        print(f"IOB银行PDF解析验证报告")
        print(f"{'='*60}")

        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总交易数: {len(df)}")

        # 日期范围
        if 'DATE' in df.columns:
            valid_dates = df[df['DATE'].notna() & (df['DATE'] != '')]
            if not valid_dates.empty:
                print(f"  日期范围: {valid_dates['DATE'].min()} 到 {valid_dates['DATE'].max()}")

        # 金额统计
        self._generate_amount_statistics(df)

        # 数据质量检查
        self._generate_quality_report(df)

    def _generate_amount_statistics(self, df: pd.DataFrame) -> None:
        """生成金额统计报告"""
        print(f"\n💰 金额统计:")

        # 借记统计
        if 'DEBIT' in df.columns:
            debits = df[df['DEBIT'] > 0]['DEBIT']
            if not debits.empty:
                print(f"  借记交易: {len(debits)} 笔")
                print(f"  借记总额: ₹{debits.sum():,.2f}")
                print(f"  平均借记: ₹{debits.mean():,.2f}")

        # 贷记统计
        if 'CREDIT' in df.columns:
            credits = df[df['CREDIT'] > 0]['CREDIT']
            if not credits.empty:
                print(f"  贷记交易: {len(credits)} 笔")
                print(f"  贷记总额: ₹{credits.sum():,.2f}")
                print(f"  平均贷记: ₹{credits.mean():,.2f}")

        # 余额统计
        if 'BALANCE' in df.columns:
            self._generate_balance_statistics(df)

    def _generate_balance_statistics(self, df: pd.DataFrame) -> None:
        """生成余额统计"""
        balance_col = 'BALANCE'
        valid_balances = df[df[balance_col].notna() & (df[balance_col] != 0)]

        if valid_balances.empty:
            print(f"  ⚠️ 没有有效的余额数据")
            return

        print(f"\n📈 余额统计:")
        print(f"  最高余额: ₹{valid_balances[balance_col].max():,.2f}")
        print(f"  最低余额: ₹{valid_balances[balance_col].min():,.2f}")

        # 余额变化分析
        balance_changes = 0
        for i in range(1, len(valid_balances)):
            if valid_balances.iloc[i][balance_col] != valid_balances.iloc[i-1][balance_col]:
                balance_changes += 1

        print(f"  余额变化次数: {balance_changes}")

        # 检查首末余额
        first_balance = valid_balances.iloc[0][balance_col]
        last_balance = valid_balances.iloc[-1][balance_col]

        print(f"  期初余额: ₹{first_balance:,.2f}")
        print(f"  期末余额: ₹{last_balance:,.2f}")
        print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")

    def _generate_quality_report(self, df: pd.DataFrame) -> None:
        """生成数据质量报告"""
        print(f"\n🔍 数据质量检查:")

        # 检查必填字段
        required_fields = ['DATE', 'NARATION']
        for field in required_fields:
            if field in df.columns:
                missing_count = df[field].isna().sum() + (df[field] == '').sum()
                if missing_count > 0:
                    print(f"  ⚠️ {field} 缺失: {missing_count} 行")
                else:
                    print(f"  ✅ {field} 完整")

        # 检查描述质量
        if 'NARATION' in df.columns:
            short_descriptions = (df['NARATION'].str.len() < 5).sum()
            if short_descriptions > 0:
                print(f"  ⚠️ 描述过短: {short_descriptions} 行")
            else:
                print(f"  ✅ 描述质量良好")


def main():
    """主函数 - 使用示例"""
    parser = IOBPDFParser()

    # 示例PDF文件路径（需要根据实际情况修改）
    pdf_path = "files/14-iob-573689117-Statement-125601000023857-2.pdf"

    print("🚀 启动IOB银行PDF混合解析器")

    # 解析PDF
    df = parser.parse_iob_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        csv_file, json_file, excel_file = parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 IOB银行PDF解析完成！")
        print(f"📁 输出文件:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")
        return df
    else:
        print(f"\n❌ IOB银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
