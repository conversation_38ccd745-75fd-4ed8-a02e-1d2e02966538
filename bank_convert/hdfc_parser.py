#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HDFC (Housing Development Finance Corporation) 银行对账单PDF解析器 - 重构版本
原始来源: hdfc/hdfc_pdf_parser.py
重构为独立的解析器，包含所有必要功能，无外部依赖

解析策略：文本解析策略，从PDF中提取交易数据并进行智能解析
"""

import os
import pandas as pd
import re
import json
import pdfplumber
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class HDFCPDFParser:
    """HDFC银行PDF解析器 - 文本解析策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Date', 'Narration', 'Chq./Ref.No.', 'ValueDt', 
            'WithdrawalAmt.', 'DepositAmt.', 'ClosingBalance'
        ]
        
    def parse_hdfc_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析HDFC银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"HDFC银行PDF解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 提取交易数据
            transactions = self._extract_transaction_data_from_pdf(pdf_path)
            
            if not transactions:
                print("未能从PDF中提取到任何交易数据")
                return pd.DataFrame()
            
            # 标准化数据
            normalized_transactions = self._normalize_transaction_data(transactions)
            
            if not normalized_transactions:
                print("数据标准化后没有有效记录")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(normalized_transactions)
            
            # 数据清理和验证
            df = self._clean_and_validate_data(df)
            
            print(f"\n✅ HDFC银行PDF解析完成！提取交易数: {len(df)} 条")
            return df
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_transaction_data_from_pdf(self, pdf_path: str) -> List[Dict]:
        """从HDFC银行PDF中提取交易数据"""
        transactions = []
        
        print(f"正在解析HDFC银行PDF文件: {pdf_path}")
        
        with pdfplumber.open(pdf_path) as pdf:
            print(f"PDF总页数: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages, 1):
                print(f"正在处理第 {page_num} 页...")
                
                # 提取表格数据
                tables = page.extract_tables()
                
                if tables:
                    for table_num, table in enumerate(tables, 1):
                        print(f"  处理表格 {table_num}...")
                        table_transactions = self._process_table(table, page_num, table_num)
                        transactions.extend(table_transactions)
                
                # 提取文本数据（主要方法）
                text = page.extract_text()
                if text:
                    text_transactions = self._parse_text_transactions(text)
                    transactions.extend(text_transactions)
        
        print(f"总计提取到 {len(transactions)} 条交易记录")
        return transactions
    
    def _process_table(self, table: List[List], page_num: int, table_num: int) -> List[Dict]:
        """处理表格数据"""
        transactions = []
        
        if not table or len(table) < 2:
            return transactions
        
        # 查找表头行
        header_row = None
        for i, row in enumerate(table):
            if row and any('Date' in str(cell) for cell in row if cell):
                header_row = i
                break
        
        if header_row is None:
            return transactions
        
        headers = table[header_row]
        
        # 处理数据行
        for row_idx in range(header_row + 1, len(table)):
            row = table[row_idx]
            if not row or not any(cell for cell in row):
                continue
            
            transaction = self._parse_table_row(headers, row)
            if transaction:
                transactions.append(transaction)
        
        return transactions
    
    def _parse_table_row(self, headers: List, row: List) -> Optional[Dict]:
        """解析表格行数据"""
        if not headers or not row:
            return None
        
        # 创建基础交易记录
        transaction = {}
        
        # 映射列数据
        for i, header in enumerate(headers):
            if i < len(row) and header:
                clean_header = self._clean_text(str(header))
                cell_value = self._clean_text(str(row[i])) if row[i] else ""
                
                # 标准化列名
                if 'date' in clean_header.lower():
                    transaction['Date'] = self._parse_date(cell_value)
                elif 'narration' in clean_header.lower() or 'description' in clean_header.lower():
                    transaction['Narration'] = cell_value
                elif 'ref' in clean_header.lower() or 'chq' in clean_header.lower():
                    transaction['Chq./Ref.No.'] = cell_value
                elif 'withdrawal' in clean_header.lower():
                    transaction['WithdrawalAmt.'] = self._parse_amount(cell_value)
                elif 'deposit' in clean_header.lower():
                    transaction['DepositAmt.'] = self._parse_amount(cell_value)
                elif 'balance' in clean_header.lower():
                    transaction['ClosingBalance'] = self._parse_amount(cell_value)
        
        # 验证交易记录
        if transaction.get('Date') and (transaction.get('WithdrawalAmt.') or transaction.get('DepositAmt.')):
            # 设置默认值
            transaction.setdefault('Narration', '')
            transaction.setdefault('Chq./Ref.No.', '')
            transaction.setdefault('ValueDt', transaction.get('Date', ''))
            transaction.setdefault('WithdrawalAmt.', '')
            transaction.setdefault('DepositAmt.', '')
            transaction.setdefault('ClosingBalance', '')
            
            return transaction
        
        return None
    
    def _parse_text_transactions(self, text: str) -> List[Dict]:
        """从文本中解析交易数据"""
        transactions = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 查找日期模式开始的行
            date_match = re.match(r'^(\d{2}/\d{2}/\d{2})\s+(.+)', line)
            if date_match:
                date = date_match.group(1)
                rest_of_line = date_match.group(2).strip()
                
                # 从行尾提取数字（余额、存款、取款）
                amounts = re.findall(r'[\d,]+\.\d{2}', rest_of_line)
                
                if amounts:
                    # 最后一个金额总是余额
                    balance = amounts[-1]
                    
                    # 移除金额后的文本部分作为描述
                    text_part = rest_of_line
                    for amount in amounts:
                        text_part = text_part.replace(amount, '').strip()
                    
                    # 清理多余的空格
                    text_part = re.sub(r'\s+', ' ', text_part).strip()
                    
                    # 确定取款和存款
                    withdrawal = ""
                    deposit = ""
                    
                    if len(amounts) >= 2:
                        # 倒数第二个可能是取款或存款
                        second_last_amount = amounts[-2]
                        # 简单判断：如果描述包含特定关键词
                        if any(keyword in text_part.upper() for keyword in ['CREDIT', 'DEPOSIT', 'SALARY']):
                            deposit = second_last_amount
                        else:
                            withdrawal = second_last_amount
                    
                    # 提取参考号
                    ref_no = ""
                    ref_match = re.search(r'(\d{10,})', text_part)
                    if ref_match:
                        ref_no = ref_match.group(1)
                        text_part = text_part.replace(ref_no, '').strip()
                    
                    # 查找ValueDt（通常与Date相同，或在描述中）
                    value_dt_match = re.search(r'(\d{2}/\d{2}/\d{2})', rest_of_line)
                    value_dt = value_dt_match.group(1) if value_dt_match else date
                    
                    transaction = {
                        'Date': self._parse_date(date),
                        'Narration': text_part,
                        'Chq./Ref.No.': ref_no,
                        'ValueDt': self._parse_date(value_dt),
                        'WithdrawalAmt.': withdrawal,
                        'DepositAmt.': deposit,
                        'ClosingBalance': balance
                    }
                    
                    transactions.append(transaction)
        
        # 如果解析失败，尝试更简单的行解析
        if not transactions:
            print("文本解析失败，尝试简单行解析...")
            transactions = self._parse_lines_simple(lines)
        
        return transactions
    
    def _parse_lines_simple(self, lines: List[str]) -> List[Dict]:
        """简单的行解析方法"""
        transactions = []
        
        # 查找包含日期的行
        date_pattern = r'\d{2}/\d{2}/\d{2}'
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否包含日期
            dates = re.findall(date_pattern, line)
            if dates:
                # 尝试分割行数据
                parts = line.split()
                if len(parts) >= 7:  # 至少需要7个部分
                    # 这是一个简化的解析，可能需要根据实际PDF格式调整
                    transaction = {
                        'Date': self._parse_date(dates[0]) if dates else '',
                        'Narration': ' '.join(parts[1:-5]) if len(parts) > 6 else '',
                        'Chq./Ref.No.': parts[-5] if len(parts) > 5 else '',
                        'ValueDt': self._parse_date(dates[1]) if len(dates) > 1 else self._parse_date(dates[0]) if dates else '',
                        'WithdrawalAmt.': parts[-3] if len(parts) > 3 and ',' in parts[-3] else '',
                        'DepositAmt.': parts[-2] if len(parts) > 2 and ',' in parts[-2] else '',
                        'ClosingBalance': parts[-1] if len(parts) > 1 and ',' in parts[-1] else ''
                    }
                    
                    if transaction['Date']:
                        transactions.append(transaction)
        
        return transactions

    def _clean_text(self, text: str) -> str:
        """清理文本，去除多余的空格和特殊字符"""
        if not text:
            return ""
        # 移除特殊字符如(cid:9)
        text = re.sub(r'\(cid:\d+\)', ' ', text)
        # 替换多个空格为单个空格
        text = re.sub(r'\s+', ' ', text.strip())
        return text

    def _parse_amount(self, amount_str: str) -> str:
        """解析金额字符串"""
        if not amount_str or amount_str.strip() == '':
            return ""

        # 移除逗号和其他非数字字符（除了小数点）
        amount_str = re.sub(r'[^\d.-]', '', amount_str.strip())

        try:
            if amount_str:
                float(amount_str)  # 验证是否为有效数字
                return amount_str
        except ValueError:
            pass

        return ""

    def _parse_date(self, date_str: str) -> str:
        """解析日期字符串，转换为统一格式"""
        if not date_str:
            return ""

        date_str = self._clean_text(date_str).strip()

        # HDFC日期格式通常是 "DD/MM/YY"
        try:
            # 尝试解析 "DD/MM/YY" 格式
            if re.match(r'\d{2}/\d{2}/\d{2}', date_str):
                date_obj = datetime.strptime(date_str, '%d/%m/%y')
                return date_obj.strftime('%d/%m/%Y')
        except:
            pass

        return date_str

    def _normalize_transaction_data(self, transactions: List[Dict]) -> List[Dict]:
        """标准化交易数据"""
        normalized = []

        for transaction in transactions:
            if not transaction.get('Date'):
                continue

            # 确保所有必需字段存在
            normalized_transaction = {
                'Date': transaction.get('Date', ''),
                'Narration': transaction.get('Narration', ''),
                'Chq./Ref.No.': transaction.get('Chq./Ref.No.', ''),
                'ValueDt': transaction.get('ValueDt', transaction.get('Date', '')),
                'WithdrawalAmt.': transaction.get('WithdrawalAmt.', ''),
                'DepositAmt.': transaction.get('DepositAmt.', ''),
                'ClosingBalance': transaction.get('ClosingBalance', '')
            }

            normalized.append(normalized_transaction)

        return normalized

    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据"""
        if df.empty:
            return df

        print("  🧹 清理和验证数据...")

        # 移除完全空白的行
        df = df.dropna(how='all')

        # 移除没有日期的行
        df = df[df['Date'].notna() & (df['Date'] != '')]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "hdfc_extracted") -> Tuple[str, str, str]:
        """保存解析结果为多种格式"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 确保输出目录存在
        output_dir = "bank_convert"
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件路径
        csv_file = os.path.join(output_dir, f"{output_base}.csv")
        json_file = os.path.join(output_dir, f"{output_base}.json")
        excel_file = os.path.join(output_dir, f"{output_base}.xlsx")

        try:
            # 保存为CSV
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ CSV文件已保存: {csv_file}")

            # 保存为JSON
            df.to_json(json_file, orient='records', indent=2, force_ascii=False)
            print(f"✅ JSON文件已保存: {json_file}")

            # 保存为Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='HDFC_Transactions', index=False)
            print(f"✅ Excel文件已保存: {excel_file}")

            return csv_file, json_file, excel_file

        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            return "", "", ""

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成数据验证报告"""
        if df.empty:
            print("❌ 没有数据可生成报告")
            return

        print(f"\n{'='*60}")
        print(f"HDFC银行PDF解析验证报告")
        print(f"{'='*60}")

        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总交易数: {len(df)}")

        # 日期范围
        if 'Date' in df.columns:
            valid_dates = df[df['Date'].notna() & (df['Date'] != '')]
            if not valid_dates.empty:
                print(f"  日期范围: {valid_dates['Date'].min()} 到 {valid_dates['Date'].max()}")

        # 金额统计
        self._generate_amount_statistics(df)

        # 数据质量检查
        self._generate_quality_report(df)

    def _generate_amount_statistics(self, df: pd.DataFrame) -> None:
        """生成金额统计报告"""
        print(f"\n💰 金额统计:")

        # 取款统计
        if 'WithdrawalAmt.' in df.columns:
            withdrawals = df[df['WithdrawalAmt.'].notna() & (df['WithdrawalAmt.'] != '')]
            if not withdrawals.empty:
                print(f"  取款交易: {len(withdrawals)} 笔")

        # 存款统计
        if 'DepositAmt.' in df.columns:
            deposits = df[df['DepositAmt.'].notna() & (df['DepositAmt.'] != '')]
            if not deposits.empty:
                print(f"  存款交易: {len(deposits)} 笔")

        # 余额统计
        if 'ClosingBalance' in df.columns:
            balances = df[df['ClosingBalance'].notna() & (df['ClosingBalance'] != '')]
            if not balances.empty:
                print(f"  余额记录: {len(balances)} 条")

    def _generate_quality_report(self, df: pd.DataFrame) -> None:
        """生成数据质量报告"""
        print(f"\n🔍 数据质量检查:")

        # 检查必填字段
        required_fields = ['Date', 'Narration']
        for field in required_fields:
            if field in df.columns:
                missing_count = df[field].isna().sum() + (df[field] == '').sum()
                if missing_count > 0:
                    print(f"  ⚠️ {field} 缺失: {missing_count} 行")
                else:
                    print(f"  ✅ {field} 完整")

        # 检查描述质量
        if 'Narration' in df.columns:
            short_descriptions = (df['Narration'].str.len() < 5).sum()
            if short_descriptions > 0:
                print(f"  ⚠️ 描述过短: {short_descriptions} 行")
            else:
                print(f"  ✅ 描述质量良好")


def main():
    """主函数 - 使用示例"""
    parser = HDFCPDFParser()

    # 示例PDF文件路径（需要根据实际情况修改）
    pdf_path = "files/2-hdfc-0552932927-recent-hdfc-statement.pdf"

    print("🚀 启动HDFC银行PDF解析器")

    # 解析PDF
    df = parser.parse_hdfc_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        csv_file, json_file, excel_file = parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 HDFC银行PDF解析完成！")
        print(f"📁 输出文件:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")
        return df
    else:
        print(f"\n❌ HDFC银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
