#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PNB (Punjab National Bank) 银行对账单PDF解析器 - 重构版本
原始来源: pnb/pnb_pdf_parser.py
重构为独立的解析器，包含所有必要功能，无外部依赖

解析策略：Tabula主导策略，针对PNB银行的表格结构优化
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class PNBPDFParser:
    """PNB银行PDF解析器 - Tabula主导策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Transaction Date', 'Cheque Number', 'Withdrawal', 'Deposit', 'Balance', 'Narration'
        ]
        self.standard_columns = [
            'Date', 'Particulars', 'Deposits', 'Withdrawals', 'Balance'
        ]
        
    def parse_pnb_pdf(self, pdf_path: str) -> pd.DataFrame:
        """解析PNB银行PDF的主要方法"""
        print(f"\n{'='*80}")
        print(f"PNB银行PDF解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：使用Tabula提取表格数据（PNB表格结构良好）
            print("\n🔄 第一步：使用Tabula提取表格数据...")
            df_tabula = self._extract_tabula_data(pdf_path)
            
            if df_tabula.empty:
                print("❌ Tabula提取失败，尝试PyPDF备选方案...")
                # 备选方案：使用PyPDF
                df_pypdf = self._extract_pypdf_data(pdf_path)
                if df_pypdf.empty:
                    print("❌ PyPDF提取也失败")
                    return pd.DataFrame()
                df_final = df_pypdf
            else:
                df_final = df_tabula
            
            # 第二步：数据清洗和标准化
            print("\n🔄 第二步：数据清洗和标准化...")
            df_final = self._clean_and_standardize_data(df_final)
            
            # 第三步：数据验证
            print("\n🔄 第三步：数据验证...")
            df_final = self._validate_data(df_final)
            
            print(f"\n✅ PNB银行PDF解析完成！提取交易数: {len(df_final)} 条")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_tabula_data(self, pdf_path: str) -> pd.DataFrame:
        """使用Tabula提取表格数据"""
        try:
            # PNB银行表格结构良好，使用lattice模式
            print("  📋 使用Tabula lattice模式提取表格...")
            dfs = tabula.read_pdf(pdf_path, pages='all', lattice=True)
            print(f"  📄 找到 {len(dfs)} 个表格")

            if not dfs:
                print("  ❌ 未找到任何表格")
                return pd.DataFrame()

            # 合并所有页面的表格
            all_transactions = []

            for i, df in enumerate(dfs):
                print(f"  📊 处理表格 {i+1}: 形状 {df.shape}")

                # 清理表格数据
                df_cleaned = self._clean_tabula_table(df, i+1)

                if not df_cleaned.empty:
                    all_transactions.append(df_cleaned)
                    print(f"    ✅ 表格 {i+1} 清理后: {len(df_cleaned)} 行")
                else:
                    print(f"    ⚠️ 表格 {i+1} 清理后为空")

            if not all_transactions:
                print("  ❌ 所有表格清理后都为空")
                return pd.DataFrame()

            # 合并所有表格
            df_combined = pd.concat(all_transactions, ignore_index=True)
            print(f"  ✅ 合并后总计: {len(df_combined)} 行")

            return df_combined

        except Exception as e:
            print(f"  ❌ Tabula提取失败: {e}")
            return pd.DataFrame()
    
    def _clean_tabula_table(self, df: pd.DataFrame, table_num: int) -> pd.DataFrame:
        """清理Tabula提取的表格数据"""
        if df.empty:
            return df

        print(f"    🧹 清理表格 {table_num}...")

        # 移除完全空白的行
        df = df.dropna(how='all')

        if df.empty:
            return df

        # 过滤掉表头行和无效行
        df = self._filter_valid_transactions(df)

        return df
    
    def _filter_valid_transactions(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤有效的交易行"""
        if df.empty:
            return df

        valid_rows = []

        for idx, row in df.iterrows():
            # 检查是否是表头行
            if self._is_header_row(row):
                continue

            # 检查是否是有效的交易行
            if self._is_valid_transaction_row(row):
                valid_rows.append(row)

        if valid_rows:
            return pd.DataFrame(valid_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()
    
    def _is_header_row(self, row) -> bool:
        """检查是否是表头行"""
        row_str = ' '.join([str(val) for val in row if pd.notna(val)]).upper()
        
        header_keywords = ['DATE', 'TRANSACTION', 'CHEQUE', 'WITHDRAWAL', 'DEPOSIT', 'BALANCE', 'NARRATION']
        
        # 如果包含多个表头关键词，认为是表头行
        keyword_count = sum(1 for keyword in header_keywords if keyword in row_str)
        return keyword_count >= 3
    
    def _is_valid_transaction_row(self, row) -> bool:
        """检查是否是有效的交易行"""
        # 检查是否有日期信息
        for cell in row:
            cell_str = str(cell).strip()
            if cell_str and cell_str.lower() != 'nan':
                # 检查是否包含日期模式
                if re.search(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', cell_str):
                    return True
        
        return False
    
    def _extract_pypdf_data(self, pdf_path: str) -> pd.DataFrame:
        """使用PyPDF提取数据（备选方案）"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                all_text = ""
                
                for page in reader.pages:
                    text = page.extract_text()
                    all_text += text + "\n"
                
                return self._parse_text_to_dataframe(all_text)
                
        except Exception as e:
            print(f"  ❌ PyPDF提取失败: {e}")
            return pd.DataFrame()
    
    def _parse_text_to_dataframe(self, text: str) -> pd.DataFrame:
        """将文本解析为DataFrame"""
        lines = text.split('\n')
        transactions = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 简单的行解析逻辑
            if re.search(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', line):
                # 包含日期的行可能是交易行
                parts = re.split(r'\s+', line)
                if len(parts) >= 3:
                    transaction = {
                        'Date': parts[0],
                        'Description': ' '.join(parts[1:-2]) if len(parts) > 3 else parts[1],
                        'Amount': parts[-1] if len(parts) > 2 else ''
                    }
                    transactions.append(transaction)
        
        return pd.DataFrame(transactions)
    
    def _clean_and_standardize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗和标准化数据"""
        if df.empty:
            return df
        
        print("  🧹 清洗和标准化数据...")
        
        # 移除完全空白的行
        df = df.dropna(how='all')
        
        # 重置索引
        df.reset_index(drop=True, inplace=True)
        
        return df
    
    def _validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据"""
        if df.empty:
            return df
        
        print("  ✅ 验证数据...")
        
        # 重置索引
        df.reset_index(drop=True, inplace=True)
        
        return df
    
    def save_results(self, df: pd.DataFrame, output_base: str = "pnb_extracted") -> Tuple[str, str, str]:
        """保存解析结果为多种格式"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 确保输出目录存在
        output_dir = "bank_convert"
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件路径
        csv_file = os.path.join(output_dir, f"{output_base}.csv")
        json_file = os.path.join(output_dir, f"{output_base}.json")
        excel_file = os.path.join(output_dir, f"{output_base}.xlsx")

        try:
            # 保存为CSV
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ CSV文件已保存: {csv_file}")

            # 保存为JSON
            df.to_json(json_file, orient='records', indent=2, force_ascii=False)
            print(f"✅ JSON文件已保存: {json_file}")

            # 保存为Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='PNB_Transactions', index=False)
            print(f"✅ Excel文件已保存: {excel_file}")

            return csv_file, json_file, excel_file

        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            return "", "", ""
    
    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成数据验证报告"""
        if df.empty:
            print("❌ 没有数据可生成报告")
            return

        print(f"\n{'='*60}")
        print(f"PNB银行PDF解析验证报告")
        print(f"{'='*60}")

        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总记录数: {len(df)}")
        print(f"  总列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")

        # 数据质量检查
        print(f"\n🔍 数据质量检查:")
        for col in df.columns:
            missing_count = df[col].isna().sum()
            if missing_count > 0:
                print(f"  ⚠️ {col} 缺失: {missing_count} 行")
            else:
                print(f"  ✅ {col} 完整")


def main():
    """主函数 - 使用示例"""
    parser = PNBPDFParser()
    
    # 示例PDF文件路径（需要根据实际情况修改）
    pdf_path = "files/5-pnb-737998343-pnb-statement.pdf"
    
    print("🚀 启动PNB银行PDF解析器")
    
    # 解析PDF
    df = parser.parse_pnb_pdf(pdf_path)
    
    if not df.empty:
        # 保存结果
        csv_file, json_file, excel_file = parser.save_results(df)
        
        # 生成验证报告
        parser.generate_validation_report(df)
        
        print(f"\n🎉 PNB银行PDF解析完成！")
        print(f"📁 输出文件:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")
        return df
    else:
        print(f"\n❌ PNB银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
