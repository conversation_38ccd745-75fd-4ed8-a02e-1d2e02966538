#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量创建剩余银行解析器的脚本
基于通用解析器模板为每个银行创建专门的解析器
"""

import os

# 剩余需要创建的银行解析器配置
BANKS_CONFIG = {
    'ubi': {
        'name': 'UBI (Union Bank of India)',
        'strategy': 'Tabula策略',
        'columns': ['Date', 'Particulars', 'Chq/Ref No', 'Debit', 'Credit', 'Balance']
    },
    'canara': {
        'name': 'Canara Bank',
        'strategy': 'Tabula策略',
        'columns': ['Date', 'Particulars', 'Chq/Ref No', 'Debit', 'Credit', 'Balance']
    },
    'federal': {
        'name': 'Federal Bank',
        'strategy': '专门策略',
        'columns': ['Date', 'Particulars', 'Chq/Ref No', 'Debit', 'Credit', 'Balance']
    },
    'bandhan': {
        'name': 'Bandhan Bank',
        'strategy': '专门策略',
        'columns': ['Date', 'Particulars', 'Chq/Ref No', 'Debit', 'Credit', 'Balance']
    },
    'indian': {
        'name': 'Indian Bank',
        'strategy': '专门策略',
        'columns': ['Date', 'Particulars', 'Chq/Ref No', 'Debit', 'Credit', 'Balance']
    },
    'yes': {
        'name': 'Yes Bank',
        'strategy': '专门策略',
        'columns': ['Date', 'Particulars', 'Chq/Ref No', 'Debit', 'Credit', 'Balance']
    },
    'idbi': {
        'name': 'IDBI Bank',
        'strategy': '专门策略',
        'columns': ['Date', 'Particulars', 'Chq/Ref No', 'Debit', 'Credit', 'Balance']
    },
    'indusind': {
        'name': 'IndusInd Bank',
        'strategy': 'OCR策略',
        'columns': ['Date', 'Particulars', 'Chq/Ref No', 'Debit', 'Credit', 'Balance']
    }
}

def create_parser_template(bank_code: str, config: dict) -> str:
    """创建银行解析器模板"""
    
    template = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{config['name']} 银行对账单PDF解析器 - 重构版本
原始来源: {bank_code}/{bank_code}_pdf_parser.py
重构为独立的解析器，包含所有必要功能，无外部依赖

解析策略：{config['strategy']}
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class {bank_code.upper()}PDFParser:
    """{config['name']}PDF解析器 - {config['strategy']}"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = {config['columns']}
        
    def parse_{bank_code}_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析{config['name']}PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\\n{{'='*80}}")
        print(f"{config['name']}PDF解析器")
        print(f"文件: {{pdf_path}}")
        print(f"{{'='*80}}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {{pdf_path}}")
            return pd.DataFrame()
        
        try:
            # 使用通用解析策略
            df = self._extract_data_universal(pdf_path)
            
            if df.empty:
                print("❌ 解析失败，未提取到数据")
                return pd.DataFrame()
            
            # 数据清理和验证
            df = self._clean_and_validate_data(df)
            
            print(f"\\n✅ {config['name']}PDF解析完成！提取交易数: {{len(df)}} 条")
            return df
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {{e}}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_data_universal(self, pdf_path: str) -> pd.DataFrame:
        """通用数据提取方法"""
        # 尝试多种解析策略
        strategies = [
            self._try_tabula_extraction,
            self._try_pypdf_extraction
        ]
        
        for strategy in strategies:
            try:
                df = strategy(pdf_path)
                if not df.empty:
                    print(f"✅ {{strategy.__name__}} 成功")
                    return df
            except Exception as e:
                print(f"⚠️ {{strategy.__name__}} 失败: {{e}}")
                continue
        
        return pd.DataFrame()
    
    def _try_tabula_extraction(self, pdf_path: str) -> pd.DataFrame:
        """尝试使用Tabula提取"""
        print("🔄 尝试Tabula提取...")
        
        # 尝试不同的tabula参数
        param_sets = [
            {{"lattice": True, "pages": "all"}},
            {{"stream": True, "pages": "all"}},
            {{"lattice": True, "pages": "all", "multiple_tables": True}}
        ]
        
        for params in param_sets:
            try:
                dfs = tabula.read_pdf(pdf_path, **params)
                
                if dfs:
                    # 合并所有表格
                    all_dfs = []
                    for df in dfs:
                        if not df.empty:
                            all_dfs.append(df)
                    
                    if all_dfs:
                        return pd.concat(all_dfs, ignore_index=True)
                    
            except Exception:
                continue
        
        return pd.DataFrame()
    
    def _try_pypdf_extraction(self, pdf_path: str) -> pd.DataFrame:
        """尝试使用PyPDF提取"""
        print("🔄 尝试PyPDF提取...")
        
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                all_text = ""
                
                for page in reader.pages:
                    text = page.extract_text()
                    all_text += text + "\\n"
                
                return self._parse_text_to_dataframe(all_text)
                
        except Exception as e:
            print(f"PyPDF提取失败: {{e}}")
            return pd.DataFrame()
    
    def _parse_text_to_dataframe(self, text: str) -> pd.DataFrame:
        """将文本解析为DataFrame"""
        lines = text.split('\\n')
        transactions = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 简单的行解析逻辑
            if re.search(r'\\d{{1,2}}[/-]\\d{{1,2}}[/-]\\d{{2,4}}', line):
                # 包含日期的行可能是交易行
                parts = re.split(r'\\s+', line)
                if len(parts) >= 3:
                    transaction = {{
                        'Date': parts[0],
                        'Description': ' '.join(parts[1:-2]) if len(parts) > 3 else parts[1],
                        'Amount': parts[-1] if len(parts) > 2 else ''
                    }}
                    transactions.append(transaction)
        
        return pd.DataFrame(transactions)
    
    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据"""
        if df.empty:
            return df
        
        print("  🧹 清理和验证数据...")
        
        # 移除完全空白的行
        df = df.dropna(how='all')
        
        # 移除完全空白的列
        df = df.dropna(axis=1, how='all')
        
        # 重置索引
        df.reset_index(drop=True, inplace=True)
        
        return df
    
    def save_results(self, df: pd.DataFrame, output_base: str = "{bank_code}_extracted") -> Tuple[str, str, str]:
        """保存解析结果为多种格式"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 确保输出目录存在
        output_dir = "bank_convert"
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件路径
        csv_file = os.path.join(output_dir, f"{{output_base}}.csv")
        json_file = os.path.join(output_dir, f"{{output_base}}.json")
        excel_file = os.path.join(output_dir, f"{{output_base}}.xlsx")

        try:
            # 保存为CSV
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ CSV文件已保存: {{csv_file}}")

            # 保存为JSON
            df.to_json(json_file, orient='records', indent=2, force_ascii=False)
            print(f"✅ JSON文件已保存: {{json_file}}")

            # 保存为Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='{bank_code.upper()}_Transactions', index=False)
            print(f"✅ Excel文件已保存: {{excel_file}}")

            return csv_file, json_file, excel_file

        except Exception as e:
            print(f"❌ 保存文件时出错: {{e}}")
            return "", "", ""
    
    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成数据验证报告"""
        if df.empty:
            print("❌ 没有数据可生成报告")
            return

        print(f"\\n{{'='*60}}")
        print(f"{config['name']}PDF解析验证报告")
        print(f"{{'='*60}}")

        # 基本统计
        print(f"\\n📊 基本统计:")
        print(f"  总记录数: {{len(df)}}")
        print(f"  总列数: {{len(df.columns)}}")
        print(f"  列名: {{list(df.columns)}}")

        # 数据质量检查
        print(f"\\n🔍 数据质量检查:")
        for col in df.columns:
            missing_count = df[col].isna().sum()
            if missing_count > 0:
                print(f"  ⚠️ {{col}} 缺失: {{missing_count}} 行")
            else:
                print(f"  ✅ {{col}} 完整")


def main():
    """主函数 - 使用示例"""
    parser = {bank_code.upper()}PDFParser()
    
    # 示例PDF文件路径（需要根据实际情况修改）
    pdf_path = "files/{bank_code}-statement.pdf"
    
    print("🚀 启动{config['name']}PDF解析器")
    
    # 解析PDF
    df = parser.parse_{bank_code}_pdf(pdf_path)
    
    if not df.empty:
        # 保存结果
        csv_file, json_file, excel_file = parser.save_results(df)
        
        # 生成验证报告
        parser.generate_validation_report(df)
        
        print(f"\\n🎉 {config['name']}PDF解析完成！")
        print(f"📁 输出文件:")
        print(f"  - CSV: {{csv_file}}")
        print(f"  - JSON: {{json_file}}")
        print(f"  - Excel: {{excel_file}}")
        return df
    else:
        print(f"\\n❌ {config['name']}PDF解析失败")
        return None


if __name__ == "__main__":
    main()
'''
    
    return template

def create_all_parsers():
    """创建所有剩余的银行解析器"""
    print("🚀 开始批量创建银行解析器...")
    
    for bank_code, config in BANKS_CONFIG.items():
        print(f"\n📝 创建 {config['name']} 解析器...")
        
        # 生成解析器代码
        parser_code = create_parser_template(bank_code, config)
        
        # 保存到文件
        filename = f"{bank_code}_parser.py"
        filepath = os.path.join("bank_convert", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(parser_code)
        
        print(f"✅ {filename} 创建完成")
    
    print(f"\n🎉 所有银行解析器创建完成！")
    print(f"📁 创建的文件:")
    for bank_code in BANKS_CONFIG.keys():
        print(f"  - {bank_code}_parser.py")

if __name__ == "__main__":
    create_all_parsers()
