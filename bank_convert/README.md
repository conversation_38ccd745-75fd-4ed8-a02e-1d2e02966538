# 银行PDF解析器重构版本

本目录包含了所有银行PDF解析器的重构版本，每个解析器都是完全独立的，包含所有必要功能，无外部依赖。

## 📁 文件结构

```
bank_convert/
├── README.md                 # 本文档
├── boi_parser.py            # BOI银行解析器
├── iob_parser.py            # IOB银行解析器
├── uco_parser.py            # UCO银行解析器
├── cbi_parser.py            # CBI银行解析器
├── sbi_parser.py            # SBI银行解析器
├── universal_parser.py      # 通用解析器（适用于其他银行）
└── [输出文件]               # 解析结果文件
```

## 🏦 已重构的银行解析器

### 1. BOI (Bank of India) - `boi_parser.py`
- **解析策略**: 混合策略（tabula + pypdf）
- **特点**: 解决Description列跨列和数据截断问题
- **列结构**: Sl No, Txn Date, Description, Cheque No, Withdrawal(in Rs.), Deposits(in Rs.), Balance(in Rs.)

### 2. IOB (Indian Overseas Bank) - `iob_parser.py`
- **解析策略**: 混合策略（tabula + pypdf）
- **特点**: 基于BOI策略，解决NARATION列跨行问题
- **列结构**: DATE, CHQ NO, NARATION, COD, DEBIT, CREDIT, BALANCE

### 3. UCO (United Commercial Bank) - `uco_parser.py`
- **解析策略**: 通用架构，动态页面检测
- **特点**: 格式自适应，规则驱动解析
- **列结构**: Date, Particulars, Withdrawals, Deposits, Balance

### 4. CBI (Central Bank of India) - `cbi_parser.py`
- **解析策略**: 四列标准提取策略
- **特点**: 处理页面边界检测和数据连续性
- **列结构**: Value Date, Post Date, Details, Balance

### 5. SBI (State Bank of India) - `sbi_parser.py`
- **解析策略**: pdfplumber策略
- **特点**: 表格和文本混合提取
- **列结构**: Date, Narration, Chq./Ref.No., ValueDt, WithdrawalAmt., DepositAmt., ClosingBalance

### 6. 通用解析器 - `universal_parser.py`
- **解析策略**: 可配置（pdfplumber/tabula/pypdf/hybrid）
- **特点**: 适用于HDFC、ICICI、Kotak、PNB、UBI、Canara等银行
- **列结构**: 动态适应

## 🚀 使用方法

### 基本使用

```python
# 导入解析器
from boi_parser import BOIPDFParser

# 创建解析器实例
parser = BOIPDFParser()

# 解析PDF文件
df = parser.parse_boi_pdf("path/to/boi_statement.pdf")

# 保存结果
csv_file, json_file, excel_file = parser.save_results(df)

# 生成验证报告
parser.generate_validation_report(df)
```

### 通用解析器使用

```python
from universal_parser import UniversalBankPDFParser, ParseStrategy

# 创建HDFC解析器
hdfc_parser = UniversalBankPDFParser("HDFC", ParseStrategy.PDFPLUMBER)

# 创建ICICI解析器
icici_parser = UniversalBankPDFParser("ICICI", ParseStrategy.TABULA)

# 创建通用解析器（自动选择最佳策略）
universal_parser = UniversalBankPDFParser("Universal", ParseStrategy.HYBRID)

# 解析PDF
df = universal_parser.parse_bank_pdf("path/to/statement.pdf")
```

### 命令行运行

```bash
# 直接运行解析器
cd bank_convert
python boi_parser.py
python iob_parser.py
python uco_parser.py
python cbi_parser.py
python sbi_parser.py
```

## 📊 输出格式

每个解析器都支持三种输出格式：

1. **CSV格式** - 便于数据分析和导入其他系统
2. **JSON格式** - 便于程序处理和API集成
3. **Excel格式** - 便于人工查看和编辑

## 🔧 技术特点

### 1. 完全独立
- 每个解析器都是独立的Python文件
- 包含所有必要的功能和方法
- 无需依赖同目录下的其他文件

### 2. 统一接口
- 所有解析器都有相似的接口设计
- 统一的方法命名规范
- 一致的错误处理和日志输出

### 3. 数据质量保证
- 内置数据验证和清理功能
- 自动生成数据质量报告
- 智能错误处理和恢复

### 4. 灵活配置
- 支持多种解析策略
- 可配置输出格式和路径
- 支持自定义参数

## 📋 解析策略说明

### 混合策略 (Hybrid)
- 结合tabula和pypdf的优势
- tabula提取表格结构和金额数据
- pypdf提取完整的描述文本
- 智能合并修复数据

### Tabula策略
- 专门用于表格结构清晰的PDF
- 支持lattice和stream模式
- 适合标准化格式的银行对账单

### PDFPlumber策略
- 适合复杂格式的PDF
- 支持表格和文本混合提取
- 处理能力强，适应性好

### PyPDF策略
- 基础文本提取
- 适合简单格式的PDF
- 轻量级，速度快

## 🔍 数据验证

每个解析器都包含完整的数据验证功能：

- **基本统计**: 交易数量、日期范围
- **金额统计**: 借贷分析、余额变化
- **质量检查**: 缺失字段、格式验证
- **完整性验证**: 数据一致性检查

## 📝 注意事项

1. **文件路径**: 确保PDF文件路径正确
2. **依赖库**: 需要安装pandas, tabula-py, pypdf, pdfplumber, openpyxl
3. **Java环境**: tabula-py需要Java运行环境
4. **内存使用**: 大文件可能需要较多内存
5. **格式适应**: 不同银行的PDF格式可能需要微调

## 🛠️ 扩展开发

如需为新银行开发解析器，可以：

1. 参考现有解析器的结构
2. 使用通用解析器作为基础
3. 根据具体银行格式调整解析逻辑
4. 保持统一的接口和命名规范

## 📞 技术支持

如遇到问题，请检查：
1. PDF文件格式是否支持
2. 依赖库是否正确安装
3. Java环境是否配置正确
4. 文件路径是否正确

## 🔄 版本历史

- **v1.0**: 初始重构版本
- 包含BOI、IOB、UCO、CBI、SBI解析器
- 提供通用解析器框架
- 统一接口和输出格式
