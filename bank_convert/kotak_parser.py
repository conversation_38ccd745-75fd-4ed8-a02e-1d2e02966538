#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kotak Mahindra Bank 银行对账单PDF解析器 - 重构版本
原始来源: kotak/kotak_pdf_parser.py
重构为独立的解析器，包含所有必要功能，无外部依赖

解析策略：pdfplumber + OCR策略，处理复杂格式的PDF文档
"""

import os
import pandas as pd
import re
import json
import pdfplumber
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class KotakPDFParser:
    """Kotak银行PDF解析器 - pdfplumber + OCR策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Date', 'Narration', 'Chq/Ref No', 'Withdrawal (Dr)/ Deposit (Cr)', 'Balance'
        ]
        
    def parse_kotak_pdf(self, pdf_path: str) -> pd.DataFrame:
        """解析Kotak银行PDF的主要方法"""
        print(f"\n{'='*80}")
        print(f"Kotak银行PDF解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 提取交易数据
            transactions = self._extract_transaction_data_from_pdf(pdf_path)
            
            if not transactions:
                print("未能从PDF中提取到任何交易数据")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(transactions)
            
            # 数据清理和验证
            df = self._clean_and_validate_data(df)
            
            print(f"\n✅ Kotak银行PDF解析完成！提取交易数: {len(df)} 条")
            return df
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_transaction_data_from_pdf(self, pdf_path: str) -> List[Dict]:
        """从Kotak银行PDF中提取交易数据"""
        transactions = []
        
        print(f"正在解析Kotak银行PDF文件: {pdf_path}")
        
        with pdfplumber.open(pdf_path) as pdf:
            print(f"PDF总页数: {len(pdf.pages)}")
            
            for page_num, page in enumerate(pdf.pages, 1):
                print(f"正在处理第 {page_num} 页...")
                
                # 提取表格数据
                tables = page.extract_tables()
                if tables:
                    for table in tables:
                        table_transactions = self._process_table(table)
                        transactions.extend(table_transactions)
                
                # 提取文本数据
                text = page.extract_text()
                if text:
                    text_transactions = self._parse_text_transactions(text)
                    transactions.extend(text_transactions)
        
        print(f"总计提取到 {len(transactions)} 条交易记录")
        return transactions
    
    def _process_table(self, table: List[List]) -> List[Dict]:
        """处理表格数据"""
        transactions = []
        
        if not table or len(table) < 2:
            return transactions
        
        # 查找表头行
        header_row = None
        for i, row in enumerate(table):
            if row and any('Date' in str(cell) for cell in row if cell):
                header_row = i
                break
        
        if header_row is None:
            return transactions
        
        # 处理数据行
        for row_idx in range(header_row + 1, len(table)):
            row = table[row_idx]
            if not row or not any(cell for cell in row):
                continue
            
            transaction = self._parse_table_row(row)
            if transaction:
                transactions.append(transaction)
        
        return transactions
    
    def _parse_table_row(self, row: List) -> Optional[Dict]:
        """解析表格行数据"""
        if not row or len(row) < 3:
            return None
        
        # 基本的行解析
        transaction = {
            'Date': self._parse_date(str(row[0])) if row[0] else '',
            'Narration': str(row[1]) if len(row) > 1 and row[1] else '',
            'Chq/Ref No': str(row[2]) if len(row) > 2 and row[2] else '',
            'Withdrawal (Dr)/ Deposit (Cr)': str(row[3]) if len(row) > 3 and row[3] else '',
            'Balance': str(row[4]) if len(row) > 4 and row[4] else ''
        }
        
        # 验证交易记录
        if transaction['Date'] and (transaction['Narration'] or transaction['Withdrawal (Dr)/ Deposit (Cr)']):
            return transaction
        
        return None
    
    def _parse_text_transactions(self, text: str) -> List[Dict]:
        """从文本中解析交易数据"""
        transactions = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 查找日期模式开始的行
            date_match = re.match(r'^(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\s+(.+)', line)
            if date_match:
                date = date_match.group(1)
                rest_of_line = date_match.group(2).strip()
                
                # 从行尾提取金额
                amounts = re.findall(r'[\d,]+\.\d{2}', rest_of_line)
                
                if amounts:
                    # 最后一个金额是余额
                    balance = amounts[-1]
                    
                    # 移除金额后的文本部分作为描述
                    text_part = rest_of_line
                    for amount in amounts:
                        text_part = text_part.replace(amount, '').strip()
                    
                    # 清理多余的空格
                    text_part = re.sub(r'\s+', ' ', text_part).strip()
                    
                    # 确定取款和存款
                    amount_info = ""
                    if len(amounts) >= 2:
                        amount_info = amounts[-2]
                    
                    transaction = {
                        'Date': self._parse_date(date),
                        'Narration': text_part,
                        'Chq/Ref No': '',
                        'Withdrawal (Dr)/ Deposit (Cr)': amount_info,
                        'Balance': balance
                    }
                    
                    transactions.append(transaction)
        
        return transactions
    
    def _parse_date(self, date_str: str) -> str:
        """解析日期字符串"""
        if not date_str:
            return ""
        
        date_str = str(date_str).strip()
        
        # Kotak日期格式通常是 DD/MM/YYYY
        try:
            if re.match(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', date_str):
                # 标准化日期格式
                date_str = date_str.replace('-', '/')
                return date_str
        except:
            pass
        
        return date_str
    
    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据"""
        if df.empty:
            return df
        
        print("  🧹 清理和验证数据...")
        
        # 移除完全空白的行
        df = df.dropna(how='all')
        
        # 移除没有日期的行
        df = df[df['Date'].notna() & (df['Date'] != '')]
        
        # 重置索引
        df.reset_index(drop=True, inplace=True)
        
        return df
    
    def save_results(self, df: pd.DataFrame, output_base: str = "kotak_extracted") -> Tuple[str, str, str]:
        """保存解析结果为多种格式"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 确保输出目录存在
        output_dir = "bank_convert"
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件路径
        csv_file = os.path.join(output_dir, f"{output_base}.csv")
        json_file = os.path.join(output_dir, f"{output_base}.json")
        excel_file = os.path.join(output_dir, f"{output_base}.xlsx")

        try:
            # 保存为CSV
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ CSV文件已保存: {csv_file}")

            # 保存为JSON
            df.to_json(json_file, orient='records', indent=2, force_ascii=False)
            print(f"✅ JSON文件已保存: {json_file}")

            # 保存为Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Kotak_Transactions', index=False)
            print(f"✅ Excel文件已保存: {excel_file}")

            return csv_file, json_file, excel_file

        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            return "", "", ""
    
    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成数据验证报告"""
        if df.empty:
            print("❌ 没有数据可生成报告")
            return

        print(f"\n{'='*60}")
        print(f"Kotak银行PDF解析验证报告")
        print(f"{'='*60}")

        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总交易数: {len(df)}")

        # 日期范围
        if 'Date' in df.columns:
            valid_dates = df[df['Date'].notna() & (df['Date'] != '')]
            if not valid_dates.empty:
                print(f"  日期范围: {valid_dates['Date'].min()} 到 {valid_dates['Date'].max()}")

        # 数据质量检查
        print(f"\n🔍 数据质量检查:")
        required_fields = ['Date', 'Narration']
        for field in required_fields:
            if field in df.columns:
                missing_count = df[field].isna().sum() + (df[field] == '').sum()
                if missing_count > 0:
                    print(f"  ⚠️ {field} 缺失: {missing_count} 行")
                else:
                    print(f"  ✅ {field} 完整")


def main():
    """主函数 - 使用示例"""
    parser = KotakPDFParser()
    
    # 示例PDF文件路径（需要根据实际情况修改）
    pdf_path = "files/4-kotak-737998343-kotak-statement.pdf"
    
    print("🚀 启动Kotak银行PDF解析器")
    
    # 解析PDF
    df = parser.parse_kotak_pdf(pdf_path)
    
    if not df.empty:
        # 保存结果
        csv_file, json_file, excel_file = parser.save_results(df)
        
        # 生成验证报告
        parser.generate_validation_report(df)
        
        print(f"\n🎉 Kotak银行PDF解析完成！")
        print(f"📁 输出文件:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")
        return df
    else:
        print(f"\n❌ Kotak银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
