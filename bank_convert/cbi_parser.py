#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CBI (Central Bank of India) 银行对账单PDF解析器 - 重构版本
原始来源: cbi/cbi_pdf_parser.py
重构为独立的解析器，包含所有必要功能，无外部依赖

解析策略：四列标准提取策略，处理页面边界检测和数据连续性
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class CBIPDFParser:
    """CBI银行PDF解析器 - 四列标准提取策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Value Date', 'Post Date', 'Details', 'Balance'
        ]
        self.cbi_date_pattern = r'\d{2}/\d{2}/\d{2}'
        
    def parse_cbi_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析CBI银行PDF - 重新设计的四列标准提取策略

        Args:
            pdf_path: PDF文件路径

        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"CBI银行PDF解析器 - 四列标准提取策略")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")

        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()

        try:
            # 第一步：获取PDF页数
            total_pages = self._get_pdf_page_count(pdf_path)
            print(f"\n📄 PDF总页数: {total_pages}")

            # 第二步：逐页提取四列标准数据
            all_transactions = []

            for page_num in range(1, total_pages + 1):
                print(f"\n🔄 处理第{page_num}页...")
                page_data = self._extract_four_column_data(pdf_path, page_num)

                if not page_data.empty:
                    all_transactions.append(page_data)
                    print(f"  ✅ 第{page_num}页提取 {len(page_data)} 条交易")
                else:
                    print(f"  ⚠️ 第{page_num}页没有提取到数据")

            if not all_transactions:
                print("❌ 所有页面都没有提取到数据")
                return pd.DataFrame()

            # 第三步：合并所有页面的数据
            df_final = pd.concat(all_transactions, ignore_index=True)

            # 第四步：数据清理和验证
            df_final = self._clean_and_validate_data(df_final)

            print(f"\n✅ CBI银行PDF解析完成！总计提取 {len(df_final)} 条交易")
            return df_final

        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _get_pdf_page_count(self, pdf_path: str) -> int:
        """获取PDF页数"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                return len(reader.pages)
        except Exception as e:
            print(f"❌ 获取PDF页数失败: {e}")
            return 0
    
    def _extract_four_column_data(self, pdf_path: str, page_num: int) -> pd.DataFrame:
        """
        提取四列标准数据

        Args:
            pdf_path: PDF文件路径
            page_num: 页码

        Returns:
            pd.DataFrame: 提取的交易数据
        """
        try:
            # 使用stream模式进行精确提取
            dfs = tabula.read_pdf(pdf_path, pages=str(page_num),
                                stream=True, area=[0, 0, 800, 600],
                                pandas_options={'header': None, 'dtype': str})

            if not dfs:
                return pd.DataFrame()

            # 处理提取到的表格
            all_page_transactions = []

            for df in dfs:
                if df.empty:
                    continue

                # 过滤页面格式内容
                filtered_df = self._filter_page_format_content(df)

                if filtered_df.empty:
                    continue

                # 根据列数选择处理策略
                if len(filtered_df.columns) == 1:
                    # 1列格式：所有数据混合在一列中
                    transactions = self._process_single_column_format(filtered_df)
                elif len(filtered_df.columns) >= 4:
                    # 多列格式：标准的四列提取
                    transactions = self._process_multi_column_format(filtered_df)
                else:
                    continue

                if not transactions.empty:
                    all_page_transactions.append(transactions)

            if all_page_transactions:
                return pd.concat(all_page_transactions, ignore_index=True)
            else:
                return pd.DataFrame()

        except Exception as e:
            print(f"    ❌ 第{page_num}页提取失败: {e}")
            return pd.DataFrame()
    
    def _filter_page_format_content(self, df: pd.DataFrame) -> pd.DataFrame:
        """过滤页面格式内容"""
        if df.empty:
            return df

        # 移除空行
        df = df.dropna(how='all')

        if df.empty:
            return df

        # 过滤掉页面说明文字和表头
        filtered_rows = []
        for idx, row in df.iterrows():
            row_text = ' '.join([str(val) for val in row if pd.notna(val)]).strip()

            # 跳过页面说明文字
            if self._is_page_format_line(row_text):
                continue

            # 跳过表头行
            if self._is_header_line(row_text):
                continue

            # 跳过BROUGHT FORWARD和CARRIED FORWARD行
            if self._is_boundary_line(row_text):
                continue

            filtered_rows.append(row)

        if filtered_rows:
            return pd.DataFrame(filtered_rows).reset_index(drop=True)
        else:
            return pd.DataFrame()
    
    def _is_page_format_line(self, text: str) -> bool:
        """检查是否是页面格式行"""
        format_keywords = [
            'In Case Your Account Is Operated By A Letter Of Authority',
            'CENTRAL BANK OF INDIA',
            'ACCOUNT STATEMENT',
            'Page No',
            'Statement Period'
        ]
        
        text_upper = text.upper()
        return any(keyword.upper() in text_upper for keyword in format_keywords)
    
    def _is_header_line(self, text: str) -> bool:
        """检查是否是表头行"""
        header_keywords = ['VALUE DATE', 'POST DATE', 'DETAILS', 'BALANCE']
        text_upper = text.upper()
        
        # 如果包含多个表头关键词，认为是表头行
        keyword_count = sum(1 for keyword in header_keywords if keyword in text_upper)
        return keyword_count >= 2
    
    def _is_boundary_line(self, text: str) -> bool:
        """检查是否是页面边界行"""
        boundary_keywords = ['BROUGHT FORWARD', 'CARRIED FORWARD', 'CLOSING BALANCE']
        text_upper = text.upper()
        
        return any(keyword in text_upper for keyword in boundary_keywords)
    
    def _process_single_column_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理单列格式数据"""
        transactions = []
        
        for idx, row in df.iterrows():
            parsed_transaction = self._parse_1_column_row(row)
            if parsed_transaction:
                transactions.append(parsed_transaction)
        
        if transactions:
            return pd.DataFrame(transactions)
        else:
            return pd.DataFrame()
    
    def _process_multi_column_format(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理多列格式数据"""
        # 确保有4列
        if len(df.columns) < 4:
            return pd.DataFrame()
        
        # 取前4列并设置标准列名
        df_4col = df.iloc[:, :4].copy()
        df_4col.columns = self.expected_columns
        
        # 过滤有效交易
        valid_transactions = []
        for idx, row in df_4col.iterrows():
            if self._is_valid_cbi_transaction(row):
                valid_transactions.append(row)
        
        if valid_transactions:
            return pd.DataFrame(valid_transactions).reset_index(drop=True)
        else:
            return pd.DataFrame()
    
    def _is_valid_cbi_transaction(self, row) -> bool:
        """检查是否是有效的CBI交易"""
        # 检查Value Date列
        value_date = str(row['Value Date']).strip()
        if not value_date or value_date.lower() == 'nan':
            return False
        
        # 必须包含日期格式
        if not re.search(self.cbi_date_pattern, value_date):
            return False
        
        # 检查Details列
        details = str(row['Details']).strip()
        if not details or details.lower() == 'nan' or len(details) < 3:
            return False
        
        # 检查Balance列
        balance = str(row['Balance']).strip()
        if not balance or balance.lower() == 'nan':
            return False
        
        return True

    def _parse_1_column_row(self, row) -> Optional[Dict]:
        """解析1列格式的行 - CBI银行的实际格式"""
        first_col = str(row.iloc[0]).strip()

        if not first_col or first_col.lower() == 'nan':
            return None

        # 过滤页面说明文字
        if 'In Case Your Account Is Operated By A Letter Of Authority' in first_col:
            return None

        # CBI银行1列格式示例：'02/10/2102/10/21TO TRF.3,847.001,678.46Cr'
        # 格式：Value Date + Post Date + 描述 + 金额 + 余额Cr

        # 使用正则表达式解析
        import re

        # 改进的匹配模式：处理实际的数据格式
        # 匹配：Value Date + Post Date + 描述 + 金额 + 余额Cr
        pattern = r'(\d{2}/\d{2}/\d{2})(\d{2}/\d{2}/\d{2})(.+?)(\d{1,3}(?:,\d{3})*\.?\d{0,2})(\d{1,3}(?:,\d{3})*\.?\d{0,2}Cr)$'

        match = re.match(pattern, first_col)
        if match:
            value_date = match.group(1)
            post_date = match.group(2)
            details = match.group(3).strip()
            amount = match.group(4)
            balance = match.group(5)

            # 清理余额格式
            balance_clean = balance.replace('Cr', '').strip()

            return {
                'Value Date': value_date,
                'Post Date': post_date,
                'Details': details,
                'Balance': balance_clean
            }

        # 尝试更简单的模式
        simple_pattern = r'(\d{2}/\d{2}/\d{2})(.+?)(\d{1,3}(?:,\d{3})*\.?\d{0,2}Cr)$'
        simple_match = re.match(simple_pattern, first_col)
        if simple_match:
            value_date = simple_match.group(1)
            details = simple_match.group(2).strip()
            balance = simple_match.group(3).replace('Cr', '').strip()

            return {
                'Value Date': value_date,
                'Post Date': value_date,  # 使用相同日期
                'Details': details,
                'Balance': balance
            }

        return None

    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据"""
        if df.empty:
            return df

        print("  🧹 清理和验证数据...")

        # 清理日期列
        df = self._clean_date_columns(df)

        # 清理金额列
        df = self._clean_balance_column(df)

        # 清理描述列
        df = self._clean_details_column(df)

        # 验证数据完整性
        df = self._validate_data_integrity(df)

        return df

    def _clean_date_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理日期列"""
        date_columns = ['Value Date', 'Post Date']

        for col in date_columns:
            if col in df.columns:
                print(f"    📅 清理 {col} 列...")
                df[col] = df[col].apply(self._parse_cbi_date)

        return df

    def _parse_cbi_date(self, value) -> str:
        """解析CBI日期格式"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()

        # CBI日期格式: DD/MM/YY
        if re.match(self.cbi_date_pattern, date_str):
            return date_str

        # 尝试其他格式转换
        try:
            # 尝试解析各种日期格式
            for fmt in ['%d/%m/%y', '%d/%m/%Y', '%d-%m-%y']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%d/%m/%y')  # 统一为CBI格式
                except ValueError:
                    continue
        except:
            pass

        return date_str  # 如果无法解析，返回原值

    def _clean_balance_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理余额列"""
        if 'Balance' in df.columns:
            print("    💰 清理余额列...")
            df['Balance'] = df['Balance'].apply(self._parse_balance_with_suffix)

        return df

    def _parse_balance_with_suffix(self, value) -> str:
        """解析带有Cr/Dr后缀的余额"""
        if pd.isna(value):
            return ""

        balance_str = str(value).strip()

        if not balance_str or balance_str.lower() == 'nan':
            return ""

        # 保持原始格式，包括Cr/Dr后缀
        return balance_str

    def _clean_details_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理描述列"""
        if 'Details' in df.columns:
            print("    📝 清理描述列...")
            df['Details'] = df['Details'].apply(self._clean_description_text)

        return df

    def _clean_description_text(self, text) -> str:
        """清理描述文本"""
        if pd.isna(text):
            return ""

        text_str = str(text).strip()

        # 移除换行符和多余空格
        text_str = re.sub(r'\s+', ' ', text_str)

        return text_str

    def _validate_data_integrity(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据完整性"""
        print("    ✅ 验证数据完整性...")

        # 移除没有日期的行
        before_count = len(df)
        df = df[df['Value Date'].notna() & (df['Value Date'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效日期的记录")

        # 移除没有描述的行
        df = df[df['Details'].notna() & (df['Details'] != '')]

        # 移除没有余额的行
        df = df[df['Balance'].notna() & (df['Balance'] != '')]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "cbi_extracted") -> Tuple[str, str, str]:
        """
        保存解析结果为多种格式

        Args:
            df: 解析后的DataFrame
            output_base: 输出文件名前缀

        Returns:
            Tuple[str, str, str]: CSV、JSON、Excel文件路径
        """
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 确保输出目录存在
        output_dir = "bank_convert"
        os.makedirs(output_dir, exist_ok=True)

        # 在保存前进行最终的文本清理
        df_clean = df.copy()
        if 'Details' in df_clean.columns:
            df_clean['Details'] = df_clean['Details'].apply(
                lambda x: self._clean_description_text(x) if pd.notna(x) else ""
            )

        # 生成文件路径
        csv_file = os.path.join(output_dir, f"{output_base}.csv")
        json_file = os.path.join(output_dir, f"{output_base}.json")
        excel_file = os.path.join(output_dir, f"{output_base}.xlsx")

        try:
            # 保存为CSV
            df_clean.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ CSV文件已保存: {csv_file}")

            # 保存为JSON
            df_clean.to_json(json_file, orient='records', indent=2, force_ascii=False)
            print(f"✅ JSON文件已保存: {json_file}")

            # 保存为Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df_clean.to_excel(writer, sheet_name='CBI_Transactions', index=False)
            print(f"✅ Excel文件已保存: {excel_file}")

            return csv_file, json_file, excel_file

        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            return "", "", ""

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成数据验证报告"""
        if df.empty:
            print("❌ 没有数据可生成报告")
            return

        print(f"\n{'='*60}")
        print(f"CBI银行PDF解析验证报告")
        print(f"{'='*60}")

        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总交易数: {len(df)}")

        # 日期范围
        if 'Value Date' in df.columns:
            valid_dates = df[df['Value Date'].notna() & (df['Value Date'] != '')]
            if not valid_dates.empty:
                print(f"  日期范围: {valid_dates['Value Date'].min()} 到 {valid_dates['Value Date'].max()}")

        # 余额统计
        self._generate_balance_statistics(df)

        # 数据质量检查
        self._generate_quality_report(df)

    def _generate_balance_statistics(self, df: pd.DataFrame) -> None:
        """生成余额统计"""
        print(f"\n💰 余额统计:")

        if 'Balance' in df.columns:
            valid_balances = df[df['Balance'].notna() & (df['Balance'] != '')]

            if not valid_balances.empty:
                print(f"  有效余额记录: {len(valid_balances)} 条")

                # 分析Cr/Dr分布
                cr_count = valid_balances['Balance'].str.contains('Cr', na=False).sum()
                dr_count = valid_balances['Balance'].str.contains('Dr', na=False).sum()

                print(f"  贷方余额(Cr): {cr_count} 条")
                print(f"  借方余额(Dr): {dr_count} 条")

                # 检查首末余额
                first_balance = valid_balances.iloc[0]['Balance']
                last_balance = valid_balances.iloc[-1]['Balance']

                print(f"  期初余额: {first_balance}")
                print(f"  期末余额: {last_balance}")
            else:
                print(f"  ⚠️ 没有有效的余额数据")

    def _generate_quality_report(self, df: pd.DataFrame) -> None:
        """生成数据质量报告"""
        print(f"\n🔍 数据质量检查:")

        # 检查必填字段
        required_fields = ['Value Date', 'Details', 'Balance']
        for field in required_fields:
            if field in df.columns:
                missing_count = df[field].isna().sum() + (df[field] == '').sum()
                if missing_count > 0:
                    print(f"  ⚠️ {field} 缺失: {missing_count} 行")
                else:
                    print(f"  ✅ {field} 完整")

        # 检查日期格式
        if 'Value Date' in df.columns:
            invalid_dates = 0
            for date_val in df['Value Date']:
                if pd.notna(date_val) and not re.match(self.cbi_date_pattern, str(date_val)):
                    invalid_dates += 1

            if invalid_dates > 0:
                print(f"  ⚠️ 日期格式异常: {invalid_dates} 行")
            else:
                print(f"  ✅ 日期格式正确")

        # 检查描述质量
        if 'Details' in df.columns:
            short_descriptions = (df['Details'].str.len() < 5).sum()
            if short_descriptions > 0:
                print(f"  ⚠️ 描述过短: {short_descriptions} 行")
            else:
                print(f"  ✅ 描述质量良好")


def main():
    """主函数 - 使用示例"""
    parser = CBIPDFParser()

    # 示例PDF文件路径（需要根据实际情况修改）
    pdf_path = "files/15-cbi-*********-Bank-statement.pdf"

    print("🚀 启动CBI银行PDF解析器")

    # 解析PDF
    df = parser.parse_cbi_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        csv_file, json_file, excel_file = parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 CBI银行PDF解析完成！")
        print(f"📁 输出文件:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")
        return df
    else:
        print(f"\n❌ CBI银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
