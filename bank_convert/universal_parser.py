#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用银行PDF解析器 - 重构版本
基于项目中的通用解析框架，提供可配置的银行PDF解析解决方案
可用于HDFC、ICICI、Kotak、PNB、UBI、Canara、Federal、Bandhan、Indian、Yes、IDBI、IndusInd等银行
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
import pdfplumber
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Any
from enum import Enum


class ParseStrategy(Enum):
    """解析策略枚举"""
    PDFPLUMBER = "pdfplumber"
    TABULA = "tabula"
    PYPDF = "pypdf"
    HYBRID = "hybrid"


class UniversalBankPDFParser:
    """通用银行PDF解析器"""
    
    def __init__(self, bank_name: str = "Universal", strategy: ParseStrategy = ParseStrategy.HYBRID):
        """
        初始化解析器
        
        Args:
            bank_name: 银行名称
            strategy: 解析策略
        """
        self.bank_name = bank_name
        self.strategy = strategy
        self.expected_columns = []  # 将根据实际解析结果动态设置
        
    def parse_bank_pdf(self, pdf_path: str, **kwargs) -> pd.DataFrame:
        """
        解析银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            **kwargs: 额外参数
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"{self.bank_name}银行PDF通用解析器")
        print(f"解析策略: {self.strategy.value}")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 根据策略选择解析方法
            if self.strategy == ParseStrategy.PDFPLUMBER:
                df = self._parse_with_pdfplumber(pdf_path)
            elif self.strategy == ParseStrategy.TABULA:
                df = self._parse_with_tabula(pdf_path)
            elif self.strategy == ParseStrategy.PYPDF:
                df = self._parse_with_pypdf(pdf_path)
            else:  # HYBRID
                df = self._parse_with_hybrid(pdf_path)
            
            if df.empty:
                print("❌ 解析失败，未提取到数据")
                return pd.DataFrame()
            
            # 数据清理和验证
            df = self._clean_and_validate_data(df)
            
            print(f"\n✅ {self.bank_name}银行PDF解析完成！提取交易数: {len(df)} 条")
            return df
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _parse_with_pdfplumber(self, pdf_path: str) -> pd.DataFrame:
        """使用pdfplumber解析PDF"""
        print("\n🔄 使用pdfplumber解析...")
        
        all_transactions = []
        
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages, 1):
                print(f"  📄 处理第{page_num}页...")
                
                # 提取表格
                tables = page.extract_tables()
                if tables:
                    for table in tables:
                        df_table = pd.DataFrame(table[1:], columns=table[0] if table else [])
                        if not df_table.empty:
                            all_transactions.append(df_table)
                
                # 提取文本（备选）
                text = page.extract_text()
                if text and not tables:
                    df_text = self._parse_text_to_dataframe(text)
                    if not df_text.empty:
                        all_transactions.append(df_text)
        
        if all_transactions:
            return pd.concat(all_transactions, ignore_index=True)
        else:
            return pd.DataFrame()
    
    def _parse_with_tabula(self, pdf_path: str) -> pd.DataFrame:
        """使用tabula解析PDF"""
        print("\n🔄 使用tabula解析...")
        
        try:
            # 尝试不同的tabula参数
            param_sets = [
                {"lattice": True, "pages": "all"},
                {"stream": True, "pages": "all"},
                {"lattice": True, "pages": "all", "multiple_tables": True}
            ]
            
            for params in param_sets:
                try:
                    dfs = tabula.read_pdf(pdf_path, **params)
                    
                    if dfs:
                        # 合并所有表格
                        all_dfs = []
                        for df in dfs:
                            if not df.empty:
                                all_dfs.append(df)
                        
                        if all_dfs:
                            return pd.concat(all_dfs, ignore_index=True)
                        
                except Exception as e:
                    print(f"    ⚠️ tabula参数 {params} 失败: {e}")
                    continue
            
            return pd.DataFrame()
            
        except Exception as e:
            print(f"  ❌ tabula解析失败: {e}")
            return pd.DataFrame()
    
    def _parse_with_pypdf(self, pdf_path: str) -> pd.DataFrame:
        """使用pypdf解析PDF"""
        print("\n🔄 使用pypdf解析...")
        
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                all_text = ""
                
                for page in reader.pages:
                    text = page.extract_text()
                    all_text += text + "\n"
                
                return self._parse_text_to_dataframe(all_text)
                
        except Exception as e:
            print(f"  ❌ pypdf解析失败: {e}")
            return pd.DataFrame()
    
    def _parse_with_hybrid(self, pdf_path: str) -> pd.DataFrame:
        """使用混合策略解析PDF"""
        print("\n🔄 使用混合策略解析...")
        
        # 尝试所有策略，选择最佳结果
        results = []
        
        # 尝试tabula
        df_tabula = self._parse_with_tabula(pdf_path)
        if not df_tabula.empty:
            results.append(('tabula', df_tabula))
        
        # 尝试pdfplumber
        df_pdfplumber = self._parse_with_pdfplumber(pdf_path)
        if not df_pdfplumber.empty:
            results.append(('pdfplumber', df_pdfplumber))
        
        # 尝试pypdf
        df_pypdf = self._parse_with_pypdf(pdf_path)
        if not df_pypdf.empty:
            results.append(('pypdf', df_pypdf))
        
        # 选择最佳结果（行数最多的）
        if results:
            best_strategy, best_df = max(results, key=lambda x: len(x[1]))
            print(f"  ✅ 选择最佳策略: {best_strategy} (提取 {len(best_df)} 行)")
            return best_df
        else:
            return pd.DataFrame()
    
    def _parse_text_to_dataframe(self, text: str) -> pd.DataFrame:
        """将文本解析为DataFrame"""
        lines = text.split('\n')
        transactions = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 简单的行解析逻辑（可根据具体银行格式调整）
            if re.search(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', line):
                # 包含日期的行可能是交易行
                parts = re.split(r'\s+', line)
                if len(parts) >= 3:
                    transaction = {
                        'Date': parts[0],
                        'Description': ' '.join(parts[1:-2]) if len(parts) > 3 else parts[1],
                        'Amount': parts[-1] if len(parts) > 2 else ''
                    }
                    transactions.append(transaction)
        
        return pd.DataFrame(transactions)
    
    def _clean_and_validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理和验证数据"""
        if df.empty:
            return df
        
        print("  🧹 清理和验证数据...")
        
        # 移除完全空白的行
        df = df.dropna(how='all')
        
        # 移除完全空白的列
        df = df.dropna(axis=1, how='all')
        
        # 重置索引
        df.reset_index(drop=True, inplace=True)
        
        return df
    
    def save_results(self, df: pd.DataFrame, output_base: str = None) -> Tuple[str, str, str]:
        """保存解析结果为多种格式"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        if output_base is None:
            output_base = f"{self.bank_name.lower()}_extracted"

        # 确保输出目录存在
        output_dir = "bank_convert"
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件路径
        csv_file = os.path.join(output_dir, f"{output_base}.csv")
        json_file = os.path.join(output_dir, f"{output_base}.json")
        excel_file = os.path.join(output_dir, f"{output_base}.xlsx")

        try:
            # 保存为CSV
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ CSV文件已保存: {csv_file}")

            # 保存为JSON
            df.to_json(json_file, orient='records', indent=2, force_ascii=False)
            print(f"✅ JSON文件已保存: {json_file}")

            # 保存为Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=f'{self.bank_name}_Transactions', index=False)
            print(f"✅ Excel文件已保存: {excel_file}")

            return csv_file, json_file, excel_file

        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            return "", "", ""
    
    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成数据验证报告"""
        if df.empty:
            print("❌ 没有数据可生成报告")
            return

        print(f"\n{'='*60}")
        print(f"{self.bank_name}银行PDF解析验证报告")
        print(f"{'='*60}")

        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总记录数: {len(df)}")
        print(f"  总列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")

        # 数据质量检查
        print(f"\n🔍 数据质量检查:")
        for col in df.columns:
            missing_count = df[col].isna().sum()
            if missing_count > 0:
                print(f"  ⚠️ {col} 缺失: {missing_count} 行")
            else:
                print(f"  ✅ {col} 完整")


def main():
    """主函数 - 使用示例"""
    # 可以为不同银行创建不同的解析器实例
    
    # HDFC示例
    hdfc_parser = UniversalBankPDFParser("HDFC", ParseStrategy.PDFPLUMBER)
    
    # ICICI示例
    icici_parser = UniversalBankPDFParser("ICICI", ParseStrategy.TABULA)
    
    # 通用示例
    universal_parser = UniversalBankPDFParser("Universal", ParseStrategy.HYBRID)
    
    print("🚀 通用银行PDF解析器示例")
    print("可用于多种银行的PDF解析")
    
    # 这里可以添加具体的解析示例
    # df = universal_parser.parse_bank_pdf("path/to/bank/statement.pdf")


if __name__ == "__main__":
    main()
