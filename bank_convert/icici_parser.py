#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ICICI (Industrial Credit and Investment Corporation of India) 银行对账单PDF解析器 - 重构版本
原始来源: icici/icici_pdf_parser.py
重构为独立的解析器，包含所有必要功能，无外部依赖

解析策略：Tabula命令行策略，使用Java命令行工具提取数据并重建交易记录
"""

import os
import pandas as pd
import re
import json
import subprocess
import tempfile
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class ICICIPDFParser:
    """ICICI银行PDF解析器 - Tabula命令行策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Date', 'Particulars', 'Chq/Ref No', 'Value Date', 
            'Withdrawal Amt.', 'Deposit Amt.', 'Closing Balance'
        ]
        
        # 设置Java和Tabula路径
        self.java_home = os.environ.get('JAVA_HOME', '/usr/lib/jvm/default-java')
        self.jar_path = self._find_tabula_jar()
        
    def _find_tabula_jar(self) -> str:
        """查找tabula-java jar文件"""
        # 常见的tabula jar位置
        possible_paths = [
            "/usr/local/lib/tabula-java/tabula-1.0.5-jar-with-dependencies.jar",
            "/opt/tabula/tabula-1.0.5-jar-with-dependencies.jar",
            "tabula-java/tabula-1.0.5-jar-with-dependencies.jar"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果找不到，返回默认路径
        return "tabula-1.0.5-jar-with-dependencies.jar"
        
    def parse_icici_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析ICICI银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"ICICI银行PDF解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 获取原始Tabula输出
            raw_lines = self._get_tabula_output(pdf_path)
            if not raw_lines:
                print("❌ 无法获取Tabula输出，尝试备选方案...")
                return self._fallback_parsing(pdf_path)
            
            # 查找表头位置
            header_line = self._find_header_line(raw_lines)
            if header_line is None:
                print("❌ 未找到表头，尝试备选方案...")
                return self._fallback_parsing(pdf_path)
            
            # 重建交易记录
            transactions = self._rebuild_transactions(raw_lines, header_line)
            
            # 转换为DataFrame
            df = self._create_dataframe(transactions)
            
            # 验证和清理结果
            df = self._validate_and_clean_data(df)
            
            print(f"\n✅ ICICI银行PDF解析完成！提取交易数: {len(df)} 条")
            return df
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _get_tabula_output(self, pdf_path: str) -> List[str]:
        """获取Tabula输出"""
        try:
            cmd = [
                f"{self.java_home}/bin/java",
                "-jar", self.jar_path,
                "--pages=all",
                "--format=CSV",
                "--stream",
                pdf_path
            ]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=120
            )
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                print(f"✅ 获取Tabula输出: {len(lines)} 行")
                return lines
            else:
                print(f"❌ Tabula执行失败")
                return []
                
        except Exception as e:
            print(f"❌ 获取Tabula输出失败: {e}")
            return []
    
    def _find_header_line(self, lines: List[str]) -> Optional[int]:
        """查找表头行"""
        for i, line in enumerate(lines):
            if 'DATE' in line.upper() and 'PARTICULARS' in line.upper() and 'BALANCE' in line.upper():
                print(f"✅ 找到表头行: {i}")
                return i
        
        print(f"❌ 未找到表头行")
        return None
    
    def _rebuild_transactions(self, lines: List[str], header_line: int) -> List[Dict]:
        """重建交易记录"""
        transactions = []
        current_transaction = {}
        
        print(f"🔄 重建交易记录...")
        
        for i in range(header_line + 1, len(lines)):
            line = lines[i].strip()
            if not line:
                continue
            
            # 解析CSV行
            fields = self._parse_csv_line(line)
            
            # 检查是否是新交易的开始
            if self._is_transaction_start(fields):
                # 保存之前的交易
                if current_transaction:
                    transactions.append(current_transaction.copy())
                
                # 开始新交易
                current_transaction = self._create_transaction_from_fields(fields)
            else:
                # 继续当前交易（可能是多行描述）
                if current_transaction:
                    self._append_to_current_transaction(current_transaction, fields)
        
        # 保存最后一个交易
        if current_transaction:
            transactions.append(current_transaction)
        
        print(f"✅ 重建完成，共 {len(transactions)} 条交易")
        return transactions
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行"""
        # 简单的CSV解析，处理引号和逗号
        fields = []
        current_field = ""
        in_quotes = False
        
        for char in line:
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
        
        # 添加最后一个字段
        fields.append(current_field.strip())
        
        return fields
    
    def _is_transaction_start(self, fields: List[str]) -> bool:
        """检查是否是交易开始行"""
        if not fields or len(fields) < 2:
            return False
        
        # 检查第一个字段是否包含日期
        date_pattern = r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}'
        return bool(re.search(date_pattern, fields[0]))
    
    def _create_transaction_from_fields(self, fields: List[str]) -> Dict:
        """从字段创建交易记录"""
        transaction = {
            'Date': '',
            'Particulars': '',
            'Chq/Ref No': '',
            'Value Date': '',
            'Withdrawal Amt.': '',
            'Deposit Amt.': '',
            'Closing Balance': ''
        }
        
        # 映射字段到交易记录
        if len(fields) >= 1:
            transaction['Date'] = self._parse_date(fields[0])
        if len(fields) >= 2:
            transaction['Particulars'] = fields[1]
        if len(fields) >= 3:
            transaction['Chq/Ref No'] = fields[2]
        if len(fields) >= 4:
            transaction['Value Date'] = self._parse_date(fields[3])
        if len(fields) >= 5:
            transaction['Withdrawal Amt.'] = self._parse_amount(fields[4])
        if len(fields) >= 6:
            transaction['Deposit Amt.'] = self._parse_amount(fields[5])
        if len(fields) >= 7:
            transaction['Closing Balance'] = self._parse_amount(fields[6])
        
        return transaction
    
    def _append_to_current_transaction(self, transaction: Dict, fields: List[str]) -> None:
        """将字段追加到当前交易"""
        # 通常是描述的延续
        if fields and fields[0]:
            if transaction['Particulars']:
                transaction['Particulars'] += " " + fields[0]
            else:
                transaction['Particulars'] = fields[0]
    
    def _parse_date(self, date_str: str) -> str:
        """解析日期字符串"""
        if not date_str:
            return ""
        
        # ICICI日期格式通常是 DD/MM/YYYY 或 DD-MM-YYYY
        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{2})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                return match.group(1)
        
        return date_str
    
    def _parse_amount(self, amount_str: str) -> str:
        """解析金额字符串"""
        if not amount_str:
            return ""
        
        # 移除非数字字符（保留小数点、逗号和负号）
        cleaned = re.sub(r'[^\d.,-]', '', amount_str)
        
        # 检查是否为有效金额
        if re.match(r'^[\d,]+\.?\d*$', cleaned):
            return cleaned
        
        return ""

    def _create_dataframe(self, transactions: List[Dict]) -> pd.DataFrame:
        """创建DataFrame"""
        if not transactions:
            return pd.DataFrame()

        df = pd.DataFrame(transactions)

        # 确保列顺序正确
        df = df.reindex(columns=self.expected_columns, fill_value='')

        return df

    def _validate_and_clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清理数据"""
        if df.empty:
            return df

        print("  🧹 验证和清理数据...")

        # 移除没有日期的行
        df = df[df['Date'].notna() & (df['Date'] != '')]

        # 移除没有描述的行
        df = df[df['Particulars'].notna() & (df['Particulars'] != '')]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def _fallback_parsing(self, pdf_path: str) -> pd.DataFrame:
        """备选解析方案"""
        print("🔄 使用备选解析方案...")

        try:
            import tabula

            # 尝试使用tabula-py
            dfs = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)

            if dfs:
                # 合并所有表格
                all_data = []
                for df in dfs:
                    if not df.empty:
                        all_data.append(df)

                if all_data:
                    combined_df = pd.concat(all_data, ignore_index=True)
                    return self._process_fallback_dataframe(combined_df)

        except ImportError:
            print("  ⚠️ tabula-py未安装，跳过备选方案")
        except Exception as e:
            print(f"  ❌ 备选方案失败: {e}")

        return pd.DataFrame()

    def _process_fallback_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理备选方案的DataFrame"""
        # 简单的列名映射和清理
        if df.empty:
            return df

        # 尝试识别和重命名列
        for i, col in enumerate(df.columns):
            col_str = str(col).upper()
            if 'DATE' in col_str:
                df.rename(columns={col: 'Date'}, inplace=True)
            elif 'PARTICULAR' in col_str or 'DESCRIPTION' in col_str:
                df.rename(columns={col: 'Particulars'}, inplace=True)
            elif 'BALANCE' in col_str:
                df.rename(columns={col: 'Closing Balance'}, inplace=True)
            elif 'WITHDRAWAL' in col_str or 'DEBIT' in col_str:
                df.rename(columns={col: 'Withdrawal Amt.'}, inplace=True)
            elif 'DEPOSIT' in col_str or 'CREDIT' in col_str:
                df.rename(columns={col: 'Deposit Amt.'}, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "icici_extracted") -> Tuple[str, str, str]:
        """保存解析结果为多种格式"""
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 确保输出目录存在
        output_dir = "bank_convert"
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件路径
        csv_file = os.path.join(output_dir, f"{output_base}.csv")
        json_file = os.path.join(output_dir, f"{output_base}.json")
        excel_file = os.path.join(output_dir, f"{output_base}.xlsx")

        try:
            # 保存为CSV
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ CSV文件已保存: {csv_file}")

            # 保存为JSON
            df.to_json(json_file, orient='records', indent=2, force_ascii=False)
            print(f"✅ JSON文件已保存: {json_file}")

            # 保存为Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='ICICI_Transactions', index=False)
            print(f"✅ Excel文件已保存: {excel_file}")

            return csv_file, json_file, excel_file

        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            return "", "", ""

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成数据验证报告"""
        if df.empty:
            print("❌ 没有数据可生成报告")
            return

        print(f"\n{'='*60}")
        print(f"ICICI银行PDF解析验证报告")
        print(f"{'='*60}")

        # 基本统计
        print(f"\n📊 基本统计:")
        print(f"  总交易数: {len(df)}")

        # 日期范围
        if 'Date' in df.columns:
            valid_dates = df[df['Date'].notna() & (df['Date'] != '')]
            if not valid_dates.empty:
                print(f"  日期范围: {valid_dates['Date'].min()} 到 {valid_dates['Date'].max()}")

        # 金额统计
        self._generate_amount_statistics(df)

        # 数据质量检查
        self._generate_quality_report(df)

    def _generate_amount_statistics(self, df: pd.DataFrame) -> None:
        """生成金额统计报告"""
        print(f"\n💰 金额统计:")

        # 取款统计
        if 'Withdrawal Amt.' in df.columns:
            withdrawals = df[df['Withdrawal Amt.'].notna() & (df['Withdrawal Amt.'] != '')]
            if not withdrawals.empty:
                print(f"  取款交易: {len(withdrawals)} 笔")

        # 存款统计
        if 'Deposit Amt.' in df.columns:
            deposits = df[df['Deposit Amt.'].notna() & (df['Deposit Amt.'] != '')]
            if not deposits.empty:
                print(f"  存款交易: {len(deposits)} 笔")

        # 余额统计
        if 'Closing Balance' in df.columns:
            balances = df[df['Closing Balance'].notna() & (df['Closing Balance'] != '')]
            if not balances.empty:
                print(f"  余额记录: {len(balances)} 条")

    def _generate_quality_report(self, df: pd.DataFrame) -> None:
        """生成数据质量报告"""
        print(f"\n🔍 数据质量检查:")

        # 检查必填字段
        required_fields = ['Date', 'Particulars']
        for field in required_fields:
            if field in df.columns:
                missing_count = df[field].isna().sum() + (df[field] == '').sum()
                if missing_count > 0:
                    print(f"  ⚠️ {field} 缺失: {missing_count} 行")
                else:
                    print(f"  ✅ {field} 完整")

        # 检查描述质量
        if 'Particulars' in df.columns:
            short_descriptions = (df['Particulars'].str.len() < 5).sum()
            if short_descriptions > 0:
                print(f"  ⚠️ 描述过短: {short_descriptions} 行")
            else:
                print(f"  ✅ 描述质量良好")


def main():
    """主函数 - 使用示例"""
    parser = ICICIPDFParser()

    # 示例PDF文件路径（需要根据实际情况修改）
    pdf_path = "files/3-icici-*********-*********-ICICI-BANK-STATEMENT-1-1.pdf"

    print("🚀 启动ICICI银行PDF解析器")

    # 解析PDF
    df = parser.parse_icici_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        csv_file, json_file, excel_file = parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 ICICI银行PDF解析完成！")
        print(f"📁 输出文件:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")
        return df
    else:
        print(f"\n❌ ICICI银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
