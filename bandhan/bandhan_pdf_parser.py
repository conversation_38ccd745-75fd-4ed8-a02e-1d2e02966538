#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bandhan银行对账单PDF解析器
基于分析结果，Bandhan PDF格式规范，使用tabula lattice模式即可很好解析
"""

import os
import pandas as pd
import re
import json
import tabula
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class BandhanPDFParser:
    """Bandhan银行PDF解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Date', 'Description', 'Debit/Credit', 'Amount', 'Balance'
        ]
        
    def parse_bandhan_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析Bandhan银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"Bandhan银行PDF解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 使用Tabula lattice模式提取表格数据
            print("\n🔄 使用Tabula lattice模式提取表格数据...")
            df_final = self._extract_tabula_data(pdf_path)
            
            if df_final.empty:
                print("❌ Tabula提取失败")
                return pd.DataFrame()
            
            # 数据验证和清理
            print("\n🔄 数据验证和清理...")
            df_final = self._validate_and_clean_data(df_final)
            
            print(f"\n✅ Bandhan银行PDF解析完成！提取交易数: {len(df_final)} 条")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_tabula_data(self, pdf_path: str) -> pd.DataFrame:
        """使用Tabula提取表格数据"""
        try:
            # 使用lattice=True模式提取表格
            dfs = tabula.read_pdf(pdf_path, pages='all', lattice=True)
            print(f"  📋 Tabula找到 {len(dfs)} 个表格")

            if not dfs:
                print("  ❌ 未找到任何表格")
                return pd.DataFrame()

            # 合并所有有效的交易表格
            all_transactions = []

            for i, df in enumerate(dfs):
                print(f"  📄 处理表格 {i+1}: 形状 {df.shape}")

                # 清理表格数据
                df_cleaned = self._clean_tabula_table(df, i+1)

                if not df_cleaned.empty:
                    all_transactions.append(df_cleaned)
                    print(f"    ✅ 提取到 {len(df_cleaned)} 条有效交易")
                else:
                    print(f"    ⚠️ 表格 {i+1} 没有有效数据")

            if not all_transactions:
                print("  ❌ 所有表格都没有有效数据")
                return pd.DataFrame()

            # 合并所有交易数据
            df_combined = pd.concat(all_transactions, ignore_index=True)
            print(f"  ✅ 合并后总计 {len(df_combined)} 条交易")

            return df_combined

        except Exception as e:
            print(f"  ❌ Tabula提取失败: {e}")
            return pd.DataFrame()

    def _clean_tabula_table(self, df: pd.DataFrame, table_num: int) -> pd.DataFrame:
        """清理单个tabula表格"""
        if df.empty:
            return pd.DataFrame()

        # 检查是否为有效的交易表格（必须包含Date, Description等列）
        if not self._is_valid_transaction_table(df):
            print(f"    ⚠️ 表格 {table_num} 不是有效的交易表格")
            return pd.DataFrame()

        # 标准化列名
        df = self._standardize_columns(df)
        
        # 移除空行和无效行
        df = df.dropna(how='all')

        # 过滤有效的交易行
        valid_rows = []
        for i, row in df.iterrows():
            if self._is_valid_transaction_row(row):
                valid_rows.append(row)

        if valid_rows:
            df_valid = pd.DataFrame(valid_rows)
            df_valid.reset_index(drop=True, inplace=True)
            return df_valid
        else:
            return pd.DataFrame()

    def _is_valid_transaction_table(self, df: pd.DataFrame) -> bool:
        """检查是否为有效的交易表格"""
        if df.empty:
            return False
        
        # 检查列数（应该有5列：Date, Description, Debit/Credit, Amount, Balance）
        if len(df.columns) < 5:
            return False
        
        # 检查列名是否包含关键字段
        columns_str = ' '.join(str(col) for col in df.columns).upper()
        required_keywords = ['DATE', 'DESCRIPTION', 'AMOUNT', 'BALANCE']
        
        return all(keyword in columns_str for keyword in required_keywords)

    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        if df.empty:
            return df

        # 确保有5列
        while len(df.columns) < 5:
            df[f'Col_{len(df.columns)}'] = None

        # 设置标准列名
        df.columns = self.expected_columns

        return df

    def _is_valid_transaction_row(self, row) -> bool:
        """检查是否为有效的交易行"""
        # 检查日期列是否有效
        date_value = row['Date'] if 'Date' in row.index else row.iloc[0]
        if not self._is_valid_date(date_value):
            return False
        
        # 检查是否有金额或余额
        amount_value = row['Amount'] if 'Amount' in row.index else row.iloc[3]
        balance_value = row['Balance'] if 'Balance' in row.index else row.iloc[4]
        
        return (pd.notna(amount_value) and str(amount_value).strip() != '') or \
               (pd.notna(balance_value) and str(balance_value).strip() != '')

    def _is_valid_date(self, value) -> bool:
        """检查是否为有效的日期格式"""
        if pd.isna(value):
            return False

        date_str = str(value).strip()
        # Bandhan日期格式: DD/MM/YYYY
        return bool(re.match(r'\d{2}/\d{2}/\d{4}', date_str))

    def _validate_and_clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清理最终数据"""
        if df.empty:
            return df

        print("  🧹 清洗数据...")

        # 清洗日期列
        df = self._clean_date_column(df)
        
        # 清洗金额列
        df = self._clean_amount_columns(df)
        
        # 清洗描述列
        df = self._clean_description_column(df)

        # 验证数据完整性
        df = self._validate_data_integrity(df)

        return df

    def _clean_date_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗日期列"""
        if 'Date' in df.columns:
            print("    📅 清洗日期列...")
            df['Date'] = df['Date'].apply(self._parse_date)

        return df

    def _parse_date(self, value) -> str:
        """解析日期字符串"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()

        # Bandhan日期格式: DD/MM/YYYY
        if re.match(r'\d{2}/\d{2}/\d{4}', date_str):
            return date_str

        # 尝试其他常见格式
        try:
            for fmt in ['%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%d/%m/%Y')  # 统一为Bandhan格式
                except ValueError:
                    continue
        except:
            pass

        return date_str  # 如果无法解析，返回原值

    def _clean_amount_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗金额列数据"""
        amount_columns = ['Amount', 'Balance']

        for col in amount_columns:
            if col in df.columns:
                print(f"    💰 清洗 {col} 列...")
                df[col] = df[col].apply(self._parse_amount)

        return df

    def _parse_amount(self, value) -> Optional[float]:
        """解析金额字符串为浮点数"""
        if pd.isna(value) or value == '' or str(value).strip() == '':
            return None

        try:
            # 转换为字符串并清理
            amount_str = str(value).strip()

            # 移除货币符号和空格
            amount_str = re.sub(r'[₹\s]', '', amount_str)

            # 移除逗号分隔符
            amount_str = re.sub(r',', '', amount_str)

            # 移除括号（负数表示）
            is_negative = '(' in amount_str and ')' in amount_str
            amount_str = re.sub(r'[()]', '', amount_str)

            # 只保留数字和小数点
            amount_str = re.sub(r'[^\d.-]', '', amount_str)

            if not amount_str or amount_str == '-':
                return None

            # 转换为浮点数
            amount = float(amount_str)

            # 处理负数
            if is_negative:
                amount = -amount

            return amount

        except (ValueError, TypeError):
            return None

    def _clean_description_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗描述列"""
        if 'Description' in df.columns:
            print("    📝 清洗描述列...")
            df['Description'] = df['Description'].apply(self._clean_description_text)

        return df

    def _clean_description_text(self, text) -> str:
        """清理描述文本"""
        if pd.isna(text):
            return ""

        text_str = str(text).strip()

        # 移除换行符，用空格替换
        text_str = re.sub(r'\n+', ' ', text_str)
        text_str = re.sub(r'\r+', ' ', text_str)
        text_str = re.sub(r'\t+', ' ', text_str)

        # 清理多余的空格
        text_str = re.sub(r'\s+', ' ', text_str)

        return text_str.strip()

    def _validate_data_integrity(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据完整性"""
        print("    ✅ 验证数据完整性...")

        # 移除没有日期的行
        before_count = len(df)
        df = df[df['Date'].notna() & (df['Date'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效日期的记录")

        # 确保至少有金额或余额
        df = df[
            df['Amount'].notna() | df['Balance'].notna()
        ]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "bandhan_extracted") -> Tuple[str, str, str]:
        """
        保存解析结果为多种格式

        Args:
            df: 解析后的DataFrame
            output_base: 输出文件名前缀

        Returns:
            Tuple[str, str, str]: CSV、JSON、Excel文件路径
        """
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 在保存前进行最终的文本清理
        df_clean = df.copy()
        if 'Description' in df_clean.columns:
            df_clean['Description'] = df_clean['Description'].apply(
                lambda x: self._clean_description_text(str(x)) if pd.notna(x) else ""
            )

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df_clean.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df_clean.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df_clean.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 Bandhan银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 交易类型统计
        if 'Debit/Credit' in df.columns:
            debit_count = df[df['Debit/Credit'].str.upper() == 'DR'].shape[0] if 'Debit/Credit' in df.columns else 0
            credit_count = df[df['Debit/Credit'].str.upper() == 'CR'].shape[0] if 'Debit/Credit' in df.columns else 0

            print(f"借记交易: {debit_count} 笔")
            print(f"贷记交易: {credit_count} 笔")

        # 金额统计
        if 'Amount' in df.columns:
            valid_amounts = df['Amount'].dropna()
            if not valid_amounts.empty:
                total_amount = valid_amounts.sum()
                avg_amount = valid_amounts.mean()
                max_amount = valid_amounts.max()
                min_amount = valid_amounts.min()

                print(f"\n💰 金额统计:")
                print(f"  总金额: ₹{total_amount:,.2f}")
                print(f"  平均金额: ₹{avg_amount:,.2f}")
                print(f"  最大金额: ₹{max_amount:,.2f}")
                print(f"  最小金额: ₹{min_amount:,.2f}")

        # 数据完整性检查
        missing_dates = df['Date'].isna().sum() if 'Date' in df.columns else 0
        missing_descriptions = df['Description'].isna().sum() if 'Description' in df.columns else 0
        missing_amounts = df['Amount'].isna().sum() if 'Amount' in df.columns else 0
        missing_balances = df['Balance'].isna().sum() if 'Balance' in df.columns else 0

        print(f"\n📋 数据完整性:")
        print(f"  缺失日期: {missing_dates} 条")
        print(f"  缺失描述: {missing_descriptions} 条")
        print(f"  缺失金额: {missing_amounts} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额连续性检查
        self._check_balance_continuity(df)

        # 日期范围
        if 'Date' in df.columns and not df['Date'].isna().all():
            valid_dates = df[df['Date'].notna() & (df['Date'] != '')]
            if not valid_dates.empty:
                print(f"\n📅 日期范围:")
                print(f"  最早交易: {valid_dates['Date'].iloc[0]}")
                print(f"  最晚交易: {valid_dates['Date'].iloc[-1]}")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            amount = f"₹{row['Amount']:,.2f}" if pd.notna(row['Amount']) else "-"
            balance = f"₹{row['Balance']:,.2f}" if pd.notna(row['Balance']) else "-"
            debit_credit = row['Debit/Credit'] if pd.notna(row['Debit/Credit']) else "-"

            print(f"  {i+1}. {row['Date']} | {debit_credit} | {str(row['Description'])[:30]}...")
            print(f"     金额: {amount} | 余额: {balance}")

    def _check_balance_continuity(self, df: pd.DataFrame) -> None:
        """检查余额连续性"""
        print(f"\n🔍 余额连续性检查:")

        if 'Balance' not in df.columns:
            print("  ⚠️ 没有余额列")
            return

        # 检查余额是否连续
        valid_balances = df[df['Balance'].notna()]

        if len(valid_balances) < 2:
            print("  ⚠️ 余额数据不足，无法检查连续性")
            return

        # 简单检查：余额应该是有变化的
        balance_changes = 0
        for i in range(1, len(valid_balances)):
            prev_balance = valid_balances.iloc[i-1]['Balance']
            curr_balance = valid_balances.iloc[i]['Balance']

            if abs(curr_balance - prev_balance) > 0.01:  # 忽略小数点误差
                balance_changes += 1

        print(f"  余额变化次数: {balance_changes}")

        # 检查首末余额
        first_balance = valid_balances.iloc[0]['Balance']
        last_balance = valid_balances.iloc[-1]['Balance']

        print(f"  期初余额: ₹{first_balance:,.2f}")
        print(f"  期末余额: ₹{last_balance:,.2f}")
        print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")


def main():
    """主函数"""
    parser = BandhanPDFParser()

    pdf_path = "../files/13-bandhan-*********-Statement-Bandhan-Bank.pdf"

    print("🚀 启动Bandhan银行PDF解析器")

    # 解析PDF
    df = parser.parse_bandhan_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 Bandhan银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ Bandhan银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
