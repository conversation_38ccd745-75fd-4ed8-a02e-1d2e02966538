#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bandhan银行PDF结构分析脚本
"""

import tabula
import pandas as pd
import pypdf
import os

def analyze_bandhan_pdf():
    """分析Bandhan银行PDF的结构"""
    pdf_path = '../files/13-bandhan-*********-Statement-Bandhan-Bank.pdf'
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return
    
    print('=' * 80)
    print('Bandhan银行PDF结构分析')
    print('=' * 80)
    
    # 1. 使用tabula lattice模式分析
    print('\n🔍 1. Tabula Lattice模式分析:')
    try:
        dfs = tabula.read_pdf(pdf_path, pages='all', lattice=True)
        print(f'   找到 {len(dfs)} 个表格')
        
        for i, df in enumerate(dfs):
            print(f'\n   表格 {i+1}: 形状 {df.shape}')
            print(f'   列名: {list(df.columns)}')
            if not df.empty:
                print('   前3行数据:')
                print(df.head(3).to_string(index=False))
                print('   ---')
    except Exception as e:
        print(f'   ❌ Lattice模式解析出错: {e}')
    
    # 2. 使用tabula stream模式分析
    print('\n🔍 2. Tabula Stream模式分析:')
    try:
        dfs_stream = tabula.read_pdf(pdf_path, pages='all', stream=True)
        print(f'   找到 {len(dfs_stream)} 个表格')
        
        for i, df in enumerate(dfs_stream):
            print(f'\n   表格 {i+1}: 形状 {df.shape}')
            print(f'   列名: {list(df.columns)}')
            if not df.empty:
                print('   前3行数据:')
                print(df.head(3).to_string(index=False))
                print('   ---')
    except Exception as e:
        print(f'   ❌ Stream模式解析出错: {e}')
    
    # 3. 使用pypdf分析文本内容
    print('\n🔍 3. PyPDF文本内容分析:')
    try:
        with open(pdf_path, 'rb') as file:
            reader = pypdf.PdfReader(file)
            print(f'   PDF总页数: {len(reader.pages)}')
            
            for page_num in range(min(2, len(reader.pages))):  # 只分析前2页
                page = reader.pages[page_num]
                text = page.extract_text()
                
                print(f'\n   第 {page_num + 1} 页文本内容 (前500字符):')
                print('   ' + '-' * 50)
                print('   ' + text[:500].replace('\n', '\n   '))
                print('   ' + '-' * 50)
                
                # 查找可能的表格标题行
                lines = text.split('\n')
                for i, line in enumerate(lines):
                    if any(keyword in line.upper() for keyword in ['DATE', 'DESCRIPTION', 'DEBIT', 'CREDIT', 'BALANCE']):
                        print(f'   可能的表头行 {i}: {line.strip()}')
                        
    except Exception as e:
        print(f'   ❌ PyPDF分析出错: {e}')
    
    # 4. 尝试不同的tabula参数组合
    print('\n🔍 4. 尝试不同的Tabula参数组合:')
    
    # 参数组合列表
    param_combinations = [
        {'lattice': True, 'pages': 'all'},
        {'stream': True, 'pages': 'all'},
        {'lattice': True, 'pages': '1'},
        {'stream': True, 'pages': '1'},
        {'lattice': True, 'pages': 'all', 'multiple_tables': True},
        {'stream': True, 'pages': 'all', 'multiple_tables': True},
    ]
    
    for i, params in enumerate(param_combinations):
        try:
            print(f'\n   组合 {i+1}: {params}')
            dfs = tabula.read_pdf(pdf_path, **params)
            print(f'   结果: 找到 {len(dfs)} 个表格')
            
            if dfs and not dfs[0].empty:
                df = dfs[0]
                print(f'   第一个表格形状: {df.shape}')
                print(f'   列名: {list(df.columns)}')
                
                # 检查数据质量
                non_empty_rows = df.dropna(how='all')
                print(f'   非空行数: {len(non_empty_rows)}')
                
        except Exception as e:
            print(f'   ❌ 组合 {i+1} 失败: {e}')
    
    print('\n✅ Bandhan银行PDF结构分析完成!')

if __name__ == "__main__":
    analyze_bandhan_pdf()
