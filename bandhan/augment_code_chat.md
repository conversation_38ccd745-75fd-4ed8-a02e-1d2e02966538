你先理解一下该项目，并仔细分析下@boi 目录，然后参考 boi 实现的解析策略(结合 tabula 和 pypdf 的优势来实现,混合策略)帮我实现 bandhan 银行账单(@files/13-bandhan-*********-Statement-Bandhan-Bank.pdf)的解析
1、要求在 bandhan 目录实现，在该目录下生成 bandhan_extracted.csv bandhan_extracted.json bandhan_extracted.xlsx 三个文件，并生成解析报告
2、要保留 bandban PDF 银行账单里面原始的表格格式，你只是参考 boi 的实现策略和方法
3、你不能随意给我删除列，我要保留 PDF 中原有的所有列和列名
4、如果你觉得格式比较规范用 tabula 就可以实现，你可以单独使用 tabula 就好，不用结合两者
5、因为 bandhan 目前没有可对比的文件，所以你先不用实现验证逻辑
