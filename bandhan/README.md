# Bandhan银行PDF账单解析器

## 项目概述

本项目实现了Bandhan银行PDF账单的自动解析功能，能够将PDF格式的银行对账单转换为结构化数据（CSV、JSON、Excel格式）。

## 解析策略

### 技术选择
基于对Bandhan银行PDF格式的深入分析，我们发现：
- PDF格式相对规范，表格结构清晰
- 使用tabula的lattice模式即可很好地解析表格数据
- 无需使用复杂的混合解析策略（tabula + pypdf）

### 解析流程
1. **PDF结构分析**: 使用tabula分析PDF中的表格结构
2. **数据提取**: 使用tabula lattice模式提取所有页面的交易数据
3. **数据清理**: 标准化列名、清洗金额和日期格式
4. **数据验证**: 检查数据完整性和一致性
5. **多格式输出**: 生成CSV、JSON、Excel三种格式的文件

## 解析结果

### 数据统计
- **总交易数**: 97 条
- **借记交易**: 41 笔
- **贷记交易**: 56 笔
- **日期范围**: 2020年4月7日 - 2021年3月31日

### 金额统计
- **总金额**: ₹1,554,018.89
- **平均金额**: ₹16,020.81
- **最大金额**: ₹500,000.00
- **最小金额**: ₹1.10

### 余额信息
- **期初余额**: ₹129,818.74
- **期末余额**: ₹35,765.43
- **余额变化**: ₹-94,053.31

### 数据完整性
- ✅ **缺失日期**: 0 条
- ✅ **缺失描述**: 0 条
- ✅ **缺失金额**: 0 条
- ✅ **缺失余额**: 0 条

## 输出文件

### 1. bandhan_extracted.csv
标准CSV格式，包含以下列：
- `Date`: 交易日期 (DD/MM/YYYY格式)
- `Description`: 交易描述
- `Debit/Credit`: 借贷标识 (Dr/Cr)
- `Amount`: 交易金额
- `Balance`: 账户余额

### 2. bandhan_extracted.json
JSON格式，便于程序化处理和API集成

### 3. bandhan_extracted.xlsx
Excel格式，便于人工查看和进一步分析

## 技术实现

### 核心依赖
- `tabula-py`: PDF表格提取
- `pandas`: 数据处理和分析
- `openpyxl`: Excel文件生成

### 关键特性
1. **智能表格识别**: 自动识别有效的交易表格
2. **数据类型转换**: 自动处理金额和日期格式
3. **错误处理**: 完善的异常处理机制
4. **数据验证**: 多层次的数据完整性检查

## 使用方法

```bash
# 进入bandhan目录
cd bandhan

# 运行解析器
python3 bandhan_pdf_parser.py
```

## 文件结构

```
bandhan/
├── README.md                    # 本文档
├── bandhan_pdf_parser.py        # 主解析器
├── analyze_bandhan_pdf.py       # PDF结构分析脚本
├── bandhan_extracted.csv        # CSV输出文件
├── bandhan_extracted.json       # JSON输出文件
└── bandhan_extracted.xlsx       # Excel输出文件
```

## 解析质量评估

### 优势
1. **高准确性**: 100%的数据完整性，无缺失字段
2. **格式标准**: 统一的日期和金额格式
3. **多格式支持**: 提供三种常用的数据格式
4. **自动化程度高**: 无需人工干预即可完成解析

### 技术亮点
1. **简化策略**: 基于PDF格式特点选择最适合的解析方法
2. **数据清洗**: 完善的数据清理和标准化流程
3. **错误处理**: 健壮的异常处理机制
4. **可扩展性**: 易于扩展到其他类似格式的银行PDF

## 对比分析

与项目中其他银行解析器相比：
- **BOI银行**: 需要混合解析策略（tabula + pypdf）
- **Bandhan银行**: 单一tabula策略即可满足需求
- **解析效率**: Bandhan解析器更简洁高效
- **维护成本**: 更低的复杂度，更易维护

## 总结

Bandhan银行PDF解析器成功实现了以下目标：
1. ✅ 完整解析PDF中的所有交易记录
2. ✅ 保持原始表格的所有列和数据
3. ✅ 生成三种格式的输出文件
4. ✅ 提供详细的解析质量报告
5. ✅ 实现高度自动化的解析流程

该解析器为Bandhan银行PDF账单的数字化处理提供了可靠的技术解决方案。
