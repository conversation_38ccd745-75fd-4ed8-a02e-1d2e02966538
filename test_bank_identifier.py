#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
银行PDF账单自动识别系统测试脚本
"""

import os
import sys
import time
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bank_identifier import BankIdentifier, BankType


def main():
    """主测试函数"""
    print("🚀 启动银行PDF账单自动识别系统测试")
    print("=" * 80)
    
    # 检查files目录
    files_dir = "files"
    if not os.path.exists(files_dir):
        print(f"❌ 测试目录不存在: {files_dir}")
        return
    
    # 获取所有PDF文件
    pdf_files = []
    for file_path in Path(files_dir).glob("*.pdf"):
        pdf_files.append(str(file_path))
    
    if not pdf_files:
        print(f"❌ 在 {files_dir} 目录中未找到PDF文件")
        return
    
    print(f"📁 找到 {len(pdf_files)} 个PDF文件")
    
    # 创建银行识别器
    identifier = BankIdentifier()
    
    # 执行批量识别
    print("\n🔍 开始批量识别...")
    start_time = time.time()
    
    results = identifier.batch_identify(sorted(pdf_files))
    
    total_time = time.time() - start_time
    
    # 生成报告
    print("\n📊 生成识别报告...")
    report = identifier.generate_identification_report(results)
    
    # 显示报告
    print(report)
    
    # 保存报告到文件
    report_file = "bank_identification_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
        f.write(f"\n\n总处理时间: {total_time:.2f}秒")
        f.write(f"\n平均处理时间: {total_time/len(pdf_files):.2f}秒/文件")
    
    print(f"\n💾 报告已保存到: {report_file}")
    
    # 显示性能统计
    print(f"\n⏱️ 性能统计:")
    print(f"  总处理时间: {total_time:.2f}秒")
    print(f"  平均处理时间: {total_time/len(pdf_files):.2f}秒/文件")
    
    # 分析准确率（基于文件名中的银行标识）
    print(f"\n🎯 准确率分析（基于文件名）:")
    analyze_accuracy_by_filename(results)
    
    print(f"\n🎉 银行PDF账单自动识别测试完成！")


def analyze_accuracy_by_filename(results):
    """基于文件名分析识别准确率"""
    # 文件名到银行类型的映射
    filename_bank_mapping = {
        'sbi': BankType.SBI,
        'hdfc': BankType.HDFC,
        'icici': BankType.ICICI,
        'kotak': BankType.KOTAK,
        'boi': BankType.BOI,
        'iob': BankType.IOB,
        'pnb': BankType.PNB,
        'canara': BankType.CANARA,
        'ubi': BankType.UBI,
        'bob': BankType.BOB,
        'cbi': BankType.CBI,
        'indian': BankType.INDIAN,
        'uco': BankType.UCO,
        'yes': BankType.YES,
        'federal': BankType.FEDERAL,
        'bandhan': BankType.BANDHAN,
        'idbi': BankType.IDBI,
        'indusind': BankType.INDUSIND,
        'sib': BankType.SIB
    }
    
    correct_predictions = 0
    total_predictions = 0
    detailed_results = []
    
    for pdf_path, result in results.items():
        filename = os.path.basename(pdf_path).lower()
        
        # 从文件名中推断银行类型
        expected_bank = None
        for bank_key, bank_type in filename_bank_mapping.items():
            if bank_key in filename:
                expected_bank = bank_type
                break
        
        if expected_bank is None:
            continue  # 跳过无法从文件名推断银行的文件
        
        total_predictions += 1
        predicted_bank = result.identified_bank
        
        is_correct = (predicted_bank == expected_bank)
        if is_correct:
            correct_predictions += 1
        
        detailed_results.append({
            'filename': os.path.basename(pdf_path),
            'expected': expected_bank.value,
            'predicted': predicted_bank.value,
            'confidence': result.confidence_score,
            'correct': is_correct
        })
    
    # 计算准确率
    if total_predictions > 0:
        accuracy = (correct_predictions / total_predictions) * 100
        print(f"  总测试文件: {total_predictions}")
        print(f"  正确识别: {correct_predictions}")
        print(f"  识别准确率: {accuracy:.1f}%")
        
        # 显示错误案例
        print(f"\n❌ 识别错误的案例:")
        for result in detailed_results:
            if not result['correct']:
                print(f"  文件: {result['filename']}")
                print(f"    期望: {result['expected']}")
                print(f"    识别: {result['predicted']}")
                print(f"    置信度: {result['confidence']:.3f}")
        
        # 显示高置信度正确案例
        print(f"\n✅ 高置信度正确识别案例:")
        correct_cases = [r for r in detailed_results if r['correct'] and r['confidence'] > 0.8]
        for result in sorted(correct_cases, key=lambda x: x['confidence'], reverse=True)[:5]:
            print(f"  文件: {result['filename']}")
            print(f"    银行: {result['predicted']}")
            print(f"    置信度: {result['confidence']:.3f}")
    else:
        print("  无法从文件名推断银行类型，跳过准确率分析")


def test_single_file():
    """测试单个文件的识别"""
    print("\n🔍 单文件测试模式")
    
    # 选择一个测试文件
    test_files = [
        "files/1-sbi-**********-recent-sbi-statement.pdf",
        "files/2-hdfc-*********-HDFC-BANK-STATEMENT-pdf.pdf",
        "files/3-icici-*********-*********-ICICI-BANK-STATEMENT-1-1.pdf"
    ]
    
    identifier = BankIdentifier()
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n测试文件: {os.path.basename(test_file)}")
            
            result = identifier.identify_bank(test_file)
            
            print(f"识别结果: {result.identified_bank.value}")
            print(f"置信度: {result.confidence_score:.3f}")
            print(f"处理时间: {result.processing_time:.2f}秒")
            print(f"提取方法: {', '.join(result.extraction_methods_used)}")
            
            if result.all_matches:
                print("所有匹配结果:")
                for i, match in enumerate(result.all_matches[:3], 1):
                    print(f"  {i}. {match.bank_type.value}: {match.confidence:.3f}")
                    print(f"     匹配特征: {', '.join(match.matched_features)}")
            
            if result.error_message:
                print(f"错误信息: {result.error_message}")
            
            print("-" * 40)


if __name__ == "__main__":
    # 检查是否有命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--single":
        test_single_file()
    else:
        main()
