#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UBI (Union Bank of India) 银行对账单PDF解析器
基于PNB银行解析器的成功模式，针对UBI银行的8列表格结构优化
采用Tabula主导的解析策略
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class UBIBankPDFParser:
    """UBI银行PDF解析器 - Tabula主导策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Tran Id', 'Txn Date', 'Cheque No', 'Description', 'Currency', 'Cr/Dr', 'Amount', 'Balance'
        ]
        # 不再使用标准化列名，保持原始PDF列结构
        
    def parse_ubi_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析UBI银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"UBI银行PDF解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：使用Tabula提取表格数据（UBI表格结构良好）
            print("\n🔄 第一步：使用Tabula提取表格数据...")
            df_tabula = self._extract_tabula_data(pdf_path)
            
            if df_tabula.empty:
                print("❌ Tabula提取失败，尝试PyPDF备选方案...")
                # 备选方案：使用PyPDF
                df_pypdf = self._extract_pypdf_data(pdf_path)
                if df_pypdf.empty:
                    print("❌ PyPDF提取也失败")
                    return pd.DataFrame()
                df_final = df_pypdf
            else:
                df_final = df_tabula
            
            # 第二步：数据清洗和标准化
            print("\n🔄 第二步：数据清洗和标准化...")
            df_final = self._clean_and_standardize_data(df_final)
            
            # 第三步：数据验证
            print("\n🔄 第三步：数据验证...")
            df_final = self._validate_data(df_final)
            
            print(f"\n✅ UBI银行PDF解析完成！提取交易数: {len(df_final)} 条")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_tabula_data(self, pdf_path: str) -> pd.DataFrame:
        """使用Tabula提取表格数据"""
        try:
            # UBI银行表格结构良好，使用lattice模式
            print("  📋 使用Tabula lattice模式提取表格...")
            dfs = tabula.read_pdf(pdf_path, pages='all', lattice=True)
            print(f"  📄 找到 {len(dfs)} 个表格")

            if not dfs:
                print("  ❌ 未找到任何表格")
                return pd.DataFrame()

            # 合并所有页面的交易表格
            all_transactions = []

            for i, df in enumerate(dfs):
                print(f"  📊 处理表格 {i+1}: 形状 {df.shape}")

                # 只处理交易表格（8列的表格）
                if len(df.columns) == 8:
                    # 清理表格数据
                    df_cleaned = self._clean_tabula_table(df, i+1)

                    if not df_cleaned.empty:
                        all_transactions.append(df_cleaned)
                        print(f"    ✅ 提取到 {len(df_cleaned)} 条有效交易")
                    else:
                        print(f"    ⚠️ 表格 {i+1} 没有有效数据")
                else:
                    print(f"    ⚠️ 表格 {i+1} 不是交易表格（{len(df.columns)}列）")

            if not all_transactions:
                print("  ❌ 所有表格都没有有效数据")
                return pd.DataFrame()

            # 合并所有交易数据
            df_combined = pd.concat(all_transactions, ignore_index=True)
            print(f"  ✅ 合并后总计 {len(df_combined)} 条交易")

            return df_combined

        except Exception as e:
            print(f"  ❌ Tabula提取失败: {e}")
            return pd.DataFrame()

    def _clean_tabula_table(self, df: pd.DataFrame, table_num: int) -> pd.DataFrame:
        """清理单个tabula表格"""
        if df.empty:
            return pd.DataFrame()

        # 保持原始列名，不强制修改
        print(f"    📋 原始列名: {list(df.columns)}")

        # 移除空行
        df = df.dropna(how='all')

        # 过滤有效的交易行
        valid_rows = []
        for i, row in df.iterrows():
            # 动态检查列位置（根据实际列名）
            tran_id = None
            txn_date = None
            amount = None

            # 根据列名找到对应的值
            if 'Tran Id' in df.columns:
                tran_id = row['Tran Id']
            elif len(row) > 0:
                tran_id = row.iloc[0]

            if 'Txn Date' in df.columns:
                txn_date = row['Txn Date']
            elif len(row) > 1:
                txn_date = row.iloc[1]

            if 'Amount' in df.columns:
                amount = row['Amount']
            elif len(row) > 6:
                amount = row.iloc[6]

            # 验证是否为有效交易行
            if (pd.notna(tran_id) and str(tran_id).strip() and
                pd.notna(txn_date) and self._is_valid_date(txn_date) and
                pd.notna(amount) and str(amount).strip()):
                valid_rows.append(row)

        if valid_rows:
            df_valid = pd.DataFrame(valid_rows)
            df_valid.reset_index(drop=True, inplace=True)
            return df_valid
        else:
            return pd.DataFrame()

    def _is_valid_date(self, value) -> bool:
        """检查是否为有效的日期格式"""
        if pd.isna(value):
            return False

        date_str = str(value).strip()
        # UBI银行日期格式: DD/MM/YYYY
        return bool(re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_str))

    def _extract_pypdf_data(self, pdf_path: str) -> pd.DataFrame:
        """使用PyPDF提取数据（备选方案）"""
        try:
            print("  📖 使用PyPDF提取文本数据...")
            
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                print(f"  📄 读取 {len(reader.pages)} 页")

                all_transactions = []

                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    text = page.extract_text()

                    print(f"  📄 处理第 {page_num + 1} 页...")
                    page_transactions = self._parse_pypdf_page(text, page_num + 1)

                    if page_transactions:
                        all_transactions.extend(page_transactions)
                        print(f"    ✅ 提取到 {len(page_transactions)} 条交易")

                if all_transactions:
                    df = pd.DataFrame(all_transactions)
                    print(f"  ✅ PyPDF总计提取 {len(df)} 条交易")
                    return df
                else:
                    print("  ❌ PyPDF未提取到任何交易")
                    return pd.DataFrame()

        except Exception as e:
            print(f"  ❌ PyPDF提取失败: {e}")
            return pd.DataFrame()

    def _parse_pypdf_page(self, text: str, page_num: int) -> List[Dict]:
        """解析PyPDF页面文本"""
        transactions = []
        lines = text.split('\n')

        # UBI银行的文本格式：每个交易分布在多行
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if not line:
                i += 1
                continue

            # 查找交易ID行（以S开头的数字）
            if re.match(r'^S\d+$', line):
                transaction = self._parse_transaction_block(lines, i)
                if transaction:
                    transactions.append(transaction)
                    i += 8  # 跳过已处理的行
                else:
                    i += 1
            else:
                i += 1

        return transactions

    def _parse_transaction_block(self, lines: List[str], start_idx: int) -> Optional[Dict]:
        """解析交易块（8行数据）"""
        try:
            if start_idx + 7 >= len(lines):
                return None

            tran_id = lines[start_idx].strip()
            txn_date = lines[start_idx + 1].strip()
            cheque_no = lines[start_idx + 2].strip() if lines[start_idx + 2].strip() else None
            description = lines[start_idx + 3].strip()
            currency = lines[start_idx + 4].strip()
            cr_dr = lines[start_idx + 5].strip()
            amount = lines[start_idx + 6].strip()
            balance = lines[start_idx + 7].strip()

            # 验证数据有效性
            if not self._is_valid_date(txn_date) or not amount:
                return None

            return {
                'Tran Id': tran_id,
                'Txn Date': txn_date,
                'Cheque No': cheque_no,
                'Description': description,
                'Currency': currency,
                'Cr/Dr': cr_dr,
                'Amount': amount,
                'Balance': balance
            }

        except Exception:
            return None

    def _clean_and_standardize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数据，保持原始列结构"""
        if df.empty:
            return df

        print("  🧹 清洗数据（保持原始列结构）...")
        print(f"    📋 当前列名: {list(df.columns)}")

        # 清洗各列数据，但保持原始列名
        df = self._clean_date_column(df)
        df = self._clean_amount_column(df)
        df = self._clean_balance_column(df)
        df = self._clean_description_column(df)

        # 清洗其他列
        df = self._clean_other_columns(df)

        return df

    def _clean_date_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗日期列"""
        if 'Txn Date' in df.columns:
            print("    📅 清洗Txn Date列...")
            df['Txn Date'] = df['Txn Date'].apply(self._parse_date)
        return df

    def _parse_date(self, value) -> str:
        """解析日期字符串"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()

        # UBI银行日期格式: DD/MM/YYYY
        if re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_str):
            try:
                # 转换为标准格式 DD-MM-YYYY
                date_obj = datetime.strptime(date_str, '%d/%m/%Y')
                return date_obj.strftime('%d-%m-%Y')
            except ValueError:
                return date_str

        return date_str

    def _clean_amount_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗金额列"""
        if 'Amount' in df.columns:
            print("    💰 清洗金额列...")
            df['Amount'] = df['Amount'].apply(self._parse_amount)
        return df

    def _parse_amount(self, value) -> Optional[float]:
        """解析金额字符串为浮点数"""
        if pd.isna(value) or value == '' or str(value).strip() == '':
            return None

        try:
            # 转换为字符串并清理
            amount_str = str(value).strip()

            # 移除货币符号和空格
            amount_str = re.sub(r'[₹\s]', '', amount_str)

            # 移除逗号分隔符
            amount_str = re.sub(r',', '', amount_str)

            # 处理负数（可能用括号表示或负号）
            is_negative = '(' in amount_str and ')' in amount_str
            amount_str = re.sub(r'[()]', '', amount_str)

            # 只保留数字和小数点
            amount_str = re.sub(r'[^\d.]', '', amount_str)

            if not amount_str:
                return None

            # 转换为浮点数
            amount = float(amount_str)

            # 处理负数
            if is_negative:
                amount = -amount

            return amount

        except (ValueError, TypeError):
            return None

    def _clean_balance_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗余额列"""
        if 'Balance' in df.columns:
            print("    💳 清洗余额列...")
            df['Balance'] = df['Balance'].apply(self._parse_balance)
        return df

    def _parse_balance(self, value) -> Optional[float]:
        """解析余额字符串"""
        if pd.isna(value) or value == '':
            return None

        try:
            balance_str = str(value).strip()

            # 移除逗号
            balance_str = re.sub(r',', '', balance_str)

            # 处理负数
            is_negative = '(' in balance_str and ')' in balance_str
            balance_str = re.sub(r'[()]', '', balance_str)

            # 只保留数字和小数点
            balance_str = re.sub(r'[^\d.]', '', balance_str)

            if not balance_str:
                return None

            balance = float(balance_str)

            # 处理负数
            if is_negative:
                balance = -balance

            return balance

        except (ValueError, TypeError):
            return None

    def _clean_description_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗描述列"""
        if 'Description' in df.columns:
            print("    📝 清洗Description列...")
            df['Description'] = df['Description'].apply(self._clean_description_text)
        return df

    def _clean_description_text(self, value) -> str:
        """清洗描述文本"""
        if pd.isna(value):
            return ""

        text = str(value).strip()

        # 移除换行符，用空格替换
        text = re.sub(r'\n+', ' ', text)
        text = re.sub(r'\r+', ' ', text)
        text = re.sub(r'\t+', ' ', text)

        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def _clean_other_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗其他列"""
        # 清洗Tran Id列
        if 'Tran Id' in df.columns:
            print("    🆔 清洗Tran Id列...")
            df['Tran Id'] = df['Tran Id'].apply(lambda x: str(x).strip() if pd.notna(x) else "")

        # 清洗Cheque No列
        if 'Cheque No' in df.columns:
            print("    📄 清洗Cheque No列...")
            df['Cheque No'] = df['Cheque No'].apply(lambda x: str(x).strip() if pd.notna(x) else "")

        # 清洗Currency列
        if 'Currency' in df.columns:
            print("    💱 清洗Currency列...")
            df['Currency'] = df['Currency'].apply(lambda x: str(x).strip() if pd.notna(x) else "")

        # 清洗Cr/Dr列
        if 'Cr/Dr' in df.columns:
            print("    ⚖️ 清洗Cr/Dr列...")
            df['Cr/Dr'] = df['Cr/Dr'].apply(lambda x: str(x).strip().upper() if pd.notna(x) else "")

        return df

    def _process_cr_dr_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理Cr/Dr列，确定交易类型"""
        if 'Cr/Dr' in df.columns and 'Amount' in df.columns:
            print("    🔄 处理Cr/Dr标识...")

            # 创建存款和取款列
            df['Deposits'] = None
            df['Withdrawals'] = None

            for i, row in df.iterrows():
                cr_dr = str(row['Cr/Dr']).strip().upper()
                amount = row['Amount']

                if pd.notna(amount) and amount > 0:
                    if cr_dr == 'CR':
                        df.at[i, 'Deposits'] = amount
                    elif cr_dr == 'DR':
                        df.at[i, 'Withdrawals'] = amount

        return df

    def _validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据完整性"""
        if df.empty:
            return df

        print("    ✅ 验证数据完整性...")

        # 移除没有日期的行
        before_count = len(df)
        if 'Txn Date' in df.columns:
            df = df[df['Txn Date'].notna() & (df['Txn Date'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效日期的记录")

        # 确保至少有一个关键列有值
        if 'Amount' in df.columns and 'Balance' in df.columns:
            df = df[
                df['Amount'].notna() |
                df['Balance'].notna()
            ]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "ubi_extracted") -> Tuple[str, str, str]:
        """
        保存解析结果为多种格式

        Args:
            df: 解析后的DataFrame
            output_base: 输出文件名前缀

        Returns:
            Tuple[str, str, str]: CSV、JSON、Excel文件路径
        """
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 在保存前进行最终的文本清理
        df_clean = df.copy()
        if 'Description' in df_clean.columns:
            df_clean['Description'] = df_clean['Description'].apply(
                lambda x: self._clean_description_text(str(x)) if pd.notna(x) else ""
            )

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df_clean.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df_clean.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df_clean.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 UBI银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 金额统计（基于Cr/Dr标识）
        cr_count = 0
        dr_count = 0
        total_cr_amount = 0
        total_dr_amount = 0

        if 'Cr/Dr' in df.columns and 'Amount' in df.columns:
            for i, row in df.iterrows():
                cr_dr = str(row['Cr/Dr']).strip().upper()
                amount = row['Amount'] if pd.notna(row['Amount']) else 0

                if cr_dr == 'CR':
                    cr_count += 1
                    total_cr_amount += amount
                elif cr_dr == 'DR':
                    dr_count += 1
                    total_dr_amount += amount

        print(f"贷记交易(CR): {cr_count} 笔")
        print(f"借记交易(DR): {dr_count} 笔")

        print(f"\n💰 金额统计:")
        print(f"  总贷记金额: ₹{total_cr_amount:,.2f}")
        print(f"  总借记金额: ₹{total_dr_amount:,.2f}")
        print(f"  净变化: ₹{total_cr_amount - total_dr_amount:,.2f}")

        # 数据完整性检查
        missing_dates = df['Txn Date'].isna().sum() if 'Txn Date' in df.columns else 0
        missing_descriptions = df['Description'].isna().sum() if 'Description' in df.columns else 0
        missing_balances = df['Balance'].isna().sum() if 'Balance' in df.columns else 0

        print(f"\n📋 数据完整性:")
        print(f"  缺失日期: {missing_dates} 条")
        print(f"  缺失描述: {missing_descriptions} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额连续性检查
        self._check_balance_continuity(df)

        # 日期范围
        if 'Txn Date' in df.columns and not df['Txn Date'].isna().all():
            valid_dates = df[df['Txn Date'].notna() & (df['Txn Date'] != '')]
            if not valid_dates.empty:
                print(f"\n📅 日期范围:")
                print(f"  最早交易: {valid_dates['Txn Date'].iloc[0]}")
                print(f"  最晚交易: {valid_dates['Txn Date'].iloc[-1]}")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]

            # 动态获取列值
            date = row['Txn Date'] if 'Txn Date' in df.columns else "-"
            description = str(row['Description'])[:30] + "..." if 'Description' in df.columns else "-"
            cr_dr = row['Cr/Dr'] if 'Cr/Dr' in df.columns else "-"
            amount = f"₹{row['Amount']:,.2f}" if 'Amount' in df.columns and pd.notna(row['Amount']) else "-"
            balance = f"₹{row['Balance']:,.2f}" if 'Balance' in df.columns and pd.notna(row['Balance']) else "-"

            print(f"  {i+1}. {date} | {description}")
            print(f"     类型: {cr_dr} | 金额: {amount} | 余额: {balance}")

    def _check_balance_continuity(self, df: pd.DataFrame) -> None:
        """检查余额连续性"""
        print(f"\n🔍 余额连续性检查:")

        balance_col = 'Balance'
        if balance_col not in df.columns:
            print("  ⚠️ 没有余额列")
            return

        # 检查余额是否连续
        valid_balances = df[df[balance_col].notna()]

        if len(valid_balances) < 2:
            print("  ⚠️ 余额数据不足，无法检查连续性")
            return

        # 检查首末余额
        first_balance = valid_balances.iloc[0][balance_col]
        last_balance = valid_balances.iloc[-1][balance_col]

        print(f"  期初余额: ₹{first_balance:,.2f}")
        print(f"  期末余额: ₹{last_balance:,.2f}")
        print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")


def main():
    """主函数"""
    parser = UBIBankPDFParser()

    pdf_path = "../files/7-ubi-*********-UBI-FullStatement-5.pdf"

    print("🚀 启动UBI银行PDF解析器")

    # 解析PDF
    df = parser.parse_ubi_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 UBI银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ UBI银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
