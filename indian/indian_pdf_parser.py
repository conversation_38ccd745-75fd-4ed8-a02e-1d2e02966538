#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Indian Bank银行对账单PDF解析器
基于BOI的混合解析策略，使用Tran Id + Tran Date精确匹配，避免IOB中的日期匹配问题
"""

import os
import pandas as pd
import re
import json
import tabula
import pypdf
from datetime import datetime
from typing import List, Dict, Tuple, Optional


class IndianBankPDFParser:
    """Indian Bank银行PDF解析器 - 混合解析策略"""
    
    def __init__(self):
        """初始化解析器"""
        self.expected_columns = [
            'Tran Id', 'Tran Date', 'Remarks', 'Amount (Rs.)', 'Balance (Rs.)'
        ]
        
    def parse_indian_pdf(self, pdf_path: str) -> pd.DataFrame:
        """
        解析Indian Bank银行PDF的主要方法
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            pd.DataFrame: 解析后的交易数据
        """
        print(f"\n{'='*80}")
        print(f"Indian Bank银行PDF混合解析器")
        print(f"文件: {pdf_path}")
        print(f"{'='*80}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return pd.DataFrame()
        
        try:
            # 第一步：使用Tabula提取表格结构和金额数据
            print("\n🔄 第一步：使用Tabula提取表格结构...")
            df_structured = self._extract_tabula_structure(pdf_path)
            
            if df_structured.empty:
                print("❌ Tabula提取失败")
                return pd.DataFrame()
            
            # 第二步：使用PyPDF提取完整描述文本
            print("\n🔄 第二步：使用PyPDF提取完整描述文本...")
            pypdf_data = self._extract_pypdf_descriptions(pdf_path)
            
            if not pypdf_data:
                print("❌ PyPDF提取失败")
                return df_structured  # 返回tabula结果作为备选
            
            # 第三步：合并数据，修复Remarks列
            print("\n🔄 第三步：合并数据，修复Remarks列...")
            df_final = self._merge_and_fix_data(df_structured, pypdf_data)
            
            # 第四步：数据验证和清理
            print("\n🔄 第四步：数据验证和清理...")
            df_final = self._validate_and_clean_data(df_final)
            
            print(f"\n✅ Indian Bank银行PDF解析完成！提取交易数: {len(df_final)} 条")
            return df_final
            
        except Exception as e:
            print(f"❌ 解析过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()
    
    def _extract_tabula_structure(self, pdf_path: str) -> pd.DataFrame:
        """使用Tabula提取表格结构和金额数据"""
        try:
            # 使用stream=True模式提取表格（分析显示stream模式更好）
            dfs = tabula.read_pdf(pdf_path, pages='all', stream=True)
            print(f"  📋 Tabula找到 {len(dfs)} 个表格")

            if not dfs:
                print("  ❌ 未找到任何表格")
                return pd.DataFrame()

            # 合并所有页面的表格
            all_transactions = []

            for i, df in enumerate(dfs):
                print(f"  📄 处理表格 {i+1}: 形状 {df.shape}")

                # 清理表格数据
                df_cleaned = self._clean_tabula_table(df, i+1)

                if not df_cleaned.empty:
                    all_transactions.append(df_cleaned)
                    print(f"    ✅ 提取到 {len(df_cleaned)} 条有效交易")
                else:
                    print(f"    ⚠️ 表格 {i+1} 没有有效数据")

            if not all_transactions:
                print("  ❌ 所有表格都没有有效数据")
                return pd.DataFrame()

            # 标准化每个表格的列名后再合并
            standardized_transactions = []
            for df_trans in all_transactions:
                df_std = self._standardize_columns(df_trans)
                standardized_transactions.append(df_std)

            # 合并所有交易数据
            df_combined = pd.concat(standardized_transactions, ignore_index=True)
            print(f"  ✅ 合并后总计 {len(df_combined)} 条交易")

            return df_combined

        except Exception as e:
            print(f"  ❌ Tabula提取失败: {e}")
            return pd.DataFrame()

    def _clean_tabula_table(self, df: pd.DataFrame, table_num: int) -> pd.DataFrame:
        """清理单个tabula表格，合并跨行的描述"""
        if df.empty:
            return pd.DataFrame()

        # 检查是否为有效的交易表格（必须包含Tran Id, Tran Date等列）
        if not self._is_valid_transaction_table(df):
            print(f"    ⚠️ 表格 {table_num} 不是有效的交易表格")
            return pd.DataFrame()

        # 移除空行和无效行
        df = df.dropna(how='all')

        # 合并跨行的描述并过滤有效的交易行
        valid_rows = []
        i = 0
        while i < len(df):
            row = df.iloc[i]

            if self._is_valid_transaction_row(row):
                # 这是一个有效的交易行，检查下一行是否为描述延续
                complete_row = row.copy()

                # 查找可能的描述延续行
                j = i + 1
                while j < len(df):
                    next_row = df.iloc[j]

                    # 如果下一行是新的交易行，停止
                    if self._is_valid_transaction_row(next_row):
                        break

                    # 如果下一行是描述延续行，合并到当前行
                    if self._is_description_continuation_row(next_row):
                        current_remarks = str(complete_row.iloc[2]) if pd.notna(complete_row.iloc[2]) else ""
                        continuation_text = str(next_row.iloc[2]) if pd.notna(next_row.iloc[2]) else ""

                        if continuation_text and continuation_text != 'nan':
                            complete_row.iloc[2] = current_remarks + " " + continuation_text

                        j += 1
                    else:
                        break

                valid_rows.append(complete_row)
                i = j  # 跳过已处理的延续行
            else:
                i += 1

        if valid_rows:
            df_valid = pd.DataFrame(valid_rows)
            df_valid.reset_index(drop=True, inplace=True)
            return df_valid
        else:
            return pd.DataFrame()

    def _is_description_continuation_row(self, row) -> bool:
        """检查是否为描述延续行"""
        # 延续行的特征：
        # 1. Tran Id和Tran Date为空
        # 2. Remarks列有内容
        # 3. Amount和Balance为空

        tran_id = row.iloc[0] if len(row) > 0 else None
        tran_date = row.iloc[1] if len(row) > 1 else None
        remarks = row.iloc[2] if len(row) > 2 else None
        amount = row.iloc[3] if len(row) > 3 else None
        balance = row.iloc[4] if len(row) > 4 else None

        # Tran Id和Tran Date应该为空
        if pd.notna(tran_id) and str(tran_id).strip() != '' and str(tran_id) != 'nan':
            return False
        if pd.notna(tran_date) and str(tran_date).strip() != '' and str(tran_date) != 'nan':
            return False

        # Remarks应该有内容
        if pd.isna(remarks) or str(remarks).strip() == '' or str(remarks) == 'nan':
            return False

        # Amount和Balance应该为空
        if pd.notna(amount) and str(amount).strip() != '' and str(amount) != 'nan':
            return False
        if pd.notna(balance) and str(balance).strip() != '' and str(balance) != 'nan':
            return False

        return True

    def _is_valid_transaction_table(self, df: pd.DataFrame) -> bool:
        """检查是否为有效的交易表格"""
        if df.empty:
            return False
        
        # 检查列数（应该有5列：Tran Id, Tran Date, Remarks, Amount, Balance）
        if len(df.columns) < 5:
            return False
        
        # 检查列名是否包含关键字段
        columns_str = ' '.join(str(col) for col in df.columns).upper()
        required_keywords = ['TRAN', 'DATE', 'REMARKS', 'AMOUNT', 'BALANCE']
        
        return all(keyword in columns_str for keyword in required_keywords)

    def _is_valid_transaction_row(self, row) -> bool:
        """检查是否为有效的交易行"""
        # 检查Tran Id列是否有效（应该以S开头的数字）
        tran_id = row.iloc[0] if len(row) > 0 else None
        if not self._is_valid_tran_id(tran_id):
            return False
        
        # 检查日期列是否有效
        date_value = row.iloc[1] if len(row) > 1 else None
        if not self._is_valid_date(date_value):
            return False
        
        # 检查是否有金额或余额
        amount_value = row.iloc[3] if len(row) > 3 else None
        balance_value = row.iloc[4] if len(row) > 4 else None
        
        return (pd.notna(amount_value) and str(amount_value).strip() != '') or \
               (pd.notna(balance_value) and str(balance_value).strip() != '')

    def _is_valid_tran_id(self, value) -> bool:
        """检查是否为有效的交易ID格式"""
        if pd.isna(value):
            return False

        tran_id_str = str(value).strip()
        # Indian Bank交易ID格式: S开头 + 数字，或者AA开头 + 数字
        return bool(re.match(r'^(S|AA)\d+', tran_id_str))

    def _is_valid_date(self, value) -> bool:
        """检查是否为有效的日期格式"""
        if pd.isna(value):
            return False

        date_str = str(value).strip()
        # Indian Bank日期格式: DD/MM/YYYY
        return bool(re.match(r'\d{2}/\d{2}/\d{4}', date_str))

    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        if df.empty:
            return df

        # 确保有5列
        while len(df.columns) < 5:
            df[f'Col_{len(df.columns)}'] = None
        
        # 如果列数超过5列，只保留前5列
        if len(df.columns) > 5:
            df = df.iloc[:, :5]

        # 设置标准列名
        df.columns = self.expected_columns

        return df

    def _extract_pypdf_descriptions(self, pdf_path: str) -> List[Dict]:
        """使用PyPDF提取完整的描述文本"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = pypdf.PdfReader(file)
                print(f"  📖 PyPDF读取 {len(reader.pages)} 页")

                all_descriptions = []

                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    text = page.extract_text()

                    print(f"  📄 处理第 {page_num + 1} 页...")
                    page_descriptions = self._parse_page_text(text, page_num + 1)

                    if page_descriptions:
                        all_descriptions.extend(page_descriptions)
                        print(f"    ✅ 提取到 {len(page_descriptions)} 条描述")
                    else:
                        print(f"    ⚠️ 第 {page_num + 1} 页没有找到有效描述")

                print(f"  ✅ PyPDF总计提取 {len(all_descriptions)} 条完整描述")
                return all_descriptions

        except Exception as e:
            print(f"  ❌ PyPDF提取失败: {e}")
            return []

    def _parse_page_text(self, text: str, page_num: int) -> List[Dict]:
        """解析单页文本，提取交易描述"""
        descriptions = []
        lines = text.split('\n')

        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if not line:
                i += 1
                continue

            # 查找交易行模式：Tran Id + Tran Date + Remarks
            match = self._match_transaction_line(line)
            if match:
                # 检查下一行是否为当前交易的延续
                complete_remarks = match['remarks']
                j = i + 1

                # 查找可能的延续行
                while j < len(lines):
                    next_line = lines[j].strip()
                    if not next_line:
                        j += 1
                        continue

                    # 如果下一行是新的交易行，停止
                    if self._is_transaction_start_line(next_line):
                        break

                    # 如果下一行是表头或其他非交易内容，停止
                    if self._is_non_transaction_line(next_line):
                        break

                    # 如果下一行看起来是当前交易的延续，添加到描述中
                    if self._is_remarks_continuation(next_line, complete_remarks):
                        complete_remarks += " " + next_line
                        j += 1
                    else:
                        break

                # 更新描述并清理格式
                match['remarks'] = self._clean_remarks_text(complete_remarks)
                descriptions.append(match)
                i = j  # 跳过已处理的延续行
            else:
                i += 1

        return descriptions

    def _match_transaction_line(self, line: str) -> Optional[Dict]:
        """匹配交易行，提取Tran Id、日期和完整描述"""
        # Indian Bank交易行模式：Tran Id + 空格 + Tran Date + 空格 + Remarks + [金额信息]
        # 例如: "S52161910 20/02/2023 UPIAR/************/DR/MASTR 6400.00 (Dr) 786138.0"

        # 正则模式：Tran Id + 空格 + 日期(DD/MM/YYYY) + 空格 + 描述
        pattern = r'^((S|AA)\d+)\s+(\d{2}/\d{2}/\d{4})\s+(.+)$'
        match = re.match(pattern, line)

        if not match:
            return None

        tran_id = match.group(1)
        tran_date = match.group(3)
        rest_content = match.group(4)

        # 提取完整描述（移除末尾的金额部分）
        remarks = self._extract_remarks_from_content(rest_content)

        if remarks:
            return {
                'tran_id': tran_id,
                'tran_date': tran_date,
                'remarks': remarks
            }

        return None

    def _extract_remarks_from_content(self, content: str) -> str:
        """从内容中提取描述部分，移除金额"""
        if not content:
            return ""

        # 更保守的金额移除策略，避免截断描述文本
        # 找到所有金额模式的位置
        amount_pattern = r'\s+[\d,]+\.\d{2}\s*\([DCdr]+\)(?=\s|$)'  # Indian Bank金额格式: 数字.数字 (Dr/Cr)
        amounts = list(re.finditer(amount_pattern, content))

        if amounts:
            # 分析金额的位置和上下文
            valid_amounts = []
            for amount_match in amounts:
                amount_text = amount_match.group().strip()
                amount_start = amount_match.start()

                # 检查金额前后的上下文
                before_text = content[:amount_start].strip()
                after_text = content[amount_match.end():].strip()

                # 如果金额后面还有很多文本，可能不是真正的金额
                if len(after_text) > 20:  # 金额后面不应该有太多文本
                    continue

                # 如果金额看起来合理（包含Dr/Cr标识）
                if '(Dr)' in amount_text or '(Cr)' in amount_text:
                    valid_amounts.append(amount_match)

            # 只移除确认的金额
            if valid_amounts:
                # 移除最后一个有效金额及其后面的内容
                last_amount_start = valid_amounts[-1].start()
                content = content[:last_amount_start]

        # 清理换行符和多余的空格
        content = self._clean_remarks_text(content)

        # 如果描述太短，可能解析有误
        if len(content) < 3:
            return ""

        return content

    def _is_transaction_start_line(self, line: str) -> bool:
        """检查是否为交易开始行"""
        # Indian Bank交易行模式：Tran Id(S或AA开头) + 空格 + 日期
        pattern = r'^(S|AA)\d+\s+\d{2}/\d{2}/\d{4}'
        return bool(re.match(pattern, line))

    def _is_non_transaction_line(self, line: str) -> bool:
        """检查是否为非交易行（表头、页脚等）"""
        non_transaction_keywords = [
            'Tran Id', 'Tran Date', 'Remarks', 'Amount', 'Balance',
            'INDIAN BANK', 'Statement', 'Page', 'Total', 'Account Number',
            'DETAILS OF STATEMENT', 'Name :', 'Address :'
        ]

        line_upper = line.upper()
        for keyword in non_transaction_keywords:
            if keyword.upper() in line_upper:
                return True

        return False

    def _is_remarks_continuation(self, line: str, current_remarks: str) -> bool:
        """检查是否为描述的延续行"""
        line_stripped = line.strip()

        # 如果行中包含金额模式，可能不是纯描述延续
        if re.search(r'[\d,]+\.\d{2}\s*\([DCdr]+\)', line):
            return False

        # 如果行太短，可能不是有效的延续
        if len(line_stripped) < 1:
            return False

        # 如果行包含典型的交易描述内容，认为是延续
        description_indicators = [
            'UPI', 'NEFT', 'IMPS', 'ATM', 'RTGS', 'UPIAR', 'CASH', 'SALARY'
        ]

        line_upper = line_stripped.upper()
        for indicator in description_indicators:
            if indicator in line_upper:
                return True

        # 如果行看起来是名字或地址的一部分，认为是延续
        if re.match(r'^[A-Z\s/]+$', line_stripped):
            return True

        return False

    def _clean_remarks_text(self, text: str) -> str:
        """清理描述文本，处理换行符和格式问题"""
        if not text:
            return ""

        # 移除换行符，用空格替换
        text = re.sub(r'\n+', ' ', text)

        # 移除回车符
        text = re.sub(r'\r+', ' ', text)

        # 移除制表符
        text = re.sub(r'\t+', ' ', text)

        # 清理多余的空格
        text = re.sub(r'\s+', ' ', text)

        # 移除首尾空格
        text = text.strip()

        return text

    def _build_description_lookup(self, pypdf_data: List[Dict]) -> Dict[Tuple[str, str], str]:
        """构建描述查找字典，键为(tran_id, tran_date) - 避免IOB的日期匹配问题"""
        lookup = {}

        for item in pypdf_data:
            key = (item['tran_id'], item['tran_date'])
            lookup[key] = item['remarks']

        return lookup

    def _merge_and_fix_data(self, df_structured: pd.DataFrame, pypdf_data: List[Dict]) -> pd.DataFrame:
        """合并tabula和pypdf数据，修复Remarks列 - 使用精确匹配避免IOB问题"""
        if df_structured.empty or not pypdf_data:
            print("  ⚠️ 无法合并：缺少tabula或pypdf数据")
            return df_structured

        print(f"  🔗 合并数据：tabula {len(df_structured)} 行，pypdf {len(pypdf_data)} 条描述")

        # 构建描述查找字典
        description_lookup = self._build_description_lookup(pypdf_data)

        # 统计匹配情况
        matched_count = 0
        improved_count = 0

        # 遍历tabula数据，替换Remarks
        for idx, row in df_structured.iterrows():
            try:
                # 获取Tran Id和日期作为匹配键
                tran_id = str(row['Tran Id']).strip()
                tran_date = str(row['Tran Date']).strip()

                if not tran_id or not tran_date:
                    continue

                # 查找匹配的完整描述
                key = (tran_id, tran_date)
                if key in description_lookup:
                    old_remarks = str(row['Remarks']) if pd.notna(row['Remarks']) else ""
                    new_remarks = description_lookup[key]

                    # 只有当新描述更完整时才替换
                    if len(new_remarks) > len(old_remarks):
                        # 清理描述文本格式
                        cleaned_remarks = self._clean_remarks_text(new_remarks)
                        df_structured.at[idx, 'Remarks'] = cleaned_remarks
                        improved_count += 1

                    matched_count += 1

            except Exception as e:
                print(f"    ⚠️ 处理第 {idx} 行时出错: {e}")
                continue

        print(f"  ✅ 匹配成功: {matched_count}/{len(df_structured)} 行")
        print(f"  📈 描述改进: {improved_count} 行")

        return df_structured

    def _validate_and_clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清理最终数据"""
        if df.empty:
            return df

        print("  🧹 清洗数据...")

        # 清洗日期列
        df = self._clean_date_column(df)

        # 清洗金额列
        df = self._clean_amount_columns(df)

        # 清洗描述列
        df = self._clean_remarks_column(df)

        # 验证数据完整性
        df = self._validate_data_integrity(df)

        return df

    def _clean_date_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗日期列"""
        if 'Tran Date' in df.columns:
            print("    📅 清洗日期列...")
            df['Tran Date'] = df['Tran Date'].apply(self._parse_date)

        return df

    def _parse_date(self, value) -> str:
        """解析日期字符串"""
        if pd.isna(value):
            return ""

        date_str = str(value).strip()

        # Indian Bank日期格式: DD/MM/YYYY
        if re.match(r'\d{2}/\d{2}/\d{4}', date_str):
            return date_str

        # 尝试其他常见格式
        try:
            for fmt in ['%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime('%d/%m/%Y')  # 统一为Indian Bank格式
                except ValueError:
                    continue
        except:
            pass

        return date_str  # 如果无法解析，返回原值

    def _clean_amount_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗金额列数据"""
        # Amount列保留原始格式（包含Dr/Cr）
        if 'Amount (Rs.)' in df.columns:
            print(f"    💰 清洗 Amount (Rs.) 列...")
            df['Amount (Rs.)'] = df['Amount (Rs.)'].apply(self._parse_amount)

        # Balance列转换为数字格式
        if 'Balance (Rs.)' in df.columns:
            print(f"    💰 清洗 Balance (Rs.) 列...")
            df['Balance (Rs.)'] = df['Balance (Rs.)'].apply(self._parse_balance)

        return df

    def _parse_balance(self, value) -> Optional[float]:
        """解析余额为浮点数"""
        if pd.isna(value) or value == '' or str(value).strip() == '':
            return None

        try:
            # 转换为字符串并清理
            balance_str = str(value).strip()

            # 移除货币符号和空格
            balance_str = re.sub(r'[₹\s,]', '', balance_str)

            # 只保留数字和小数点
            balance_str = re.sub(r'[^\d.-]', '', balance_str)

            if not balance_str or balance_str == '-':
                return None

            # 转换为浮点数
            return float(balance_str)

        except (ValueError, TypeError):
            return None

    def _parse_amount(self, value) -> Optional[str]:
        """保留金额的原始格式，包含Dr/Cr标识"""
        if pd.isna(value) or value == '' or str(value).strip() == '':
            return None

        try:
            # 转换为字符串并清理多余空格
            amount_str = str(value).strip()

            # 移除货币符号但保留Dr/Cr标识
            amount_str = re.sub(r'[₹]', '', amount_str)

            # 清理多余的空格但保持格式
            amount_str = re.sub(r'\s+', ' ', amount_str).strip()

            # 如果包含Dr/Cr标识，保持原格式
            if '(Dr)' in amount_str or '(Cr)' in amount_str or '(DR)' in amount_str or '(CR)' in amount_str:
                return amount_str

            # 如果没有Dr/Cr标识但是数字，尝试保持数字格式
            clean_for_test = re.sub(r'[^\d.-]', '', amount_str)
            if clean_for_test and clean_for_test != '-':
                try:
                    float(clean_for_test)
                    return amount_str  # 返回原始格式
                except:
                    pass

            return amount_str if amount_str else None

        except (ValueError, TypeError):
            return None

    def _clean_remarks_column(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗Remarks列"""
        if 'Remarks' in df.columns:
            print("    📝 清洗Remarks列...")
            df['Remarks'] = df['Remarks'].apply(self._clean_remarks_text)

        return df

    def _validate_data_integrity(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证数据完整性"""
        print("    ✅ 验证数据完整性...")

        # 移除没有Tran Id的行
        before_count = len(df)
        df = df[df['Tran Id'].notna() & (df['Tran Id'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效Tran Id的记录")

        # 移除没有日期的行
        before_count = len(df)
        df = df[df['Tran Date'].notna() & (df['Tran Date'] != '')]
        after_count = len(df)

        if before_count != after_count:
            print(f"    🗑️ 移除 {before_count - after_count} 行无效日期的记录")

        # 确保至少有金额或余额
        df = df[
            df['Amount (Rs.)'].notna() | df['Balance (Rs.)'].notna()
        ]

        # 重置索引
        df.reset_index(drop=True, inplace=True)

        return df

    def save_results(self, df: pd.DataFrame, output_base: str = "indian_extracted") -> Tuple[str, str, str]:
        """
        保存解析结果为多种格式

        Args:
            df: 解析后的DataFrame
            output_base: 输出文件名前缀

        Returns:
            Tuple[str, str, str]: CSV、JSON、Excel文件路径
        """
        if df.empty:
            print("❌ 没有数据可保存")
            return "", "", ""

        # 在保存前进行最终的文本清理
        df_clean = df.copy()
        if 'Remarks' in df_clean.columns:
            df_clean['Remarks'] = df_clean['Remarks'].apply(
                lambda x: self._clean_remarks_text(str(x)) if pd.notna(x) else ""
            )

        # 保存为CSV
        csv_file = f"{output_base}.csv"
        df_clean.to_csv(csv_file, index=False, encoding='utf-8')

        # 保存为JSON
        json_file = f"{output_base}.json"
        df_clean.to_json(json_file, orient='records', indent=2, force_ascii=False)

        # 保存为Excel
        excel_file = f"{output_base}.xlsx"
        df_clean.to_excel(excel_file, index=False, engine='openpyxl')

        print(f"\n📁 文件已保存:")
        print(f"  - CSV: {csv_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Excel: {excel_file}")

        return csv_file, json_file, excel_file

    def generate_validation_report(self, df: pd.DataFrame) -> None:
        """生成验证报告"""
        if df.empty:
            print("❌ 没有数据可验证")
            return

        print(f"\n📊 Indian Bank银行数据验证报告")
        print(f"{'='*50}")

        # 基本统计
        print(f"总交易数: {len(df)}")

        # 交易类型统计（基于金额的Dr/Cr）
        debit_count = 0
        credit_count = 0
        debit_total = 0
        credit_total = 0

        if 'Amount (Rs.)' in df.columns:
            for _, row in df.iterrows():
                amount_str = str(row['Amount (Rs.)']) if pd.notna(row['Amount (Rs.)']) else ""
                if '(Dr)' in amount_str or '(DR)' in amount_str:
                    debit_count += 1
                    try:
                        # 提取数字部分
                        amount_val = float(re.sub(r'[^\d.]', '', amount_str.split('(')[0]))
                        debit_total += amount_val
                    except:
                        pass
                elif '(Cr)' in amount_str or '(CR)' in amount_str:
                    credit_count += 1
                    try:
                        # 提取数字部分
                        amount_val = float(re.sub(r'[^\d.]', '', amount_str.split('(')[0]))
                        credit_total += amount_val
                    except:
                        pass

        print(f"\n💸 借记统计:")
        print(f"  借记交易: {debit_count} 笔")
        print(f"  借记总额: ₹{debit_total:,.2f}")

        print(f"\n💰 贷记统计:")
        print(f"  贷记交易: {credit_count} 笔")
        print(f"  贷记总额: ₹{credit_total:,.2f}")

        print(f"\n📊 净变化: ₹{credit_total - debit_total:,.2f}")

        # 数据完整性检查
        missing_tran_ids = df['Tran Id'].isna().sum() if 'Tran Id' in df.columns else 0
        missing_dates = df['Tran Date'].isna().sum() if 'Tran Date' in df.columns else 0
        missing_remarks = df['Remarks'].isna().sum() if 'Remarks' in df.columns else 0
        missing_amounts = df['Amount (Rs.)'].isna().sum() if 'Amount (Rs.)' in df.columns else 0
        missing_balances = df['Balance (Rs.)'].isna().sum() if 'Balance (Rs.)' in df.columns else 0

        print(f"\n📋 数据完整性:")
        print(f"  缺失交易ID: {missing_tran_ids} 条")
        print(f"  缺失日期: {missing_dates} 条")
        print(f"  缺失描述: {missing_remarks} 条")
        print(f"  缺失金额: {missing_amounts} 条")
        print(f"  缺失余额: {missing_balances} 条")

        # 余额连续性检查
        self._check_balance_continuity(df)

        # 日期范围
        if 'Tran Date' in df.columns and not df['Tran Date'].isna().all():
            valid_dates = df[df['Tran Date'].notna() & (df['Tran Date'] != '')]
            if not valid_dates.empty:
                print(f"\n📅 日期范围:")
                print(f"  最早交易: {valid_dates['Tran Date'].iloc[0]}")
                print(f"  最晚交易: {valid_dates['Tran Date'].iloc[-1]}")

        # 显示样本数据
        print(f"\n📝 前3条记录样本:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            amount = str(row['Amount (Rs.)']) if pd.notna(row['Amount (Rs.)']) else "-"
            balance = f"₹{row['Balance (Rs.)']:,.2f}" if pd.notna(row['Balance (Rs.)']) else "-"

            print(f"  {i+1}. {row['Tran Id']} | {row['Tran Date']} | {str(row['Remarks'])[:30]}...")
            print(f"     金额: {amount} | 余额: {balance}")

    def _check_balance_continuity(self, df: pd.DataFrame) -> None:
        """检查余额连续性"""
        print(f"\n🔍 余额连续性检查:")

        if 'Balance (Rs.)' not in df.columns:
            print("  ⚠️ 没有余额列")
            return

        # 检查余额是否连续
        valid_balances = df[df['Balance (Rs.)'].notna()]

        if len(valid_balances) < 2:
            print("  ⚠️ 余额数据不足，无法检查连续性")
            return

        # 简单检查：余额应该是有变化的
        balance_changes = 0
        for i in range(1, len(valid_balances)):
            prev_balance = valid_balances.iloc[i-1]['Balance (Rs.)']
            curr_balance = valid_balances.iloc[i]['Balance (Rs.)']

            if abs(curr_balance - prev_balance) > 0.01:  # 忽略小数点误差
                balance_changes += 1

        print(f"  余额变化次数: {balance_changes}")

        # 检查首末余额
        first_balance = valid_balances.iloc[0]['Balance (Rs.)']
        last_balance = valid_balances.iloc[-1]['Balance (Rs.)']

        print(f"  期初余额: ₹{first_balance:,.2f}")
        print(f"  期末余额: ₹{last_balance:,.2f}")
        print(f"  余额变化: ₹{last_balance - first_balance:,.2f}")


def main():
    """主函数"""
    parser = IndianBankPDFParser()

    pdf_path = "../files/16-indian-*********-SOORAJ-INDIAN-BANK-STATEMENT.pdf.crdownload.pdf"

    print("🚀 启动Indian Bank银行PDF混合解析器")

    # 解析PDF
    df = parser.parse_indian_pdf(pdf_path)

    if not df.empty:
        # 保存结果
        parser.save_results(df)

        # 生成验证报告
        parser.generate_validation_report(df)

        print(f"\n🎉 Indian Bank银行PDF解析完成！")
        return df
    else:
        print(f"\n❌ Indian Bank银行PDF解析失败")
        return None


if __name__ == "__main__":
    main()
