#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Indian Bank PDF解析器中的数据质量问题
"""

import tabula
import pandas as pd
import pypdf

def debug_data_quality():
    """调试数据质量问题"""
    pdf_path = '../files/16-indian-*********-SOORAJ-INDIAN-BANK-STATEMENT.pdf.crdownload.pdf'
    
    print('=' * 80)
    print('调试Indian Bank PDF数据质量问题')
    print('=' * 80)
    
    # 1. 查看原始tabula数据
    print('\n🔍 1. 原始Tabula数据分析:')
    try:
        dfs = tabula.read_pdf(pdf_path, pages='1', stream=True)
        if dfs:
            df = dfs[0]
            print(f'   列名: {list(df.columns)}')
            print('   前10行原始数据:')
            for i in range(min(10, len(df))):
                row = df.iloc[i]
                tran_id = row.iloc[0]
                tran_date = row.iloc[1]
                remarks = str(row.iloc[2])
                amount = str(row.iloc[3])
                balance = row.iloc[4]
                print(f'   {i+1:2d}. {tran_id} | {tran_date} | {remarks[:50]} | {amount} | {balance}')
    except Exception as e:
        print(f'   ❌ Tabula分析失败: {e}')
    
    # 2. 查看PyPDF提取的数据
    print('\n🔍 2. PyPDF文本提取分析:')
    try:
        with open(pdf_path, 'rb') as file:
            reader = pypdf.PdfReader(file)
            page = reader.pages[0]
            text = page.extract_text()
            
            lines = text.split('\n')
            print('   查找交易行:')
            count = 0
            for i, line in enumerate(lines):
                line = line.strip()
                if line and ('S52161910' in line or 'S48966680' in line or 'S41139321' in line):
                    print(f'   {count+1:2d}. 第{i}行: {line}')
                    count += 1
                    if count >= 5:
                        break
                        
    except Exception as e:
        print(f'   ❌ PyPDF分析失败: {e}')
    
    # 3. 分析问题
    print('\n🔍 3. 问题分析:')
    print('   问题1 - Remarks列不完整:')
    print('     当前: UPIAB/211521058791/CR/AVINA')
    print('     应该: UPIAB/211521058791/CR/AVINASH /HDFC/avinashkaranda (完整描述)')
    print('   ')
    print('   问题2 - Amount格式丢失:')
    print('     当前: 6400.0')
    print('     应该: 6400.00 (Dr)')
    print('   ')
    print('   问题3 - 可能的原因:')
    print('     - PyPDF提取时金额移除逻辑过于激进')
    print('     - Tabula本身就没有完整的描述')
    print('     - 金额格式在数据清理时被错误处理')

if __name__ == "__main__":
    debug_data_quality()
