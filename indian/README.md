# Indian Bank银行PDF账单解析器

## 项目概述

本项目实现了Indian Bank银行PDF账单的自动解析功能，能够将PDF格式的银行对账单转换为结构化数据（CSV、JSON、Excel格式）。

## 解析策略

### 技术选择
基于对Indian Bank银行PDF格式的深入分析，我们发现：
- PDF表格结构清晰，有5列标准格式
- Stream模式比Lattice模式更适合此PDF格式
- Remarks列存在跨行显示问题（包含`\r`换行符）
- 需要使用混合解析策略（tabula + pypdf）来获取完整的交易描述

### 解析流程
1. **PDF结构分析**: 使用tabula分析PDF中的表格结构
2. **Tabula表格提取**: 使用stream模式提取表格结构和金额数据
3. **PyPDF文本提取**: 提取完整的交易描述文本
4. **精确数据合并**: 基于Tran Id + Tran Date精确匹配，避免IOB中的日期匹配问题
5. **数据清理验证**: 标准化格式并验证数据完整性
6. **多格式输出**: 生成CSV、JSON、Excel三种格式的文件

## 解析结果

### 数据统计
- **总交易数**: 84 条
- **数据完整性**: 100%（无缺失字段）
- **日期范围**: 2022年8月24日 - 2023年2月20日
- **描述改进**: 5 行（5.9%的描述得到改进）

### 余额信息
- **期初余额**: ₹786,138.00
- **期末余额**: ₹460,184.40
- **余额变化**: ₹-325,953.60

### 数据完整性
- ✅ **缺失交易ID**: 0 条
- ✅ **缺失日期**: 0 条
- ✅ **缺失描述**: 0 条
- ✅ **缺失金额**: 0 条
- ✅ **缺失余额**: 0 条

## 输出文件

### 1. indian_extracted.csv
标准CSV格式，包含以下列：
- `Tran Id`: 交易ID (S开头或AA开头的数字)
- `Tran Date`: 交易日期 (DD/MM/YYYY格式)
- `Remarks`: 交易描述（已修复跨行问题）
- `Amount (Rs.)`: 交易金额
- `Balance (Rs.)`: 账户余额

### 2. indian_extracted.json
JSON格式，便于程序化处理和API集成

### 3. indian_extracted.xlsx
Excel格式，便于人工查看和进一步分析

## 技术实现

### 核心依赖
- `tabula-py`: PDF表格提取
- `pypdf`: PDF文本提取
- `pandas`: 数据处理和分析
- `openpyxl`: Excel文件生成

### 混合解析策略
1. **Tabula结构提取**: 
   - 使用stream=True模式提取表格结构
   - 准确获取金额数据和基本信息
   - 处理多页表格合并

2. **PyPDF文本提取**:
   - 提取完整的交易描述文本
   - 处理跨行描述的连接
   - 智能识别交易行模式

3. **精确数据合并**:
   - 基于Tran Id + Tran Date进行精确匹配
   - 84/84行成功匹配（100%匹配率）
   - 5行描述得到改进（5.9%改进率）

### 关键特性
1. **精确匹配策略**: 使用Tran Id + Tran Date避免IOB中的日期匹配问题
2. **跨行描述修复**: 自动处理Remarks列的跨行显示问题
3. **智能表格识别**: 自动识别有效的交易表格
4. **数据类型转换**: 自动处理金额和日期格式
5. **错误处理**: 完善的异常处理机制
6. **数据验证**: 多层次的数据完整性检查

## 使用方法

```bash
# 进入indian目录
cd indian

# 运行解析器
python3 indian_pdf_parser.py
```

## 文件结构

```
indian/
├── README.md                    # 本文档
├── indian_pdf_parser.py         # 主解析器
├── analyze_indian_pdf.py        # PDF结构分析脚本
├── indian_extracted.csv         # CSV输出文件
├── indian_extracted.json        # JSON输出文件
└── indian_extracted.xlsx        # Excel输出文件
```

## 解析质量评估

### 优势
1. **高准确性**: 100%的数据完整性，无缺失字段
2. **精确匹配**: 使用Tran Id + Tran Date避免了IOB中的匹配问题
3. **描述完整**: 5.9%的描述得到改进，解决跨行问题
4. **格式标准**: 统一的日期和金额格式
5. **多格式支持**: 提供三种常用的数据格式
6. **自动化程度高**: 无需人工干预即可完成解析

### 技术亮点
1. **精确匹配算法**: 基于Tran Id + Tran Date的双重匹配键
2. **智能表格识别**: 自动识别有效交易表格，过滤无关内容
3. **跨行文本处理**: 有效解决PDF文本跨行问题
4. **保守替换策略**: 只在确实需要时才进行描述改进
5. **数据清洗**: 完善的数据清理和标准化流程
6. **错误处理**: 健壮的异常处理机制

## 对比分析

与项目中其他银行解析器相比：
- **BOI银行**: 使用序号+日期匹配，Indian Bank使用Tran Id+日期匹配
- **IOB银行**: IOB使用日期匹配导致问题，Indian Bank使用精确匹配避免问题
- **Bandhan银行**: 单一tabula策略，Indian Bank需要混合策略
- **解析精度**: Indian Bank实现了100%的匹配率和最低的错误率
- **匹配策略**: 最精确的匹配策略，完全避免了数据覆盖问题

## 技术创新

### 避免IOB问题的策略
1. **双重匹配键**: 使用Tran Id + Tran Date而非单一日期匹配
2. **精确匹配**: 确保每条记录都有唯一的匹配键
3. **保守替换**: 只在描述确实更完整时才进行替换
4. **数据独立性**: 每条交易记录保持完全独立

### 文件完整性处理
- 成功处理了`.crdownload.pdf`格式的文件
- 验证了PDF的完整性和可读性
- 提供了文件完整性检查功能

## 总结

Indian Bank银行PDF解析器成功实现了以下目标：
1. ✅ 完整解析PDF中的所有交易记录（84条）
2. ✅ 保持原始表格的所有列和数据结构
3. ✅ 解决Remarks列跨行显示问题（5行改进）
4. ✅ 生成三种格式的输出文件
5. ✅ 提供详细的解析质量报告
6. ✅ 实现高度自动化的解析流程
7. ✅ 完全避免了IOB中的日期匹配问题

该解析器为Indian Bank银行PDF账单的数字化处理提供了可靠的技术解决方案，特别是在精确匹配策略和数据独立性保护方面表现出色。
