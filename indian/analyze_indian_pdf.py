#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Indian Bank银行PDF结构分析脚本
"""

import tabula
import pandas as pd
import pypdf
import os

def analyze_indian_pdf():
    """分析Indian Bank银行PDF的结构"""
    pdf_path = '../files/16-indian-*********-SOORAJ-INDIAN-BANK-STATEMENT.pdf.crdownload.pdf'
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return
    
    print('=' * 80)
    print('Indian Bank银行PDF结构分析')
    print('=' * 80)
    
    # 1. 使用tabula lattice模式分析
    print('\n🔍 1. Tabula Lattice模式分析:')
    try:
        dfs = tabula.read_pdf(pdf_path, pages='all', lattice=True)
        print(f'   找到 {len(dfs)} 个表格')
        
        for i, df in enumerate(dfs):
            print(f'\n   表格 {i+1}: 形状 {df.shape}')
            print(f'   列名: {list(df.columns)}')
            if not df.empty:
                print('   前5行数据:')
                print(df.head(5).to_string(index=False))
                print('   ---')
    except Exception as e:
        print(f'   ❌ Lattice模式解析出错: {e}')
    
    # 2. 使用tabula stream模式分析
    print('\n🔍 2. Tabula Stream模式分析:')
    try:
        dfs_stream = tabula.read_pdf(pdf_path, pages='all', stream=True)
        print(f'   找到 {len(dfs_stream)} 个表格')
        
        for i, df in enumerate(dfs_stream):
            print(f'\n   表格 {i+1}: 形状 {df.shape}')
            print(f'   列名: {list(df.columns)}')
            if not df.empty:
                print('   前5行数据:')
                print(df.head(5).to_string(index=False))
                print('   ---')
    except Exception as e:
        print(f'   ❌ Stream模式解析出错: {e}')
    
    # 3. 使用pypdf分析文本内容
    print('\n🔍 3. PyPDF文本内容分析:')
    try:
        with open(pdf_path, 'rb') as file:
            reader = pypdf.PdfReader(file)
            print(f'   PDF总页数: {len(reader.pages)}')
            
            for page_num in range(min(2, len(reader.pages))):  # 只分析前2页
                page = reader.pages[page_num]
                text = page.extract_text()
                
                print(f'\n   第 {page_num + 1} 页文本内容 (前500字符):')
                print('   ' + '-' * 50)
                print('   ' + text[:500].replace('\n', '\n   '))
                print('   ' + '-' * 50)
                
                # 查找可能的表格标题行
                lines = text.split('\n')
                for i, line in enumerate(lines):
                    if any(keyword in line.upper() for keyword in ['DATE', 'DESCRIPTION', 'DEBIT', 'CREDIT', 'BALANCE', 'PARTICULARS', 'TRANSACTION']):
                        print(f'   可能的表头行 {i}: {line.strip()}')
                        
    except Exception as e:
        print(f'   ❌ PyPDF分析出错: {e}')
    
    # 4. 尝试不同的tabula参数组合
    print('\n🔍 4. 尝试不同的Tabula参数组合:')
    
    # 参数组合列表
    param_combinations = [
        {'lattice': True, 'pages': 'all'},
        {'stream': True, 'pages': 'all'},
        {'lattice': True, 'pages': '1'},
        {'stream': True, 'pages': '1'},
        {'lattice': True, 'pages': 'all', 'multiple_tables': True},
        {'stream': True, 'pages': 'all', 'multiple_tables': True},
        {'lattice': True, 'pages': 'all', 'pandas_options': {'header': None}},
        {'stream': True, 'pages': 'all', 'pandas_options': {'header': None}},
    ]
    
    for i, params in enumerate(param_combinations):
        try:
            print(f'\n   组合 {i+1}: {params}')
            dfs = tabula.read_pdf(pdf_path, **params)
            print(f'   结果: 找到 {len(dfs)} 个表格')
            
            if dfs and not dfs[0].empty:
                df = dfs[0]
                print(f'   第一个表格形状: {df.shape}')
                print(f'   列名: {list(df.columns)}')
                
                # 检查数据质量
                non_empty_rows = df.dropna(how='all')
                print(f'   非空行数: {len(non_empty_rows)}')
                
                # 检查是否有日期模式
                date_patterns = 0
                for col in df.columns:
                    col_data = df[col].astype(str)
                    for val in col_data.head(10):
                        if any(pattern in val for pattern in ['/', '-']) and any(c.isdigit() for c in val):
                            date_patterns += 1
                            break
                print(f'   可能的日期列数: {date_patterns}')
                
        except Exception as e:
            print(f'   ❌ 组合 {i+1} 失败: {e}')
    
    # 5. 分析特定页面
    print('\n🔍 5. 分析特定页面:')
    for page in [1, 2]:
        try:
            print(f'\n   分析第 {page} 页:')
            dfs_page = tabula.read_pdf(pdf_path, pages=str(page), lattice=True)
            print(f'   找到 {len(dfs_page)} 个表格')
            
            for i, df in enumerate(dfs_page):
                if not df.empty:
                    print(f'   表格 {i+1}: {df.shape}')
                    print(f'   列名: {list(df.columns)}')
                    
                    # 显示前几行数据
                    print('   前3行数据:')
                    print(df.head(3).to_string(index=False))
                    
        except Exception as e:
            print(f'   ❌ 第 {page} 页分析失败: {e}')
    
    # 6. 检查文件完整性
    print('\n🔍 6. 文件完整性检查:')
    try:
        file_size = os.path.getsize(pdf_path)
        print(f'   文件大小: {file_size:,} 字节')
        
        # 检查文件扩展名
        if pdf_path.endswith('.crdownload.pdf'):
            print('   ⚠️ 文件名包含.crdownload，可能是未完成的下载文件')
        
        # 尝试读取PDF基本信息
        with open(pdf_path, 'rb') as file:
            reader = pypdf.PdfReader(file)
            print(f'   PDF页数: {len(reader.pages)}')
            if reader.metadata:
                print(f'   PDF创建者: {reader.metadata.get("/Creator", "未知")}')
                print(f'   PDF制作者: {reader.metadata.get("/Producer", "未知")}')
                
    except Exception as e:
        print(f'   ❌ 文件完整性检查失败: {e}')
    
    print('\n✅ Indian Bank银行PDF结构分析完成!')

if __name__ == "__main__":
    analyze_indian_pdf()
